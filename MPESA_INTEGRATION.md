# M-Pesa Payment Integration for RentalX

This document provides comprehensive instructions for integrating M-Pesa payments into your RentalX application.

## Overview

The M-Pesa integration allows tenants to pay rent directly from their mobile phones using Safaricom's M-Pesa service. The system supports:

- STK Push payments (customer receives payment prompt on their phone)
- Real-time payment status tracking
- Automatic invoice updates upon successful payment
- Multi-tenant support (each organization can have its own M-Pesa configuration)
- Comprehensive payment history and reporting
- Secure API key management

## Features

### For Property Managers
- Configure M-Pesa settings per organization
- View all M-Pesa transactions and payment history
- Automatic invoice payment reconciliation
- Real-time payment status monitoring

### For Tenants
- Pay rent using M-Pesa from any device
- Receive instant payment confirmations
- View payment history
- Quick payment options for outstanding invoices

## Installation & Setup

### 1. Database Migration

Run the following commands to create the necessary database tables:

```bash
python manage.py makemigrations payments
python manage.py makemigrations rentals
python manage.py migrate
```

### 2. M-Pesa API Setup

#### Get M-Pesa API Credentials

1. Visit the [Safaricom Developer Portal](https://developer.safaricom.co.ke/)
2. Create an account and log in
3. Create a new app to get your Consumer Key and Consumer Secret
4. Note your Business Short Code (Paybill or Till number)
5. Get your Passkey from the app configuration

#### Configure M-Pesa for Your Organization

Use the management command to set up M-Pesa configuration:

```bash
# For sandbox/testing environment
python manage.py setup_mpesa your-org-slug \
    --consumer-key "your_consumer_key" \
    --consumer-secret "your_consumer_secret" \
    --business-shortcode "174379" \
    --passkey "your_passkey" \
    --callback-url "https://yourdomain.com/payments/callback/" \
    --sandbox

# For production environment (remove --sandbox flag)
python manage.py setup_mpesa your-org-slug \
    --consumer-key "your_consumer_key" \
    --consumer-secret "your_consumer_secret" \
    --business-shortcode "your_shortcode" \
    --passkey "your_passkey" \
    --callback-url "https://yourdomain.com/payments/callback/"
```

### 3. Environment Variables (Production)

For production, set these environment variables instead of using the management command:

```bash
export MPESA_ENVIRONMENT=production
export MPESA_CONSUMER_KEY=your_consumer_key
export MPESA_CONSUMER_SECRET=your_consumer_secret
export MPESA_BUSINESS_SHORTCODE=your_shortcode
export MPESA_PASSKEY=your_passkey
export MPESA_CALLBACK_URL=https://yourdomain.com/payments/callback/
```

### 4. Callback URL Configuration

Ensure your callback URL is:
- Publicly accessible (not localhost)
- Uses HTTPS (required by Safaricom)
- Points to: `https://yourdomain.com/payments/callback/`

For local development, use tools like ngrok:
```bash
ngrok http 8000
# Use the HTTPS URL provided by ngrok
```

## Usage Examples

### 1. Initiating a Payment (View)

```python
# In your template or view
<a href="{% org_url 'initiate_payment' invoice_id=invoice.id %}" 
   class="btn btn-success">
    Pay with M-Pesa
</a>
```

### 2. Quick Payment Form

```html
<form method="post" action="{% org_url 'initiate_payment' invoice_id=invoice.id %}">
    {% csrf_token %}
    <input type="tel" name="phone_number" placeholder="0712345678" required>
    <input type="number" name="amount" value="{{ invoice.balance }}" step="0.01" required>
    <button type="submit">Pay Now</button>
</form>
```

### 3. Checking Payment Status (JavaScript)

```javascript
function checkPaymentStatus(transactionId) {
    fetch(`/${orgSlug}/payments/transaction/${transactionId}/check/`)
        .then(response => response.json())
        .then(data => {
            if (data.is_successful) {
                alert('Payment successful!');
                location.reload();
            } else if (data.is_failed) {
                alert('Payment failed: ' + data.result_desc);
            }
        });
}
```

### 4. Custom Payment Processing

```python
from payments.services import MpesaService, MpesaAPIException

def process_custom_payment(request, invoice_id):
    invoice = get_object_or_404(Invoice, id=invoice_id, organization=request.org)
    
    try:
        mpesa_service = MpesaService(request.org)
        transaction, response = mpesa_service.initiate_stk_push(
            phone_number='************',
            amount=invoice.balance,
            invoice=invoice,
            account_reference=f'RENT-{invoice.number}'
        )
        
        return JsonResponse({
            'success': True,
            'transaction_id': transaction.id,
            'checkout_request_id': transaction.checkout_request_id
        })
        
    except MpesaAPIException as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })
```

## API Endpoints

### Payment Initiation
- **URL**: `/{org_slug}/payments/invoice/{invoice_id}/pay/`
- **Method**: GET, POST
- **Description**: Display payment form and process payment initiation

### Payment Status
- **URL**: `/{org_slug}/payments/transaction/{transaction_id}/status/`
- **Method**: GET
- **Description**: Display payment status page

### Check Payment Status (AJAX)
- **URL**: `/{org_slug}/payments/transaction/{transaction_id}/check/`
- **Method**: GET
- **Description**: Return JSON payment status

### M-Pesa Callback
- **URL**: `/payments/callback/`
- **Method**: POST
- **Description**: Receive M-Pesa payment notifications

### Payment History
- **URL**: `/{org_slug}/payments/history/`
- **Method**: GET
- **Description**: Display M-Pesa payment history

## Security Best Practices

### 1. API Key Security
- Never commit API keys to version control
- Use environment variables in production
- Rotate keys regularly
- Use different keys for sandbox and production

### 2. Callback Security
- Validate callback data structure
- Log all callback requests
- Implement idempotency checks
- Use HTTPS for all callback URLs

### 3. Transaction Security
- Validate payment amounts
- Check transaction uniqueness
- Implement timeout handling
- Store transaction logs

### 4. Error Handling
```python
try:
    mpesa_service = MpesaService(organization)
    transaction, response = mpesa_service.initiate_stk_push(...)
except MpesaAPIException as e:
    logger.error(f"M-Pesa API error: {e}")
    messages.error(request, "Payment service temporarily unavailable")
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    messages.error(request, "An error occurred. Please try again.")
```

## Testing

### 1. Sandbox Testing
Use these test credentials for sandbox:
- **Consumer Key**: Get from Daraja portal
- **Consumer Secret**: Get from Daraja portal  
- **Business Short Code**: 174379 (test shortcode)
- **Test Phone Numbers**: 254708374149, 254711111111

### 2. Test Scenarios
```python
# Test successful payment
phone = '254708374149'  # Always succeeds in sandbox

# Test failed payment
phone = '254711111111'  # Always fails in sandbox

# Test timeout
phone = '254700000000'  # Times out in sandbox
```

## Troubleshooting

### Common Issues

1. **"Invalid Access Token"**
   - Check consumer key and secret
   - Ensure credentials match environment (sandbox/production)

2. **"Invalid Phone Number"**
   - Phone must be in format 254XXXXXXXXX
   - Must be registered with M-Pesa

3. **"Callback Not Received"**
   - Check callback URL is publicly accessible
   - Ensure HTTPS is used
   - Check firewall settings

4. **"Transaction Not Found"**
   - Verify checkout request ID
   - Check database for transaction record

### Logging
Check logs for detailed error information:
```bash
tail -f logs/mpesa.log
```

## Production Deployment

### 1. Environment Setup
```bash
# Set production environment variables
export DJANGO_SETTINGS_MODULE=rentalx.settings.production
export MPESA_ENVIRONMENT=production
export MPESA_CONSUMER_KEY=prod_consumer_key
export MPESA_CONSUMER_SECRET=prod_consumer_secret
export MPESA_BUSINESS_SHORTCODE=your_paybill
export MPESA_PASSKEY=prod_passkey
export MPESA_CALLBACK_URL=https://yourdomain.com/payments/callback/
```

### 2. SSL Certificate
Ensure your domain has a valid SSL certificate for HTTPS callbacks.

### 3. Monitoring
Set up monitoring for:
- Payment success rates
- API response times
- Failed transactions
- Callback delivery

## Support

For issues related to:
- **M-Pesa API**: Contact Safaricom Developer Support
- **Integration Code**: Check the GitHub repository
- **Production Issues**: Review logs and error messages

## License

This M-Pesa integration is part of the RentalX project and follows the same license terms.
