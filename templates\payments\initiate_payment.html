{% extends 'base.html' %}
{% load org_urls %}

{% block title %}Pay Invoice - {{ invoice.number }}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Pay Invoice {{ invoice.number }}</h4>
                        </div>
                        <div class="card-body">
                            <!-- Invoice Details -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Invoice Details</h6>
                                            <p><strong>Invoice Number:</strong> {{ invoice.number }}</p>
                                            <p><strong>Property:</strong> {{ invoice.lease.unit.property.name }}</p>
                                            <p><strong>Unit:</strong> {{ invoice.lease.unit.code }}</p>
                                            <p><strong>Tenant:</strong> {{ invoice.lease.tenant.first_name }} {{ invoice.lease.tenant.last_name }}</p>
                                            <p><strong>Due Date:</strong> {{ invoice.due_date|date:"M d, Y" }}</p>
                                            <p><strong>Total Amount:</strong> <span class="text-primary">KES {{ invoice.amount_due|floatformat:2 }}</span></p>
                                            <p><strong>Amount Paid:</strong> <span class="text-success">KES {{ invoice.amount_paid|floatformat:2 }}</span></p>
                                            <p><strong>Balance:</strong> <span class="text-danger">KES {{ invoice.balance|floatformat:2 }}</span></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-mobile-alt"></i> M-Pesa Payment
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <form method="post">
                                                {% csrf_token %}
                                                
                                                <div class="form-group mb-3">
                                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                                        <i class="fas fa-phone"></i> Phone Number
                                                    </label>
                                                    {{ form.phone_number }}
                                                    {% if form.phone_number.errors %}
                                                        <div class="text-danger small">
                                                            {{ form.phone_number.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                    <small class="form-text text-muted">{{ form.phone_number.help_text }}</small>
                                                </div>
                                                
                                                <div class="form-group mb-3">
                                                    <label for="{{ form.amount.id_for_label }}" class="form-label">
                                                        <i class="fas fa-money-bill"></i> Amount (KES)
                                                    </label>
                                                    {{ form.amount }}
                                                    {% if form.amount.errors %}
                                                        <div class="text-danger small">
                                                            {{ form.amount.errors.0 }}
                                                        </div>
                                                    {% endif %}
                                                    <small class="form-text text-muted">{{ form.amount.help_text }}</small>
                                                </div>
                                                
                                                <div class="d-grid gap-2">
                                                    <button type="submit" class="btn btn-primary btn-lg">
                                                        <i class="fas fa-credit-card"></i> Pay with M-Pesa
                                                    </button>
                                                    <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">
                                                        <i class="fas fa-arrow-left"></i> Back to Invoices
                                                    </a>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Payment Instructions -->
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Payment Instructions</h6>
                                <ol class="mb-0">
                                    <li>Enter your M-Pesa registered phone number</li>
                                    <li>Enter the amount you want to pay</li>
                                    <li>Click "Pay with M-Pesa" button</li>
                                    <li>You will receive an STK Push notification on your phone</li>
                                    <li>Enter your M-Pesa PIN to complete the payment</li>
                                    <li>You will receive a confirmation SMS from M-Pesa</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
}

.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}
</style>
{% endblock %}
