from django.http import Http404
from .models import Organization


class OrganizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_view(self, request, view_func, view_args, view_kwargs):
        """Process the view and extract organization from URL kwargs"""
        request.org = None
        if 'org_slug' in view_kwargs:
            slug = view_kwargs.get('org_slug')
            try:
                request.org = Organization.objects.get(slug=slug)
            except Organization.DoesNotExist:
                raise Http404('Organization not found')
        return None
