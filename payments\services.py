try:
    import requests
except ImportError:
    requests = None
import base64
import json
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from .models import MpesaConfiguration, MpesaTransaction
import logging

logger = logging.getLogger(__name__)


class MpesaAPIException(Exception):
    """Custom exception for M-Pesa API errors"""
    pass


class MpesaService:
    """Service class for M-Pesa API integration"""
    
    def __init__(self, organization):
        self.organization = organization
        try:
            self.config = MpesaConfiguration.objects.get(
                organization=organization, 
                is_active=True
            )
        except MpesaConfiguration.DoesNotExist:
            raise MpesaAPIException(
                f"M-Pesa configuration not found for organization {organization.name}"
            )
        
        # Set API URLs based on environment
        if self.config.is_sandbox:
            self.base_url = "https://sandbox.safaricom.co.ke"
        else:
            self.base_url = "https://api.safaricom.co.ke"
    
    def get_access_token(self):
        """Get OAuth access token from M-Pesa API"""
        if not requests:
            raise MpesaAPIException("requests library not installed. Run: pip install requests")

        url = f"{self.base_url}/oauth/v1/generate?grant_type=client_credentials"

        # Create basic auth header
        credentials = f"{self.config.consumer_key}:{self.config.consumer_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()

        headers = {
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        }

        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            data = response.json()
            return data.get('access_token')

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get M-Pesa access token: {e}")
            raise MpesaAPIException(f"Failed to get access token: {e}")
    
    def generate_password(self):
        """Generate password for STK Push"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        password_string = f"{self.config.business_short_code}{self.config.passkey}{timestamp}"
        password = base64.b64encode(password_string.encode()).decode()
        return password, timestamp
    
    def initiate_stk_push(self, phone_number, amount, invoice, account_reference=None):
        """Initiate STK Push payment"""
        access_token = self.get_access_token()
        password, timestamp = self.generate_password()
        
        # Format phone number (ensure it starts with 254)
        if phone_number.startswith('0'):
            phone_number = '254' + phone_number[1:]
        elif phone_number.startswith('+254'):
            phone_number = phone_number[1:]
        elif not phone_number.startswith('254'):
            phone_number = '254' + phone_number
        
        url = f"{self.base_url}/mpesa/stkpush/v1/processrequest"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'BusinessShortCode': self.config.business_short_code,
            'Password': password,
            'Timestamp': timestamp,
            'TransactionType': 'CustomerPayBillOnline',
            'Amount': str(int(amount)),  # M-Pesa expects integer amount
            'PartyA': phone_number,
            'PartyB': self.config.business_short_code,
            'PhoneNumber': phone_number,
            'CallBackURL': self.config.callback_url,
            'AccountReference': account_reference or f"Invoice-{invoice.number}",
            'TransactionDesc': f"Rent payment for invoice {invoice.number}"
        }
        
        try:
            logger.info(f"Sending STK Push request to: {url}")
            logger.info(f"Payload: {payload}")

            response = requests.post(url, json=payload, headers=headers, timeout=30)

            # Log response for debugging
            logger.info(f"Response Status Code: {response.status_code}")
            logger.info(f"Response Body: {response.text}")

            # Try to parse JSON response
            try:
                data = response.json()
            except ValueError:
                logger.error(f"Invalid JSON response: {response.text}")
                raise MpesaAPIException(f"Invalid response from M-Pesa API: {response.text}")

            # Check for errors in response
            if response.status_code != 200:
                error_msg = data.get('errorMessage') or data.get('errorCode') or response.text
                logger.error(f"STK Push request failed: {error_msg}")
                raise MpesaAPIException(f"STK Push request failed: {error_msg}")

            if data.get('ResponseCode') == '0':
                # Create transaction record
                transaction = MpesaTransaction.objects.create(
                    organization=self.organization,
                    invoice=invoice,
                    phone_number=phone_number,
                    amount=amount,
                    checkout_request_id=data.get('CheckoutRequestID'),
                    merchant_request_id=data.get('MerchantRequestID'),
                    status='PENDING'
                )

                logger.info(f"STK Push initiated successfully: {data.get('CheckoutRequestID')}")
                return transaction, data
            else:
                error_msg = data.get('errorMessage', 'Unknown error')
                logger.error(f"STK Push failed: {error_msg}")
                raise MpesaAPIException(f"STK Push failed: {error_msg}")

        except requests.exceptions.RequestException as e:
            logger.error(f"STK Push request failed: {e}")
            raise MpesaAPIException(f"STK Push request failed: {e}")
    
    def query_transaction_status(self, checkout_request_id):
        """Query the status of an STK Push transaction"""
        access_token = self.get_access_token()
        password, timestamp = self.generate_password()
        
        url = f"{self.base_url}/mpesa/stkpushquery/v1/query"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'BusinessShortCode': self.config.business_short_code,
            'Password': password,
            'Timestamp': timestamp,
            'CheckoutRequestID': checkout_request_id
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Transaction status query failed: {e}")
            raise MpesaAPIException(f"Transaction status query failed: {e}")


class MpesaCallbackHandler:
    """Handle M-Pesa callback responses"""
    
    @staticmethod
    def process_callback(callback_data):
        """Process M-Pesa callback data"""
        try:
            body = callback_data.get('Body', {})
            stk_callback = body.get('stkCallback', {})
            
            checkout_request_id = stk_callback.get('CheckoutRequestID')
            merchant_request_id = stk_callback.get('MerchantRequestID')
            result_code = stk_callback.get('ResultCode')
            result_desc = stk_callback.get('ResultDesc')
            
            if not checkout_request_id:
                logger.error("No CheckoutRequestID in callback data")
                return False
            
            # Find the transaction
            try:
                transaction = MpesaTransaction.objects.get(
                    checkout_request_id=checkout_request_id
                )
            except MpesaTransaction.DoesNotExist:
                logger.error(f"Transaction not found: {checkout_request_id}")
                return False
            
            # Update transaction details
            transaction.merchant_request_id = merchant_request_id
            transaction.result_code = str(result_code)
            transaction.result_desc = result_desc
            
            if result_code == 0:  # Success
                # Extract callback metadata
                callback_metadata = stk_callback.get('CallbackMetadata', {})
                items = callback_metadata.get('Item', [])
                
                for item in items:
                    name = item.get('Name')
                    value = item.get('Value')
                    
                    if name == 'MpesaReceiptNumber':
                        transaction.mpesa_receipt_number = value
                    elif name == 'TransactionDate':
                        # Convert M-Pesa timestamp to datetime
                        if value:
                            try:
                                transaction.transaction_date = datetime.strptime(
                                    str(value), '%Y%m%d%H%M%S'
                                ).replace(tzinfo=timezone.get_current_timezone())
                            except ValueError:
                                logger.warning(f"Invalid transaction date format: {value}")
                
                transaction.status = 'COMPLETED'
                
                # Create payment record
                from rentals.models import Payment
                from django.db import models
                Payment.objects.create(
                    organization=transaction.organization,
                    invoice=transaction.invoice,
                    date=timezone.now().date(),
                    reference=transaction.mpesa_receipt_number or transaction.checkout_request_id,
                    method='MPESA',
                    amount=transaction.amount,
                    transaction_id=transaction.mpesa_receipt_number,
                    status='COMPLETED',
                    mpesa_receipt_number=transaction.mpesa_receipt_number,
                    phone_number=transaction.phone_number,
                    checkout_request_id=transaction.checkout_request_id,
                    merchant_request_id=transaction.merchant_request_id,
                    result_code=str(result_code),
                    result_desc=result_desc
                )
                
                # Update invoice payment status
                invoice = transaction.invoice
                total_payments = Payment.objects.filter(
                    invoice=invoice, 
                    status='COMPLETED'
                ).aggregate(total=models.Sum('amount'))['total'] or 0
                
                invoice.amount_paid = total_payments
                invoice.is_paid = invoice.amount_paid >= invoice.amount_due
                invoice.save()
                
                logger.info(f"Payment completed successfully: {transaction.mpesa_receipt_number}")
                
            else:  # Failed
                transaction.status = 'FAILED'
                logger.warning(f"Payment failed: {result_desc}")
            
            transaction.save()
            return True
            
        except Exception as e:
            logger.error(f"Error processing M-Pesa callback: {e}")
            return False
