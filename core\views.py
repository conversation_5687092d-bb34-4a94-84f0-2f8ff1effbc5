from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from .forms import LoginForm, PropertyForm, UnitForm, TenantForm, LeaseForm, InvoiceForm, PaymentForm
from .decorators import require_any_member, require_admin_or_manager, require_admin_only
from rentals.models import Invoice, Lease, Unit, Property, Tenant, Payment
from rentals.dashboard_service import DashboardService
import json


def login_view(request, org_slug):
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            user = authenticate(
                request,
                username=form.cleaned_data['username'],
                password=form.cleaned_data['password'],
            )
            if user:
                login(request, user)
                messages.success(request, f'Welcome back, {user.username}!')
                return redirect('dashboard', org_slug=org_slug)
            else:
                messages.error(request, 'Invalid username or password.')
    else:
        form = LoginForm()

    return render(request, 'auth/auth-login.html', {'form': form, 'org_slug': org_slug})


def logout_view(request, org_slug):
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('login', org_slug=org_slug)


@require_any_member
def dashboard(request, org_slug):
    """Enhanced dashboard with comprehensive real-time data"""
    org = request.org
    dashboard_service = DashboardService(org)

    # Get all dashboard data from service
    context = {
        'org': org,
        'quick_stats': dashboard_service.get_quick_stats(),
        'kpis': dashboard_service.get_kpi_cards(),
        'revenue_overview': dashboard_service.get_revenue_overview('monthly'),
        'property_performance': dashboard_service.get_property_performance(),
        'portfolio_insights': dashboard_service.get_portfolio_insights(),
        'latest_invoices': dashboard_service.get_latest_invoices(10),
    }

    return render(request, 'dashboard/index.html', context)


@require_any_member
def dashboard_revenue_data(request, org_slug):
    """API endpoint for revenue data by period"""
    period = request.GET.get('period', 'monthly')
    dashboard_service = DashboardService(request.org)
    revenue_data = dashboard_service.get_revenue_overview(period)
    return JsonResponse(revenue_data)


@require_any_member
def dashboard_refresh_data(request, org_slug):
    """API endpoint to refresh all dashboard data"""
    dashboard_service = DashboardService(request.org)

    data = {
        'quick_stats': dashboard_service.get_quick_stats(),
        'kpis': dashboard_service.get_kpi_cards(),
        'property_performance': dashboard_service.get_property_performance(),
        'portfolio_insights': dashboard_service.get_portfolio_insights(),
        'timestamp': timezone.now().isoformat()
    }

    return JsonResponse(data)


def debug_view(request, org_slug):
    """Debug view to check context variables"""
    return render(request, 'debug_context.html')


# Property CRUD Views
@login_required
def property_create(request, org_slug):
    if request.method == 'POST':
        form = PropertyForm(request.POST)
        if form.is_valid():
            property_obj = form.save(commit=False)
            property_obj.organization = request.org
            property_obj.save()
            messages.success(request, 'Property created successfully!')
            return redirect('property_list', org_slug=org_slug)
    else:
        form = PropertyForm()
    return render(request, 'rentals/property_form.html', {'form': form, 'title': 'Add Property'})


@login_required
def property_edit(request, org_slug, pk):
    property_obj = get_object_or_404(Property, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = PropertyForm(request.POST, instance=property_obj)
        if form.is_valid():
            form.save()
            messages.success(request, 'Property updated successfully!')
            return redirect('property_list', org_slug=org_slug)
    else:
        form = PropertyForm(instance=property_obj)
    return render(request, 'rentals/property_form.html', {'form': form, 'title': 'Edit Property'})


@login_required
def property_delete(request, org_slug, pk):
    property_obj = get_object_or_404(Property, pk=pk, organization=request.org)
    if request.method == 'POST':
        property_obj.delete()
        messages.success(request, 'Property deleted successfully!')
        return redirect('property_list', org_slug=org_slug)
    return render(request, 'rentals/property_confirm_delete.html', {'object': property_obj})