# RentalX - Property Management System MVP

A Django-based multi-tenant property management system for managing properties, units, tenants, leases, invoices, and payments.

## 🚀 Features

### ✅ MVP Ready Features
- **Multi-tenant Architecture**: Organization-based data isolation with auto-routing
- **Smart User Routing**: Automatically routes users to their organization dashboard
- **Property Management**: Create and manage properties and units
- **Tenant Management**: Track tenant information and contact details
- **Lease Management**: Handle lease agreements with rent and deposit tracking
- **Invoice Management**: Generate and track rental invoices
- **Payment Processing**: Record and track payments against invoices
- **Dashboard**: KPI overview with key metrics
- **User Authentication**: Secure login/logout system with role-based access
- **Responsive UI**: Bootstrap-based responsive design

### 🔧 Management Commands
- **Invoice Generation**: `python manage.py generate_invoices` - Auto-generate monthly invoices
- **Demo Data Seeder**: `python manage.py seed_demo_data` - Create sample data for testing

## 📋 Quick Setup

### 1. Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install django
```

### 2. Database Setup
```bash
# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 3. Create Demo Data (Optional)
```bash
# Generate sample data
python manage.py seed_demo_data --org-slug demo-org

# Or create your own organization via Django admin
```

### 4. Run Development Server
```bash
python manage.py runserver
```

### 5. Access the Application
- **Admin Panel**: http://127.0.0.1:8000/admin/
- **Application**: http://127.0.0.1:8000/ (auto-routes to your organization)
- **Direct Access**: http://127.0.0.1:8000/demo-org/ (if you know the organization slug)

## 🧪 Testing

Run the test suite to verify functionality:
```bash
python manage.py test
```

## 📁 Project Structure

```
RentalX/
├── core/                   # Core app (auth, organizations, forms, views)
├── rentals/               # Rentals app (properties, units, tenants, leases)
├── templates/             # HTML templates
├── manage.py             # Django management script
└── rentalx/              # Project settings
```

## 🔑 Key Models

- **Organization**: Multi-tenant container
- **Property**: Real estate properties
- **Unit**: Individual rental units within properties
- **Tenant**: Renter information
- **Lease**: Rental agreements
- **Invoice**: Billing records
- **Payment**: Payment tracking

## 🔄 Smart Auto-Routing

RentalX features intelligent user routing:

- **Single Organization**: Users are automatically redirected to their organization's dashboard
- **Multiple Organizations**: Users see a selection screen with their accessible organizations
- **No Access**: Users without organization membership see an access denied page
- **Superuser Access**: Superusers can access any organization or the admin panel
- **Guest Users**: Unauthenticated users are directed to appropriate login pages

## 🎯 Usage Workflow

1. **Setup Organization**: Create via admin panel
2. **Assign Users**: Add users to organizations with appropriate roles (ADMIN, MANAGER, AGENT, TENANT)
3. **Add Properties**: Create properties and their units
4. **Register Tenants**: Add tenant information
5. **Create Leases**: Link tenants to units with rental terms
6. **Generate Invoices**: Use management command or create manually
7. **Record Payments**: Track payments against invoices

## 🔧 Management Commands

### Generate Monthly Invoices
```bash
# Generate invoices for all organizations
python manage.py generate_invoices

# Generate for specific organization
python manage.py generate_invoices --org-slug your-org

# Generate for specific month
python manage.py generate_invoices --month 2025-10

# Dry run (preview without creating)
python manage.py generate_invoices --dry-run
```

### Seed Demo Data
```bash
# Create demo data with default org slug
python manage.py seed_demo_data

# Create with custom org slug
python manage.py seed_demo_data --org-slug my-company

# Clear existing data first
python manage.py seed_demo_data --clear
```

### Assign Users to Organizations
```bash
# Assign a user to an organization
python manage.py assign_user_to_org --username john --org-slug my-company --role ADMIN

# Available roles: ADMIN, MANAGER, AGENT, TENANT
python manage.py assign_user_to_org --username jane --org-slug my-company --role MANAGER
```

## 🛡️ Security Features

- User authentication required for all views
- Organization-based data isolation
- CSRF protection on all forms
- Input validation and sanitization

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🚀 Production Deployment

For production deployment:
1. Set `DEBUG = False` in settings
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure email backend for notifications
5. Set up proper logging
6. Use environment variables for sensitive settings

## 📄 License

This project is open source and available under the MIT License.