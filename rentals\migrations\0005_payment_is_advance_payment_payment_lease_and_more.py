# Generated by Django 5.2.5 on 2025-10-03 18:59

import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_recurringinvoiceschedule'),
        ('rentals', '0004_alter_unit_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='payment',
            name='is_advance_payment',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='payment',
            name='lease',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='rentals.lease'),
        ),
        migrations.AddField(
            model_name='payment',
            name='months_covered',
            field=models.IntegerField(default=1, help_text='Number of months this payment covers'),
        ),
        migrations.AddField(
            model_name='payment',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payments', to='rentals.invoice'),
        ),
        migrations.CreateModel(
            name='AdvancePaymentAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_allocated', models.DecimalField(decimal_places=2, max_digits=10)),
                ('allocation_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advance_allocations', to='rentals.invoice')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='rentals.payment')),
            ],
            options={
                'ordering': ['-allocation_date'],
            },
        ),
        migrations.CreateModel(
            name='TenantCreditBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('lease', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='credit_balance', to='rentals.lease')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
            ],
            options={
                'verbose_name': 'Tenant Credit Balance',
                'verbose_name_plural': 'Tenant Credit Balances',
            },
        ),
    ]
