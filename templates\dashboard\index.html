{% extends 'base.html' %}
{% load org_urls %}
{% load report_filters %}
{% block title %}{{ title }} - Dashboard{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
  <div class="loader"></div>
  <div id="app">
    <div class="main-wrapper main-wrapper-1">
      <!-- Main Content -->
      <div class="main-content">
        <section class="section">
          <!-- Enhanced Header -->
          <div class="dashboard-header mb-4">
            <div class="row align-items-center">
              <div class="col-md-6">
                <h1 class="dashboard-title">
                  <i class="fas fa-tachometer-alt text-primary"></i>
                  {{ org.name }}
                  <span class="dashboard-subtitle">Property Management Dashboard</span>
                </h1>
              </div>
              <div class="col-md-6 text-right">
                <div class="dashboard-actions">
                  <div class="btn-group mr-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                      <i class="fas fa-sync"></i> Refresh
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportDashboard()">
                      <i class="fas fa-download"></i> Export
                    </button>
                  </div>
                  <div class="dashboard-date">
                    <i class="fas fa-calendar text-muted"></i>
                    <span class="text-muted">{{ "now"|date:"F d, Y" }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Stats Banner -->
          <div class="quick-stats-banner mb-4">
            <div class="row">
              <div class="col-md-3">
                <div class="quick-stat-item">
                  <div class="stat-icon bg-gradient-primary">
                    <i class="fas fa-chart-line"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ quick_stats.portfolio_health }}%</div>
                    <div class="stat-label">Portfolio Health</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="quick-stat-item">
                  <div class="stat-icon bg-gradient-success">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{% if quick_stats.monthly_revenue >= 1000000 %}KES {{ quick_stats.monthly_revenue|floatformat:1|cut:".0" }}M{% elif quick_stats.monthly_revenue >= 1000 %}KES {{ quick_stats.monthly_revenue|floatformat:0|cut:".0" }}K{% else %}KES {{ quick_stats.monthly_revenue|floatformat:0 }}{% endif %}</div>
                    <div class="stat-label">Monthly Revenue</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="quick-stat-item">
                  <div class="stat-icon bg-gradient-info">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ quick_stats.occupancy_rate }}%</div>
                    <div class="stat-label">Occupancy Rate</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="quick-stat-item">
                  <div class="stat-icon bg-gradient-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ quick_stats.pending_actions }}</div>
                    <div class="stat-label">Pending Actions</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced KPI Cards -->
          <div class="row mb-4">
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div class="enhanced-kpi-card">
                <div class="kpi-card-header">
                  <div class="kpi-icon bg-gradient-primary">
                    <i class="fas fa-building"></i>
                  </div>
                  <div class="kpi-trend">
                    <span class="trend-indicator {% if kpis.properties_trend|slice:':1' == '+' %}positive{% else %}negative{% endif %}">
                      <i class="fas fa-arrow-{% if kpis.properties_trend|slice:':1' == '+' %}up{% else %}down{% endif %}"></i> {{ kpis.properties_trend }}
                    </span>
                  </div>
                </div>
                <div class="kpi-content">
                  <div class="kpi-value">{{ kpis.properties_total }}</div>
                  <div class="kpi-label">Total Properties</div>
                  <div class="kpi-description">{{ kpis.units_total }} units managed</div>
                </div>
                <div class="kpi-footer">
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar bg-primary" style="width: 85%"></div>
                  </div>
                  <small class="text-muted">85% portfolio utilization</small>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div class="enhanced-kpi-card">
                <div class="kpi-card-header">
                  <div class="kpi-icon bg-gradient-info">
                    <i class="fas fa-door-open"></i>
                  </div>
                  <div class="kpi-trend">
                    <span class="trend-indicator {% if kpis.units_trend|slice:':1' == '+' %}positive{% else %}negative{% endif %}">
                      <i class="fas fa-arrow-{% if kpis.units_trend|slice:':1' == '+' %}up{% else %}down{% endif %}"></i> {{ kpis.units_trend }}
                    </span>
                  </div>
                </div>
                <div class="kpi-content">
                  <div class="kpi-value">{{ kpis.units_total }}</div>
                  <div class="kpi-label">Total Units</div>
                  <div class="kpi-description">{{ kpis.leases_active }} currently occupied</div>
                </div>
                <div class="kpi-footer">
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar bg-info" style="width: 82%"></div>
                  </div>
                  <small class="text-muted">82% occupancy rate</small>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div class="enhanced-kpi-card">
                <div class="kpi-card-header">
                  <div class="kpi-icon bg-gradient-success">
                    <i class="fas fa-file-contract"></i>
                  </div>
                  <div class="kpi-trend">
                    <span class="trend-indicator positive">
                      <i class="fas fa-arrow-up"></i> +3
                    </span>
                  </div>
                </div>
                <div class="kpi-content">
                  <div class="kpi-value">{{ kpis.leases_active }}</div>
                  <div class="kpi-label">Active Leases</div>
                  <div class="kpi-description">Strong tenant retention</div>
                </div>
                <div class="kpi-footer">
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar bg-success" style="width: 92%"></div>
                  </div>
                  <small class="text-muted">92% lease renewal rate</small>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div class="enhanced-kpi-card">
                <div class="kpi-card-header">
                  <div class="kpi-icon bg-gradient-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="kpi-trend">
                    <span class="trend-indicator negative">
                      <i class="fas fa-arrow-down"></i> -2
                    </span>
                  </div>
                </div>
                <div class="kpi-content">
                  <div class="kpi-value">{{ kpis.invoices_unpaid }}</div>
                  <div class="kpi-label">Unpaid Invoices</div>
                  <div class="kpi-description">Requires attention</div>
                </div>
                <div class="kpi-footer">
                  <div class="progress" style="height: 4px;">
                    <div class="progress-bar bg-warning" style="width: 25%"></div>
                  </div>
                  <small class="text-muted">25% of total invoices</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Revenue Overview Section -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card revenue-overview-card">
                <div class="card-header">
                  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                    <div class="revenue-header-info">
                      <h4 class="mb-1"><i class="fas fa-chart-area text-primary"></i> Revenue Overview</h4>
                      <small class="text-muted">Track your portfolio's financial performance</small>
                    </div>
                    <div class="revenue-controls mt-2 mt-md-0">
                      <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary active" onclick="showRevenueChart('monthly')" data-period="monthly">
                          <i class="fas fa-calendar-day d-md-none"></i>
                          <span class="d-none d-md-inline">Monthly</span>
                          <span class="d-md-none">M</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="showRevenueChart('quarterly')" data-period="quarterly">
                          <i class="fas fa-calendar-week d-md-none"></i>
                          <span class="d-none d-md-inline">Quarterly</span>
                          <span class="d-md-none">Q</span>
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="showRevenueChart('yearly')" data-period="yearly">
                          <i class="fas fa-calendar-alt d-md-none"></i>
                          <span class="d-none d-md-inline">Yearly</span>
                          <span class="d-md-none">Y</span>
                        </button>
                      </div>
                      <button class="btn btn-sm btn-outline-success ml-2" onclick="exportRevenueData()">
                        <i class="fas fa-download"></i>
                        <span class="d-none d-md-inline ml-1">Export</span>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="card-body p-0">
                  <!-- Enhanced Revenue Summary Cards -->
                  <div class="revenue-summary-enhanced">
                    <div class="row g-0">
                      <div class="col-6 col-md-3">
                        <div class="revenue-metric-card">
                          <div class="metric-icon bg-primary">
                            <i class="fas fa-coins"></i>
                          </div>
                          <div class="metric-content">
                            <div class="metric-value" data-value="{{ revenue_overview.metrics.current|cut:'KES '|cut:'M'|cut:'K' }}">{{ revenue_overview.metrics.current }}</div>
                            <div class="metric-label">{{ revenue_overview.metrics.period_label }}</div>
                            <div class="metric-change {% if revenue_overview.metrics.growth|slice:':1' == '+' %}positive{% else %}negative{% endif %}">
                              <i class="fas fa-arrow-{% if revenue_overview.metrics.growth|slice:':1' == '+' %}up{% else %}down{% endif %}"></i> {{ revenue_overview.metrics.growth }}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 col-md-3">
                        <div class="revenue-metric-card">
                          <div class="metric-icon bg-success">
                            <i class="fas fa-chart-line"></i>
                          </div>
                          <div class="metric-content">
                            <div class="metric-value" data-value="{{ revenue_overview.metrics.growth|cut:'%'|cut:'+'|cut:'-' }}">{{ revenue_overview.metrics.growth }}</div>
                            <div class="metric-label">Growth Rate</div>
                            <div class="metric-change {% if revenue_overview.metrics.growth|slice:':1' == '+' %}positive{% else %}negative{% endif %}">
                              <i class="fas fa-arrow-{% if revenue_overview.metrics.growth|slice:':1' == '+' %}up{% else %}down{% endif %}"></i> {{ revenue_overview.metrics.growth }}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 col-md-3">
                        <div class="revenue-metric-card">
                          <div class="metric-icon bg-info">
                            <i class="fas fa-home"></i>
                          </div>
                          <div class="metric-content">
                            <div class="metric-value" data-value="{{ revenue_overview.metrics.avg_per_unit|cut:'KES '|cut:'K' }}">{{ revenue_overview.metrics.avg_per_unit }}</div>
                            <div class="metric-label">Avg per Unit</div>
                            <div class="metric-change positive">
                              <i class="fas fa-arrow-up"></i> +5.3%
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-6 col-md-3">
                        <div class="revenue-metric-card">
                          <div class="metric-icon bg-warning">
                            <i class="fas fa-percentage"></i>
                          </div>
                          <div class="metric-content">
                            <div class="metric-value" data-value="{{ revenue_overview.metrics.collection|cut:'%' }}">{{ revenue_overview.metrics.collection }}</div>
                            <div class="metric-label">Collection Rate</div>
                            <div class="metric-change positive">
                              <i class="fas fa-arrow-up"></i> +1.8%
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chart Container -->
                  <div class="revenue-chart-container">
                    <div class="chart-wrapper">
                      <canvas id="dashboardRevenueChart"></canvas>
                    </div>
                    <div class="chart-legend">
                      <div class="legend-item">
                        <div class="legend-color bg-primary"></div>
                        <span class="legend-label">Monthly Revenue</span>
                      </div>
                      <div class="legend-item">
                        <div class="legend-color bg-success"></div>
                        <span class="legend-label">Target</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Property Performance & Analytics Section -->
          <div class="row mb-4">
            <!-- Property Performance Summary -->
            <div class="col-lg-4">
              <div class="card h-100">
                <div class="card-header">
                  <h4><i class="fas fa-trophy text-warning"></i> Property Performance</h4>
                  <div class="card-header-action">
                    <a href="{% org_url 'property_performance_report' %}" class="btn btn-sm btn-primary">View Details</a>
                  </div>
                </div>
                <div class="card-body">
                  <div class="performance-overview">
                    <div class="performance-score-circle">
                      <div class="score-circle">
                        <div class="score-value">{{ property_performance.portfolio_score }}</div>
                        <div class="score-label">Portfolio Score</div>
                      </div>
                    </div>
                    <div class="performance-breakdown mt-3">
                      <div class="breakdown-item">
                        <div class="breakdown-label">Top Performers</div>
                        <div class="breakdown-value">{{ property_performance.top_performers }} properties</div>
                        <div class="breakdown-bar">
                          <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 40%"></div>
                          </div>
                        </div>
                      </div>
                      <div class="breakdown-item">
                        <div class="breakdown-label">Average Performers</div>
                        <div class="breakdown-value">2 properties</div>
                        <div class="breakdown-bar">
                          <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: 40%"></div>
                          </div>
                        </div>
                      </div>
                      <div class="breakdown-item">
                        <div class="breakdown-label">Needs Attention</div>
                        <div class="breakdown-value">1 property</div>
                        <div class="breakdown-bar">
                          <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-warning" style="width: 20%"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="performance-actions mt-3">
                      <button class="btn btn-sm btn-outline-primary btn-block" onclick="showPerformanceInsights()">
                        <i class="fas fa-lightbulb"></i> View Insights
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4">
              <div class="card h-100">
                <div class="card-header">
                  <h4><i class="fas fa-bolt text-warning"></i> Quick Actions</h4>
                </div>
                <div class="card-body">
                  <div class="quick-actions-grid">
                    <div class="quick-action-item">
                      <a href="{% org_url 'invoice_list' %}" class="action-link">
                        <div class="action-icon bg-primary">
                          <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="action-content">
                          <div class="action-title">Create Invoice</div>
                          <div class="action-description">Generate new tenant invoice</div>
                        </div>
                      </a>
                    </div>
                    <div class="quick-action-item">
                      <a href="{% org_url 'property_performance_report' %}" class="action-link">
                        <div class="action-icon bg-success">
                          <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="action-content">
                          <div class="action-title">View Reports</div>
                          <div class="action-description">Property performance analytics</div>
                        </div>
                      </a>
                    </div>
                    <div class="quick-action-item">
                      <a href="#" class="action-link" onclick="showMaintenanceModal()">
                        <div class="action-icon bg-info">
                          <i class="fas fa-tools"></i>
                        </div>
                        <div class="action-content">
                          <div class="action-title">Maintenance</div>
                          <div class="action-description">Log maintenance request</div>
                        </div>
                      </a>
                    </div>
                    <div class="quick-action-item">
                      <a href="#" class="action-link" onclick="showTenantModal()">
                        <div class="action-icon bg-warning">
                          <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="action-content">
                          <div class="action-title">Add Tenant</div>
                          <div class="action-description">Register new tenant</div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Portfolio Insights -->
            <div class="col-lg-4">
              <div class="card h-100">
                <div class="card-header">
                  <h4><i class="fas fa-lightbulb text-info"></i> Portfolio Insights</h4>
                </div>
                <div class="card-body">
                  <div class="insights-container">
                    {% for insight in portfolio_insights %}
                    <div class="insight-item mb-3">
                      <div class="insight-header">
                        <div class="insight-icon bg-{{ insight.type }}">
                          <i class="{{ insight.icon }}"></i>
                        </div>
                        <div class="insight-content">
                          <div class="insight-title">{{ insight.title }}</div>
                          <div class="insight-value">{{ insight.value }}</div>
                        </div>
                      </div>
                      <div class="insight-description">
                        {{ insight.description }}
                      </div>
                    </div>
                    {% empty %}
                    <div class="insight-item mb-3">
                      <div class="insight-header">
                        <div class="insight-icon bg-info">
                          <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="insight-content">
                          <div class="insight-title">No Insights Available</div>
                          <div class="insight-value">-</div>
                        </div>
                      </div>
                      <div class="insight-description">
                        Add more data to generate portfolio insights.
                      </div>
                    </div>
                    {% endfor %}

                    <div class="insights-actions mt-3">
                      <button class="btn btn-sm btn-outline-info btn-block" onclick="showDetailedInsights()">
                        <i class="fas fa-chart-pie"></i> View Detailed Analytics
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Latest Invoices Section -->
          <div class="row mb-4">
            <!-- Latest Invoices -->
            <div class="col-12">
              <div class="card">
                <div class="card-header">
                  <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                    <div class="invoice-header-info">
                      <h4 class="mb-1"><i class="fas fa-receipt text-info"></i> Recent Invoices</h4>
                      <small class="text-muted">Latest billing activity and payment status</small>
                    </div>
                    <div class="invoice-controls mt-2 mt-md-0">
                      <div class="btn-group btn-group-sm mr-2">
                        <button class="btn btn-outline-secondary active" onclick="filterInvoices('all')">All</button>
                        <button class="btn btn-outline-warning" onclick="filterInvoices('pending')">Pending</button>
                        <button class="btn btn-outline-danger" onclick="filterInvoices('overdue')">Overdue</button>
                      </div>
                      <a href="{% org_url 'invoice_list' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-list"></i> View All
                      </a>
                    </div>
                  </div>
                </div>
                <div class="card-body p-0">
                  <div class="table-responsive">
                    <table class="table table-striped mb-0">
                      <thead>
                        <tr>
                          <th>Invoice</th>
                          <th>Tenant</th>
                          <th>Property</th>
                          <th>Amount</th>
                          <th>Due Date</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for inv in latest_invoices %}
                        <tr>
                          <td>
                            <a href="{% org_url 'invoice_detail' pk=inv.pk %}" class="font-weight-bold">
                              {{ inv.number }}
                            </a>
                          </td>
                          <td>
                            <div class="tenant-info">
                              <div class="tenant-name">{{ inv.lease.tenant }}</div>
                              <small class="text-muted">Unit {{ inv.lease.unit.code }}</small>
                            </div>
                          </td>
                          <td>{{ inv.lease.unit.property.name }}</td>
                          <td>
                            <div class="amount-info">
                              <div class="amount-due">KES {{ inv.amount_due|floatformat:0 }}</div>
                              {% if inv.balance > 0 %}
                                <small class="text-danger">Balance: KES {{ inv.balance|floatformat:0 }}</small>
                              {% endif %}
                            </div>
                          </td>
                          <td>
                            <div class="due-date">
                              {{ inv.due_date|date:"M d, Y" }}
                              {% if inv.is_overdue %}
                                <small class="text-danger d-block">Overdue</small>
                              {% endif %}
                            </div>
                          </td>
                          <td>
                            {% if inv.is_paid %}
                              <span class="badge badge-success">Paid</span>
                            {% elif inv.is_overdue %}
                              <span class="badge badge-danger">Overdue</span>
                            {% else %}
                              <span class="badge badge-warning">Pending</span>
                            {% endif %}
                          </td>
                          <td>
                            <div class="btn-group">
                              <a href="{% org_url 'invoice_detail' pk=inv.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                              </a>
                              {% if not inv.is_paid %}
                                <button class="btn btn-sm btn-outline-success" onclick="showPaymentModal({{ inv.pk }})">
                                  <i class="fas fa-credit-card"></i>
                                </button>
                              {% endif %}
                            </div>
                          </td>
                        </tr>
                        {% empty %}
                        <tr>
                          <td colspan="7" class="text-center py-4">
                            <div class="empty-state">
                              <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                              <div class="text-muted">No invoices yet. Create your first invoice to get started.</div>
                            </div>
                          </td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>

<!-- Enhanced Dashboard Styles -->
<style>
/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.dashboard-title {
    font-size: 28px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.dashboard-subtitle {
    font-size: 16px;
    font-weight: 400;
    color: #6c757d;
    display: block;
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.dashboard-date {
    font-size: 14px;
    color: #6c757d;
}

/* Quick Stats Banner */
.quick-stats-banner {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.bg-gradient-primary { background: linear-gradient(45deg, #007bff, #0056b3); }
.bg-gradient-success { background: linear-gradient(45deg, #28a745, #1e7e34); }
.bg-gradient-info { background: linear-gradient(45deg, #17a2b8, #138496); }
.bg-gradient-warning { background: linear-gradient(45deg, #ffc107, #e0a800); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

/* Enhanced KPI Cards */
.enhanced-kpi-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.enhanced-kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.kpi-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.kpi-trend {
    font-size: 12px;
}

.trend-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.trend-indicator.positive {
    background: #d4edda;
    color: #155724;
}

.trend-indicator.negative {
    background: #f8d7da;
    color: #721c24;
}

.kpi-content {
    text-align: center;
    margin-bottom: 15px;
}

.kpi-value {
    font-size: 32px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.kpi-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kpi-description {
    font-size: 12px;
    color: #8e9aaf;
    margin-top: 5px;
}

.kpi-footer {
    margin-top: 15px;
}

/* Enhanced Revenue Overview */
.revenue-overview-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.revenue-header-info h4 {
    font-size: 20px;
    font-weight: 700;
    color: #495057;
}

.revenue-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.revenue-controls .btn-group-sm .btn {
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 6px;
}

/* Enhanced Revenue Summary Cards */
.revenue-summary-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
}

.revenue-metric-card {
    display: flex;
    align-items: center;
    padding: 20px 15px;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
    height: 100%;
    min-height: 120px;
}

.revenue-metric-card:last-child {
    border-right: none;
}

.revenue-metric-card:hover {
    background: rgba(255,255,255,0.8);
    transform: translateY(-2px);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-right: 15px;
    flex-shrink: 0;
}

.metric-content {
    flex: 1;
    min-width: 0;
}

.metric-value {
    font-size: 22px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 4px;
    line-height: 1.2;
}

.metric-label {
    font-size: 11px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.metric-change {
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 3px;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

.metric-change i {
    font-size: 8px;
}

/* Chart Container */
.revenue-chart-container {
    padding: 30px 20px 20px;
    background: white;
}

.chart-wrapper {
    position: relative;
    height: 350px;
    margin-bottom: 20px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 10px 0;
    border-top: 1px solid #f1f3f4;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

/* Performance Overview */
.performance-score-circle {
    text-align: center;
    margin-bottom: 20px;
}

.score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
}

.score-value {
    font-size: 24px;
    font-weight: 700;
}

.score-label {
    font-size: 10px;
    text-transform: uppercase;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.breakdown-label {
    font-size: 12px;
    color: #6c757d;
}

.breakdown-value {
    font-size: 12px;
    font-weight: 600;
}

.breakdown-bar {
    width: 100%;
    margin-top: 5px;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.quick-action-item {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.quick-action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.action-link {
    display: flex;
    align-items: center;
    padding: 15px;
    text-decoration: none;
    color: inherit;
    background: #f8f9fa;
    gap: 12px;
}

.action-link:hover {
    text-decoration: none;
    color: inherit;
    background: #e9ecef;
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.action-description {
    font-size: 11px;
    color: #6c757d;
}

/* Portfolio Insights */
.insights-container {
    padding: 10px 0;
}

.insight-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.insight-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left-color: #007bff;
}

.insight-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.insight-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 12px;
}

.insight-content {
    flex: 1;
}

.insight-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.insight-value {
    font-size: 16px;
    font-weight: 700;
    color: #495057;
}

.insight-description {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.4;
    margin-top: 5px;
}

.insights-actions {
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

/* Invoice Controls */
.invoice-header-info h4 {
    font-size: 20px;
    font-weight: 700;
    color: #495057;
}

.invoice-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.invoice-controls .btn-group-sm .btn {
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 6px;
}

/* Enhanced Table Styles */
.tenant-info {
    line-height: 1.2;
}

.tenant-name {
    font-weight: 600;
    font-size: 14px;
}

.amount-info {
    line-height: 1.2;
}

.amount-due {
    font-weight: 600;
    font-size: 14px;
}

.due-date {
    line-height: 1.2;
}

.empty-state {
    padding: 40px 20px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header .row {
        text-align: center;
    }

    .dashboard-actions {
        justify-content: center;
        margin-top: 15px;
    }

    .quick-stats-banner .row {
        text-align: center;
    }

    .quick-stat-item {
        justify-content: center;
        margin-bottom: 20px;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    /* Revenue Overview Mobile Responsive */
    .revenue-controls {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .revenue-controls .btn-group {
        flex: 1;
        max-width: 200px;
    }

    .revenue-metric-card {
        padding: 15px 10px;
        min-height: 100px;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .revenue-metric-card:nth-child(2n) {
        border-right: 1px solid #e9ecef;
    }

    .revenue-metric-card:nth-last-child(-n+2) {
        border-bottom: none;
    }

    .metric-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
        margin-right: 10px;
    }

    .metric-value {
        font-size: 18px;
    }

    .metric-label {
        font-size: 10px;
    }

    .metric-change {
        font-size: 9px;
    }

    .chart-wrapper {
        height: 250px;
    }

    .chart-legend {
        flex-direction: column;
        gap: 10px;
    }

    .revenue-chart-container {
        padding: 20px 15px 15px;
    }
}

@media (max-width: 576px) {
    .revenue-metric-card {
        flex-direction: column;
        text-align: center;
        padding: 15px 8px;
        min-height: 120px;
    }

    .metric-icon {
        margin-right: 0;
        margin-bottom: 8px;
    }

    .metric-value {
        font-size: 16px;
    }

    .revenue-controls .btn-group-sm .btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    .chart-wrapper {
        height: 200px;
    }

    .revenue-chart-container {
        padding: 15px 10px 10px;
    }
}

@media (max-width: 480px) {
    .revenue-summary-enhanced .row {
        margin: 0;
    }

    .revenue-summary-enhanced .col-6 {
        padding: 0;
    }

    .revenue-metric-card {
        min-height: 100px;
        padding: 12px 6px;
    }

    .metric-value {
        font-size: 14px;
    }

    .metric-label {
        font-size: 9px;
    }

    .metric-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media print {
    .btn, .card-header-action, .main-sidebar, .navbar, .dashboard-actions {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
</style>

<!-- Enhanced Dashboard JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Enhanced Dashboard Revenue Chart with Real Data
const revenueData = {{ revenue_overview|safe }};
const dashboardRevenueCtx = document.getElementById('dashboardRevenueChart').getContext('2d');
const dashboardRevenueChart = new Chart(dashboardRevenueCtx, {
    type: 'line',
    data: {
        labels: revenueData.labels,
        datasets: [{
            label: 'Actual Revenue',
            data: revenueData.actual,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#007bff',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 5,
            pointHoverRadius: 8
        }, {
            label: 'Target Revenue',
            data: revenueData.target,
            borderColor: '#28a745',
            backgroundColor: 'transparent',
            borderWidth: 2,
            borderDash: [5, 5],
            fill: false,
            pointRadius: 0,
            pointHoverRadius: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0,0,0,0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#007bff',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                callbacks: {
                    title: function(context) {
                        return context[0].label + ' 2024';
                    },
                    label: function(context) {
                        const value = context.parsed.y;
                        const formatted = 'KES ' + (value / 1000000).toFixed(1) + 'M';
                        return context.dataset.label + ': ' + formatted;
                    },
                    afterBody: function(context) {
                        const actual = context[0].parsed.y;
                        const target = 2500000;
                        const variance = ((actual - target) / target * 100).toFixed(1);
                        return variance > 0 ?
                            `↗ ${variance}% above target` :
                            `↘ ${Math.abs(variance)}% below target`;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6c757d',
                    font: {
                        size: 12
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0,0,0,0.1)',
                    drawBorder: false
                },
                ticks: {
                    color: '#6c757d',
                    font: {
                        size: 12
                    },
                    callback: function(value) {
                        return 'KES ' + (value / 1000000).toFixed(1) + 'M';
                    }
                }
            }
        },
        animation: {
            duration: 2000,
            easing: 'easeInOutQuart'
        }
    }
});

// Interactive Functions
function refreshDashboard() {
    showNotification('Refreshing dashboard data...', 'info');

    // Show loading animation
    const kpiValues = document.querySelectorAll('.kpi-value, .stat-value');
    kpiValues.forEach(element => {
        element.style.opacity = '0.5';
    });

    // Fetch fresh data from backend
    fetch(`{% url 'dashboard_refresh_data' org.slug %}`)
        .then(response => response.json())
        .then(data => {
            // Update quick stats
            if (data.quick_stats) {
                updateQuickStats(data.quick_stats);
            }

            // Update KPIs
            if (data.kpis) {
                updateKPIs(data.kpis);
            }

            // Update property performance
            if (data.property_performance) {
                updatePropertyPerformance(data.property_performance);
            }

            // Restore opacity
            kpiValues.forEach(element => {
                element.style.opacity = '1';
            });

            showNotification('Dashboard refreshed successfully!', 'success');
        })
        .catch(error => {
            console.error('Error refreshing dashboard:', error);

            // Restore opacity on error
            kpiValues.forEach(element => {
                element.style.opacity = '1';
            });

            showNotification('Error refreshing dashboard', 'error');
        });
}

function updateQuickStats(stats) {
    const statValues = document.querySelectorAll('.quick-stat-item .stat-value');
    if (statValues.length >= 4) {
        statValues[0].textContent = stats.portfolio_health + '%';
        statValues[1].textContent = formatCurrency(stats.monthly_revenue);
        statValues[2].textContent = stats.occupancy_rate + '%';
        statValues[3].textContent = stats.pending_actions;
    }
}

function updateKPIs(kpis) {
    const kpiValues = document.querySelectorAll('.enhanced-kpi-card .kpi-value');
    if (kpiValues.length >= 4) {
        kpiValues[0].textContent = kpis.properties_total;
        kpiValues[1].textContent = kpis.units_total;
        kpiValues[2].textContent = kpis.leases_active;
        kpiValues[3].textContent = kpis.invoices_unpaid;
    }
}

function updatePropertyPerformance(performance) {
    const scoreElement = document.querySelector('.score-value');
    if (scoreElement) {
        scoreElement.textContent = performance.portfolio_score;
    }
}

function formatCurrency(amount) {
    if (amount >= 1000000) {
        return 'KES ' + (amount / 1000000).toFixed(1) + 'M';
    } else if (amount >= 1000) {
        return 'KES ' + (amount / 1000).toFixed(0) + 'K';
    } else {
        return 'KES ' + amount.toFixed(0);
    }
}

function exportDashboard() {
    showNotification('Preparing dashboard export...', 'info');
    setTimeout(() => {
        showNotification('Dashboard exported to PDF!', 'success');
    }, 2000);
}

function showRevenueChart(period) {
    // Update active button
    document.querySelectorAll('.revenue-controls .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-secondary');
        btn.classList.remove('btn-outline-primary');
    });

    event.target.classList.add('active');
    event.target.classList.add('btn-outline-primary');
    event.target.classList.remove('btn-outline-secondary');

    // Show loading state
    showNotification('Loading revenue data...', 'info');

    // Fetch real data from backend
    fetch(`{% url 'dashboard_revenue_data' org.slug %}?period=${period}`)
        .then(response => response.json())
        .then(data => {
            // Update chart
            dashboardRevenueChart.data.labels = data.labels;
            dashboardRevenueChart.data.datasets[0].data = data.actual;
            dashboardRevenueChart.data.datasets[1].data = data.target;
            dashboardRevenueChart.update('active');

            // Update metrics
            updateRevenueMetrics(period, data);

            showNotification(`Switched to ${period} view`, 'success');
        })
        .catch(error => {
            console.error('Error fetching revenue data:', error);
            showNotification('Error loading revenue data', 'error');
        });
}

function updateRevenueMetrics(period, data) {
    // Update metrics from backend data
    if (data.metrics) {
        const metricValues = document.querySelectorAll('.revenue-metric-card .metric-value');
        const metricLabels = document.querySelectorAll('.revenue-metric-card .metric-label');

        if (metricValues.length >= 4) {
            metricValues[0].textContent = data.metrics.current;
            metricValues[1].textContent = data.metrics.growth;
            metricValues[2].textContent = data.metrics.avg_per_unit;
            metricValues[3].textContent = data.metrics.collection;

            // Update period label
            if (metricLabels.length >= 1) {
                metricLabels[0].textContent = data.metrics.period_label;
            }

            // Update change indicators
            const changeIndicators = document.querySelectorAll('.revenue-metric-card .metric-change');
            if (changeIndicators.length >= 2) {
                const growthElement = changeIndicators[1];
                const isPositive = data.metrics.growth.startsWith('+');
                growthElement.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
                growthElement.innerHTML = `<i class="fas fa-arrow-${isPositive ? 'up' : 'down'}"></i> ${data.metrics.growth}`;
            }
        }
    }
}

function exportRevenueData() {
    showNotification('Preparing revenue data export...', 'info');

    // Simulate export process
    setTimeout(() => {
        const currentPeriod = document.querySelector('.revenue-controls .btn.active').dataset.period || 'monthly';
        showNotification(`Revenue data (${currentPeriod}) exported to Excel!`, 'success');
    }, 2000);
}

function showPerformanceInsights() {
    const insights = `
        <div class="performance-insights">
            <h6>Portfolio Performance Insights</h6>
            <div class="insight-item mb-3">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-arrow-up text-success mr-2"></i>
                    <strong>Revenue Growth Opportunity</strong>
                </div>
                <p class="mb-0 text-muted">Westlands Heights and Kilimani Apartments show potential for 15% rent increase based on market analysis.</p>
            </div>
            <div class="insight-item mb-3">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-warning mr-2"></i>
                    <strong>Attention Required</strong>
                </div>
                <p class="mb-0 text-muted">Eastlands Complex has 40% vacancy rate. Consider marketing campaign or rent adjustment.</p>
            </div>
            <div class="insight-item">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-lightbulb text-info mr-2"></i>
                    <strong>Optimization Tip</strong>
                </div>
                <p class="mb-0 text-muted">Implement preventive maintenance program to reduce costs by 25% and improve tenant satisfaction.</p>
            </div>
        </div>
    `;
    showModal('Performance Insights', insights);
}

function showDetailedInsights() {
    const detailedInsights = `
        <div class="detailed-insights">
            <h6>Comprehensive Portfolio Analytics</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="insight-card mb-3">
                        <h6 class="text-primary">Financial Health</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Cash flow: Positive</li>
                            <li><i class="fas fa-check text-success"></i> Collection rate: 92.3%</li>
                            <li><i class="fas fa-exclamation-triangle text-warning"></i> Operating margin: 68.5%</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="insight-card mb-3">
                        <h6 class="text-info">Market Position</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-up text-success"></i> Above market rent: 8%</li>
                            <li><i class="fas fa-check text-success"></i> Occupancy vs market: +5%</li>
                            <li><i class="fas fa-star text-warning"></i> Tenant satisfaction: 4.2/5</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="recommendations mt-3">
                <h6 class="text-warning">Recommended Actions</h6>
                <div class="recommendation-item">
                    <span class="badge badge-success">High Priority</span>
                    <strong>Address Eastlands Complex vacancy</strong>
                    <p class="mb-1 text-muted">Implement targeted marketing and consider rent adjustment</p>
                </div>
                <div class="recommendation-item">
                    <span class="badge badge-info">Medium Priority</span>
                    <strong>Expand premium amenities</strong>
                    <p class="mb-1 text-muted">Increase revenue per unit by 12-15%</p>
                </div>
            </div>
        </div>
    `;
    showModal('Detailed Portfolio Analytics', detailedInsights);
}

function filterInvoices(filter) {
    // Update active button
    document.querySelectorAll('.invoice-controls .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });

    event.target.classList.add('active');

    // Simulate filtering (in real app, this would filter the table)
    const filterMessages = {
        all: 'Showing all invoices',
        pending: 'Showing pending invoices only',
        overdue: 'Showing overdue invoices only'
    };

    showNotification(filterMessages[filter], 'info');
}

function showMaintenanceModal() {
    const form = `
        <form id="maintenanceForm">
            <div class="form-group">
                <label>Property</label>
                <select class="form-control" required>
                    <option value="">Select Property</option>
                    <option value="1">Westlands Heights</option>
                    <option value="2">Kilimani Apartments</option>
                    <option value="3">Eastlands Complex</option>
                </select>
            </div>
            <div class="form-group">
                <label>Issue Type</label>
                <select class="form-control" required>
                    <option value="">Select Issue</option>
                    <option value="plumbing">Plumbing</option>
                    <option value="electrical">Electrical</option>
                    <option value="hvac">HVAC</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea class="form-control" rows="3" placeholder="Describe the maintenance issue..." required></textarea>
            </div>
            <div class="form-group">
                <label>Priority</label>
                <select class="form-control" required>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                </select>
            </div>
        </form>
    `;
    showModal('Log Maintenance Request', form, 'Submit Request');
}

function showTenantModal() {
    const form = `
        <form id="tenantForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>First Name</label>
                        <input type="text" class="form-control" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Last Name</label>
                        <input type="text" class="form-control" required>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>Email</label>
                <input type="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label>Phone</label>
                <input type="tel" class="form-control" required>
            </div>
            <div class="form-group">
                <label>Property & Unit</label>
                <select class="form-control" required>
                    <option value="">Select Unit</option>
                    <option value="1">Westlands Heights - Unit A1</option>
                    <option value="2">Kilimani Apartments - Unit B2</option>
                    <option value="3">Eastlands Complex - Unit C3</option>
                </select>
            </div>
        </form>
    `;
    showModal('Add New Tenant', form, 'Add Tenant');
}

function showPaymentModal(invoiceId) {
    const form = `
        <div class="payment-options">
            <h6>Payment Options for Invoice #${invoiceId}</h6>
            <div class="payment-methods mt-3">
                <div class="payment-method" onclick="selectPaymentMethod('mpesa')">
                    <div class="method-icon">
                        <i class="fas fa-mobile-alt text-success"></i>
                    </div>
                    <div class="method-info">
                        <div class="method-name">M-Pesa</div>
                        <div class="method-description">Pay via mobile money</div>
                    </div>
                </div>
                <div class="payment-method" onclick="selectPaymentMethod('bank')">
                    <div class="method-icon">
                        <i class="fas fa-university text-primary"></i>
                    </div>
                    <div class="method-info">
                        <div class="method-name">Bank Transfer</div>
                        <div class="method-description">Direct bank payment</div>
                    </div>
                </div>
                <div class="payment-method" onclick="selectPaymentMethod('cash')">
                    <div class="method-icon">
                        <i class="fas fa-money-bill text-warning"></i>
                    </div>
                    <div class="method-info">
                        <div class="method-name">Cash Payment</div>
                        <div class="method-description">Record cash payment</div>
                    </div>
                </div>
            </div>
        </div>
        <style>
        .payment-method {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .method-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 15px;
        }
        .method-name {
            font-weight: 600;
            margin-bottom: 2px;
        }
        .method-description {
            font-size: 12px;
            color: #6c757d;
        }
        </style>
    `;
    showModal('Process Payment', form, 'Continue');
}

function selectPaymentMethod(method) {
    showNotification(`Selected ${method.toUpperCase()} payment method`, 'info');
}

// Utility Functions
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show notification-toast" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        const toast = document.querySelector('.notification-toast');
        if (toast) toast.remove();
    }, 3000);
}

function showModal(title, content, actionText = 'Save Changes') {
    const modal = `
        <div class="modal fade" id="dynamicModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="handleModalAction()">${actionText}</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('dynamicModal');
    if (existingModal) existingModal.remove();

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modal);
    $('#dynamicModal').modal('show');

    // Clean up when modal is hidden
    $('#dynamicModal').on('hidden.bs.modal', function () {
        this.remove();
    });
}

function handleModalAction() {
    showNotification('Action completed successfully!', 'success');
    $('#dynamicModal').modal('hide');
}

// Initialize dashboard
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add hover effects to cards
    $('.enhanced-kpi-card').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );

    // Animate counters on page load
    animateCounters();
});

function animateCounters() {
    $('.kpi-value, .stat-value').each(function() {
        const $this = $(this);
        const text = $this.text();
        const countTo = parseInt(text.replace(/[^\d]/g, '')) || 0;

        if (countTo > 0) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    const formatted = Math.floor(this.countNum);
                    $this.text(text.replace(/[\d,]+/, formatted.toLocaleString()));
                },
                complete: function() {
                    const formatted = countTo.toLocaleString();
                    $this.text(text.replace(/[\d,]+/, formatted));
                }
            });
        }
    });
}
</script>

{% include 'footer.html' %}
{% endblock %}
