from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Q, Avg
from django.db.models.functions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>runcYear
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from core.decorators import require_any_member
from .models import (
    Property, Unit, Tenant, Lease, Invoice, Payment,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem, MaintenanceRequest
)
import json


@require_any_member
def financial_dashboard(request, org_slug):
    """Main financial dashboard with key metrics"""
    current_date = timezone.now().date()
    current_month = current_date.replace(day=1)
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    current_year = current_date.year
    
    # Key Financial Metrics
    total_properties = Property.objects.filter(organization=request.org).count()
    total_units = Unit.objects.filter(organization=request.org).count()
    occupied_units = Unit.objects.filter(
        organization=request.org,
        lease__status='ACTIVE',
        lease__start_date__lte=current_date,
        lease__end_date__gte=current_date
    ).distinct().count()
    
    occupancy_rate = (occupied_units / total_units * 100) if total_units > 0 else 0
    
    # Monthly Revenue
    current_month_revenue = Payment.objects.filter(
        organization=request.org,
        status='COMPLETED',
        date__gte=current_month,
        date__lt=current_month + timedelta(days=32)
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    last_month_revenue = Payment.objects.filter(
        organization=request.org,
        status='COMPLETED',
        date__gte=last_month,
        date__lt=current_month
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    # Outstanding Invoices
    outstanding_amount = Invoice.objects.filter(
        organization=request.org,
        is_paid=False
    ).aggregate(total=Sum('amount_due') - Sum('amount_paid'))['total'] or Decimal('0.00')
    
    outstanding_count = Invoice.objects.filter(
        organization=request.org,
        is_paid=False
    ).count()
    
    # Government Taxes Due
    taxes_due = calculate_monthly_taxes(request.org, current_month)
    
    context = {
        'total_properties': total_properties,
        'total_units': total_units,
        'occupied_units': occupied_units,
        'occupancy_rate': round(occupancy_rate, 1),
        'current_month_revenue': current_month_revenue,
        'last_month_revenue': last_month_revenue,
        'outstanding_amount': outstanding_amount,
        'outstanding_count': outstanding_count,
        'taxes_due': taxes_due,
        'current_month': current_month.strftime('%B %Y'),
    }
    
    return render(request, 'reports/financial_dashboard.html', context)


@require_any_member
def income_statement(request, org_slug):
    """Generate income statement report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not start_date or not end_date:
        # Default to current month
        current_date = timezone.now().date()
        start_date = current_date.replace(day=1)
        end_date = current_date
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    
    # Revenue Breakdown
    rent_revenue = InvoiceLineItem.objects.filter(
        organization=request.org,
        item_type='RENT',
        invoice__issue_date__gte=start_date,
        invoice__issue_date__lte=end_date,
        invoice__is_paid=True
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    amenities_revenue = InvoiceLineItem.objects.filter(
        organization=request.org,
        item_type__in=['WATER', 'ELECTRICITY', 'GARBAGE', 'INTERNET', 'SECURITY', 'PARKING'],
        invoice__issue_date__gte=start_date,
        invoice__issue_date__lte=end_date,
        invoice__is_paid=True
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    other_revenue = InvoiceLineItem.objects.filter(
        organization=request.org,
        item_type='OTHER',
        invoice__issue_date__gte=start_date,
        invoice__issue_date__lte=end_date,
        invoice__is_paid=True
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    total_revenue = rent_revenue + amenities_revenue + other_revenue
    
    # Government Taxes
    rental_income_tax = calculate_tax_for_period(request.org, 'RENTAL_INCOME_TAX', total_revenue, start_date, end_date)
    property_tax = calculate_tax_for_period(request.org, 'PROPERTY_TAX', total_revenue, start_date, end_date)
    vat = calculate_tax_for_period(request.org, 'VAT', amenities_revenue, start_date, end_date)
    
    total_taxes = rental_income_tax + property_tax + vat
    net_income = total_revenue - total_taxes
    
    context = {
        'start_date': start_date,
        'end_date': end_date,
        'rent_revenue': rent_revenue,
        'amenities_revenue': amenities_revenue,
        'other_revenue': other_revenue,
        'total_revenue': total_revenue,
        'rental_income_tax': rental_income_tax,
        'property_tax': property_tax,
        'vat': vat,
        'total_taxes': total_taxes,
        'net_income': net_income,
    }
    
    return render(request, 'reports/income_statement.html', context)


@require_any_member
def occupancy_report(request, org_slug):
    """Generate occupancy and vacancy report"""
    current_date = timezone.now().date()
    
    # Get all properties with their units
    properties = Property.objects.filter(organization=request.org).prefetch_related('unit_set')
    
    property_data = []
    total_units = 0
    total_occupied = 0
    total_revenue = Decimal('0.00')
    
    for property_obj in properties:
        units = property_obj.unit_set.all()
        property_units = units.count()
        
        # Get occupied units
        occupied_units = units.filter(
            lease__status='ACTIVE',
            lease__start_date__lte=current_date,
            lease__end_date__gte=current_date
        ).distinct()
        
        occupied_count = occupied_units.count()
        occupancy_rate = (occupied_count / property_units * 100) if property_units > 0 else 0
        
        # Calculate revenue for this property
        property_revenue = Payment.objects.filter(
            organization=request.org,
            status='COMPLETED',
            invoice__lease__unit__property=property_obj,
            date__month=current_date.month,
            date__year=current_date.year
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Average rent per unit
        avg_rent = Lease.objects.filter(
            organization=request.org,
            unit__property=property_obj,
            status='ACTIVE'
        ).aggregate(avg=Avg('rent_amount'))['avg'] or Decimal('0.00')
        
        property_data.append({
            'property': property_obj,
            'total_units': property_units,
            'occupied_units': occupied_count,
            'vacant_units': property_units - occupied_count,
            'occupancy_rate': round(occupancy_rate, 1),
            'revenue': property_revenue,
            'avg_rent': avg_rent,
        })
        
        total_units += property_units
        total_occupied += occupied_count
        total_revenue += property_revenue
    
    overall_occupancy = (total_occupied / total_units * 100) if total_units > 0 else 0
    
    # Tenant Analytics
    total_tenants = Tenant.objects.filter(organization=request.org).count()
    active_leases = Lease.objects.filter(
        organization=request.org,
        status='ACTIVE',
        start_date__lte=current_date,
        end_date__gte=current_date
    ).count()
    
    # Lease expiry analysis
    expiring_soon = Lease.objects.filter(
        organization=request.org,
        status='ACTIVE',
        end_date__gte=current_date,
        end_date__lte=current_date + timedelta(days=90)
    ).count()
    
    context = {
        'property_data': property_data,
        'total_units': total_units,
        'total_occupied': total_occupied,
        'total_vacant': total_units - total_occupied,
        'overall_occupancy': round(overall_occupancy, 1),
        'total_revenue': total_revenue,
        'total_tenants': total_tenants,
        'active_leases': active_leases,
        'expiring_soon': expiring_soon,
        'current_month': current_date.strftime('%B %Y'),
    }
    
    return render(request, 'reports/occupancy_report.html', context)


def calculate_monthly_taxes(organization, month_date):
    """Calculate government taxes due for a specific month"""
    taxes = GovernmentTax.objects.filter(
        organization=organization,
        is_active=True,
        effective_date__lte=month_date
    )
    
    # Get monthly revenue for tax calculation
    monthly_revenue = Payment.objects.filter(
        organization=organization,
        status='COMPLETED',
        date__month=month_date.month,
        date__year=month_date.year
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
    
    # Get unit count
    unit_count = Unit.objects.filter(organization=organization).count()
    
    total_taxes = Decimal('0.00')
    tax_breakdown = {}
    
    for tax in taxes:
        tax_amount = tax.calculate_tax(monthly_revenue, unit_count)
        total_taxes += tax_amount
        tax_breakdown[tax.name] = tax_amount
    
    return {
        'total': total_taxes,
        'breakdown': tax_breakdown
    }


def calculate_tax_for_period(organization, tax_type, base_amount, start_date, end_date):
    """Calculate specific tax for a period"""
    try:
        tax = GovernmentTax.objects.get(
            organization=organization,
            tax_type=tax_type,
            is_active=True,
            effective_date__lte=end_date
        )
        return tax.calculate_tax(base_amount)
    except GovernmentTax.DoesNotExist:
        return Decimal('0.00')


@require_any_member
def cash_flow_report(request, org_slug):
    """Generate cash flow report"""
    # Get last 12 months of data
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=365)

    # Monthly cash flow data
    monthly_data = Payment.objects.filter(
        organization=request.org,
        status='COMPLETED',
        date__gte=start_date,
        date__lte=end_date
    ).annotate(
        month=TruncMonth('date')
    ).values('month').annotate(
        total_inflow=Sum('amount')
    ).order_by('month')

    # Convert to list for chart
    cash_flow_data = []
    for item in monthly_data:
        cash_flow_data.append({
            'month': item['month'].strftime('%Y-%m'),
            'month_name': item['month'].strftime('%B %Y'),
            'inflow': float(item['total_inflow']),
            'outflow': 0,  # Add expense tracking later
            'net_flow': float(item['total_inflow'])
        })

    context = {
        'cash_flow_data': cash_flow_data,
        'cash_flow_json': json.dumps(cash_flow_data),
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'reports/cash_flow_report.html', context)


@require_any_member
def property_performance_report(request, org_slug):
    """Generate comprehensive property performance report with analytics"""
    current_date = timezone.now().date()
    current_month = current_date.replace(day=1)
    last_month = (current_month - timedelta(days=1)).replace(day=1)
    start_of_year = current_date.replace(month=1, day=1)

    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date or not end_date:
        # Default to current year
        start_date = start_of_year
        end_date = current_date
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get all properties with comprehensive analytics
    properties = Property.objects.filter(organization=request.org).prefetch_related(
        'unit_set__lease_set',
        'unit_set__maintenancerequest_set'
    )

    property_analytics = []
    total_revenue = Decimal('0.00')
    total_maintenance_cost = Decimal('0.00')

    for property_obj in properties:
        analytics = calculate_property_analytics(property_obj, start_date, end_date, current_date)
        property_analytics.append(analytics)
        total_revenue += analytics['total_revenue']
        total_maintenance_cost += analytics['maintenance_cost']

    # Sort properties by performance score
    property_analytics.sort(key=lambda x: x['performance_score'], reverse=True)

    # Calculate portfolio-wide metrics
    portfolio_metrics = calculate_portfolio_metrics(request.org, start_date, end_date, current_date)

    # Monthly revenue trends for all properties
    monthly_trends = calculate_monthly_trends(request.org, start_date, end_date)

    # Top and bottom performers
    top_performers = property_analytics[:3] if len(property_analytics) >= 3 else property_analytics
    bottom_performers = property_analytics[-3:] if len(property_analytics) >= 3 else []

    context = {
        'property_analytics': property_analytics,
        'portfolio_metrics': portfolio_metrics,
        'monthly_trends': monthly_trends,
        'monthly_trends_json': json.dumps(monthly_trends),
        'top_performers': top_performers,
        'bottom_performers': bottom_performers,
        'total_revenue': total_revenue,
        'total_maintenance_cost': total_maintenance_cost,
        'start_date': start_date,
        'end_date': end_date,
        'current_date': current_date,
    }

    return render(request, 'reports/property_performance_report.html', context)


def calculate_property_analytics(property_obj, start_date, end_date, current_date):
    """Calculate comprehensive analytics for a single property"""
    units = property_obj.unit_set.all()
    total_units = units.count()

    # Occupancy Analysis
    occupied_units = units.filter(
        lease__status='ACTIVE',
        lease__start_date__lte=current_date,
        lease__end_date__gte=current_date
    ).distinct().count()

    occupancy_rate = (occupied_units / total_units * 100) if total_units > 0 else 0

    # Revenue Analysis
    total_revenue = Payment.objects.filter(
        organization=property_obj.organization,
        status='COMPLETED',
        invoice__lease__unit__property=property_obj,
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    # Revenue breakdown by type
    rent_revenue = Payment.objects.filter(
        organization=property_obj.organization,
        status='COMPLETED',
        invoice__lease__unit__property=property_obj,
        invoice__rent_amount__gt=0,
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    amenities_revenue = Payment.objects.filter(
        organization=property_obj.organization,
        status='COMPLETED',
        invoice__lease__unit__property=property_obj,
        invoice__amenities_amount__gt=0,
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    # Average rent per unit (from active leases)
    avg_rent = Lease.objects.filter(
        unit__property=property_obj,
        status='ACTIVE'
    ).aggregate(avg=Avg('rent_amount'))['avg'] or Decimal('0.00')

    # Revenue per unit
    revenue_per_unit = (total_revenue / total_units) if total_units > 0 else Decimal('0.00')

    # Maintenance Analysis
    maintenance_requests = property_obj.unit_set.aggregate(
        total_requests=Count('maintenancerequest'),
        open_requests=Count('maintenancerequest', filter=Q(maintenancerequest__status='Open')),
        high_priority=Count('maintenancerequest', filter=Q(maintenancerequest__priority='HIGH'))
    )

    # Estimate maintenance cost (placeholder - you can add actual cost tracking)
    maintenance_cost = maintenance_requests['total_requests'] * Decimal('5000.00')  # Avg cost per request

    # Tenant Retention Analysis
    total_leases = Lease.objects.filter(
        unit__property=property_obj,
        start_date__gte=start_date - timedelta(days=365),
        start_date__lte=end_date
    ).count()

    expired_leases = Lease.objects.filter(
        unit__property=property_obj,
        end_date__gte=start_date,
        end_date__lte=end_date,
        status='EXPIRED'
    ).count()

    renewed_leases = Lease.objects.filter(
        unit__property=property_obj,
        start_date__gte=start_date,
        start_date__lte=end_date,
        status='ACTIVE'
    ).count()

    retention_rate = ((renewed_leases / expired_leases) * 100) if expired_leases > 0 else 100

    # Performance Score Calculation (0-100)
    occupancy_score = min(occupancy_rate, 100) * 0.3  # 30% weight
    revenue_score = min((float(revenue_per_unit) / 50000) * 100, 100) * 0.4  # 40% weight
    maintenance_score = max(100 - (maintenance_requests['open_requests'] * 10), 0) * 0.2  # 20% weight
    retention_score = min(retention_rate, 100) * 0.1  # 10% weight

    performance_score = occupancy_score + revenue_score + maintenance_score + retention_score

    # Vacancy Analysis
    vacant_units = total_units - occupied_units
    avg_vacancy_days = calculate_avg_vacancy_days(property_obj, start_date, end_date)

    return {
        'property': property_obj,
        'total_units': total_units,
        'occupied_units': occupied_units,
        'vacant_units': vacant_units,
        'occupancy_rate': round(occupancy_rate, 1),
        'total_revenue': total_revenue,
        'rent_revenue': rent_revenue,
        'amenities_revenue': amenities_revenue,
        'revenue_per_unit': revenue_per_unit,
        'avg_rent': avg_rent,
        'maintenance_requests': maintenance_requests,
        'maintenance_cost': maintenance_cost,
        'retention_rate': round(retention_rate, 1),
        'performance_score': round(performance_score, 1),
        'avg_vacancy_days': avg_vacancy_days,
        'total_leases': total_leases,
    }


def calculate_avg_vacancy_days(property_obj, start_date, end_date):
    """Calculate average vacancy days for a property"""
    # This is a simplified calculation - you can enhance based on actual lease data
    vacant_units = property_obj.unit_set.filter(
        lease__isnull=True
    ).count()

    if vacant_units == 0:
        return 0

    # Estimate based on lease gaps (simplified)
    return 30  # Placeholder - implement actual vacancy tracking


def calculate_portfolio_metrics(organization, start_date, end_date, current_date):
    """Calculate portfolio-wide performance metrics"""
    total_properties = Property.objects.filter(organization=organization).count()
    total_units = Unit.objects.filter(organization=organization).count()

    occupied_units = Unit.objects.filter(
        organization=organization,
        lease__status='ACTIVE',
        lease__start_date__lte=current_date,
        lease__end_date__gte=current_date
    ).distinct().count()

    portfolio_occupancy = (occupied_units / total_units * 100) if total_units > 0 else 0

    total_revenue = Payment.objects.filter(
        organization=organization,
        status='COMPLETED',
        date__gte=start_date,
        date__lte=end_date
    ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

    avg_revenue_per_property = (total_revenue / total_properties) if total_properties > 0 else Decimal('0.00')
    avg_revenue_per_unit = (total_revenue / total_units) if total_units > 0 else Decimal('0.00')

    # Maintenance metrics
    total_maintenance_requests = MaintenanceRequest.objects.filter(
        unit__organization=organization,
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).count()

    open_maintenance_requests = MaintenanceRequest.objects.filter(
        unit__organization=organization,
        status='Open'
    ).count()

    return {
        'total_properties': total_properties,
        'total_units': total_units,
        'occupied_units': occupied_units,
        'portfolio_occupancy': round(portfolio_occupancy, 1),
        'total_revenue': total_revenue,
        'avg_revenue_per_property': avg_revenue_per_property,
        'avg_revenue_per_unit': avg_revenue_per_unit,
        'total_maintenance_requests': total_maintenance_requests,
        'open_maintenance_requests': open_maintenance_requests,
    }


def calculate_monthly_trends(organization, start_date, end_date):
    """Calculate monthly revenue trends for portfolio"""
    monthly_data = Payment.objects.filter(
        organization=organization,
        status='COMPLETED',
        date__gte=start_date,
        date__lte=end_date
    ).annotate(
        month=TruncMonth('date')
    ).values('month').annotate(
        total_revenue=Sum('amount'),
        payment_count=Count('id')
    ).order_by('month')

    trends = []
    for item in monthly_data:
        trends.append({
            'month': item['month'].strftime('%Y-%m'),
            'month_name': item['month'].strftime('%B %Y'),
            'revenue': float(item['total_revenue']),
            'payment_count': item['payment_count'],
        })

    return trends
