{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{{ title }}</h4>
                                <div class="card-header-action">
                                    <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Back to Invoices
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Bulk Invoice Generation</strong><br>
                                    This will generate invoices for <strong>{{ active_leases.count }}</strong> active lease(s).
                                    All mandatory amenities will be automatically included.
                                </div>

                                {% if active_leases.count == 0 %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    No active leases found. Please ensure you have active leases before generating invoices.
                                </div>
                                {% else %}

                                <form method="post" id="bulk-invoice-form">
                                    {% csrf_token %}
                                    
                                    <!-- Invoice Settings -->
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Invoice Settings</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Invoice Type</label>
                                                        <select name="invoice_type" class="form-control">
                                                            <option value="RENT">Rent Only</option>
                                                            <option value="AMENITIES">Amenities Only</option>
                                                            <option value="MIXED" selected>Rent + Amenities</option>
                                                            <option value="OTHER">Other</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Issue Date</label>
                                                        <input type="date" name="issue_date" class="form-control" 
                                                               value="{{ suggested_issue_date|date:'Y-m-d' }}" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Due Date</label>
                                                        <input type="date" name="due_date" class="form-control" 
                                                               value="{{ suggested_due_date|date:'Y-m-d' }}" required>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Billing Items -->
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Billing Items to Include</h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- Rent -->
                                            <div class="form-check mb-3">
                                                <input type="checkbox" name="include_rent" id="include_rent" 
                                                       class="form-check-input" checked>
                                                <label class="form-check-label" for="include_rent">
                                                    <strong>Include Rent</strong>
                                                    <small class="text-muted d-block">Monthly rent will be automatically calculated from each lease</small>
                                                </label>
                                            </div>

                                            <!-- Amenities -->
                                            {% if amenities %}
                                            <h6 class="border-bottom pb-2 mb-3">Amenities & Services</h6>
                                            <div class="row">
                                                {% for amenity in amenities %}
                                                <div class="col-md-6 mb-2">
                                                    <div class="card">
                                                        <div class="card-body p-2">
                                                            <div class="form-check">
                                                                <input type="checkbox" 
                                                                       name="amenity_{{ amenity.id }}" 
                                                                       id="amenity_{{ amenity.id }}"
                                                                       class="form-check-input amenity-checkbox"
                                                                       {% if amenity.is_mandatory %}checked{% endif %}>
                                                                <label class="form-check-label" for="amenity_{{ amenity.id }}">
                                                                    {{ amenity.name }} - KES {{ amenity.default_amount }}
                                                                    {% if amenity.is_mandatory %}
                                                                        <span class="badge bg-warning text-dark">Mandatory</span>
                                                                    {% endif %}
                                                                </label>
                                                                <small class="text-muted d-block">
                                                                    {{ amenity.billing_cycle }} billing
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% else %}
                                            <p class="text-muted">
                                                No amenities configured. 
                                                <a href="/admin/rentals/amenitytype/add/" target="_blank">Add amenities</a> 
                                                in the admin panel.
                                            </p>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Leases Preview -->
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Leases to Invoice ({{ active_leases.count }})</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>#</th>
                                                            <th>Tenant</th>
                                                            <th>Unit</th>
                                                            <th>Rent Amount</th>
                                                            <th>Start Date</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {% for lease in active_leases %}
                                                        <tr>
                                                            <td>{{ forloop.counter }}</td>
                                                            <td>{{ lease.tenant }}</td>
                                                            <td>{{ lease.unit }}</td>
                                                            <td>KES {{ lease.rent_amount }}</td>
                                                            <td>{{ lease.start_date }}</td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Summary -->
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-check-circle"></i> Generation Summary</h6>
                                        <ul class="mb-0">
                                            <li><strong>{{ active_leases.count }}</strong> invoice(s) will be created</li>
                                            <li>Each invoice will have <strong id="item-count">1</strong> line item(s)</li>
                                            <li>Invoice numbers will be auto-generated</li>
                                        </ul>
                                    </div>

                                    <!-- Actions -->
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-file-invoice"></i> Generate Invoices
                                        </button>
                                        <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">Cancel</a>
                                    </div>
                                </form>

                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% include 'footer.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const includeRent = document.getElementById('include_rent');
    const amenityCheckboxes = document.querySelectorAll('.amenity-checkbox');
    const itemCount = document.getElementById('item-count');
    
    function updateItemCount() {
        let count = 0;
        if (includeRent && includeRent.checked) count++;
        amenityCheckboxes.forEach(cb => {
            if (cb.checked) count++;
        });
        itemCount.textContent = count;
    }
    
    if (includeRent) {
        includeRent.addEventListener('change', updateItemCount);
    }
    
    amenityCheckboxes.forEach(cb => {
        cb.addEventListener('change', updateItemCount);
    });
    
    updateItemCount();
});
</script>

{% endblock %}

