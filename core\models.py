from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify


class Organization(models.Model):
    name = models.CharField(max_length=150, unique=True)
    slug = models.SlugField(max_length=160, unique=True)
    owner = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='owned_orgs')
    created_at = models.DateTimeField(auto_now_add=True)


    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        return super().save(*args, **kwargs)


    def __str__(self):
        return self.name


class OrgScopedModel(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)

    class Meta:
        abstract = True


class Membership(models.Model):
    ROLE_CHOICES = [
    ('ADMIN', 'Admin'),
    ('MANAGER', 'Property Manager'),
    ('AGENT', 'Agent'),
    ('TENANT', 'Tenant'),
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)


    class Meta:
        unique_together = ('user', 'organization')


    def __str__(self):
        return f"{self.user} @ {self.organization} ({self.role})"


class RecurringInvoiceSchedule(OrgScopedModel):
    """Track recurring invoice generation schedules"""
    FREQUENCY_CHOICES = [
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('YEARLY', 'Yearly'),
    ]

    name = models.CharField(max_length=200, default='Monthly Rent Invoices')
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='MONTHLY')
    is_active = models.BooleanField(default=True)

    # Schedule settings
    day_of_month = models.IntegerField(default=1, help_text='Day of month to generate invoices (1-28)')
    due_days = models.IntegerField(default=7, help_text='Number of days until invoice is due')
    include_mandatory_amenities = models.BooleanField(default=True)

    # Tracking
    last_run_date = models.DateField(null=True, blank=True)
    last_run_count = models.IntegerField(default=0, help_text='Number of invoices created in last run')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.organization.name} - {self.name} ({self.get_frequency_display()})"
