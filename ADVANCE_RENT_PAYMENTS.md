# Advance Rent Payments System - Complete Guide

## Overview
The RentalX system now supports **advance rent payments**, allowing tenants to pay rent for multiple months in advance (e.g., 6, 7, or 12 months). The system automatically tracks credit balances and applies them to future invoices.

---

## 🎯 Key Features

### 1. **Flexible Advance Payments**
- ✅ Pay for any number of months (1-24 months)
- ✅ Supports all payment methods (M-Pesa, Cash, Bank Transfer, Cheque, Card)
- ✅ Auto-calculates total amount based on monthly rent
- ✅ Manual amount adjustment supported

### 2. **Automatic Credit Balance Tracking**
- ✅ Each tenant has a credit balance account
- ✅ Automatically updated when advance payments are made
- ✅ Automatically deducted when invoices are paid
- ✅ Real-time balance display

### 3. **Smart Invoice Application**
- ✅ Option to apply to existing unpaid invoices first
- ✅ Remaining amount goes to credit balance
- ✅ Manual application to specific invoices
- ✅ Automatic allocation tracking

### 4. **Complete Audit Trail**
- ✅ All advance payments recorded
- ✅ All credit allocations tracked
- ✅ Full payment history
- ✅ Transparent reporting

---

## 📋 How It Works

### Scenario: Tenant Pays 6 Months Rent in Advance

**Example:**
- Monthly Rent: KES 30,000
- Advance Payment: 6 months = KES 180,000
- Existing Unpaid Invoices: KES 60,000 (2 months)

**What Happens:**

1. **Record Advance Payment**
   - Amount: KES 180,000
   - Months: 6
   - Payment Method: M-Pesa

2. **System Processes:**
   - Applies KES 60,000 to existing unpaid invoices (marks them as paid)
   - Adds remaining KES 120,000 to tenant's credit balance

3. **Future Invoices:**
   - When new invoices are generated, credit balance is automatically applied
   - Tenant doesn't need to pay until credit balance is exhausted

---

## 🚀 Usage Guide

### For Property Managers

#### **1. Recording Advance Payment**

**Step 1:** Navigate to Lease Details
- Go to: Leases → Select Lease → View Details

**Step 2:** Click "Record Advance Payment"
- Button located at top right of lease details page

**Step 3:** Fill in Payment Details
- **Number of Months:** Enter how many months (e.g., 6)
- **Amount:** Auto-calculated, but can be adjusted
- **Payment Method:** Select (M-Pesa, Cash, Bank, Cheque, Card)
- **Reference:** Optional (auto-generated if blank)
- **Apply to Existing:** Check to pay existing unpaid invoices first
- **Notes:** Optional additional information

**Step 4:** Submit
- Click "Record Advance Payment"
- System processes and shows confirmation

**Example:**
```
Months: 6
Amount: KES 180,000
Method: M-Pesa
Apply to Existing: ✓ Checked
```

**Result:**
```
✅ Advance payment of KES 180,000 for 6 month(s) recorded successfully!
   Applied to 2 invoice(s).
   Credit balance: KES 120,000
```

---

#### **2. Viewing Credit Balance**

**Option A: From Lease Details**
- Credit balance card shows current balance
- Click "View Credit Balance" for full details

**Option B: Direct Link**
- Leases → Select Lease → "View Credit Balance" button

**What You'll See:**
- Current credit balance
- All advance payments made
- All credit allocations to invoices
- Summary statistics

---

#### **3. Applying Credit to Invoice**

**Option A: Automatic (Recommended)**
- When recording advance payment, check "Apply to existing invoices"
- System automatically applies to oldest unpaid invoices first

**Option B: Manual Application**
- Go to Invoice Details
- If credit balance available, see "Apply Credit" button
- Click to apply credit to that specific invoice

---

### For Tenants

#### **Making Advance Payment**

**M-Pesa Payment:**
1. Property manager initiates advance payment
2. You receive STK push on your phone
3. Enter M-Pesa PIN to confirm
4. Payment recorded automatically
5. Credit balance updated

**Cash Payment:**
1. Pay cash to property manager
2. Manager records payment in system
3. Receive receipt
4. Credit balance updated

**Bank Transfer:**
1. Transfer amount to property account
2. Provide reference number to manager
3. Manager records payment
4. Credit balance updated

---

## 💡 Use Cases

### **Use Case 1: New Tenant Paying 12 Months Upfront**

**Scenario:**
- New tenant wants to pay full year rent upfront
- Monthly rent: KES 25,000
- Total: KES 300,000

**Process:**
1. Record advance payment for 12 months
2. Amount: KES 300,000
3. No existing invoices to apply to
4. Full amount goes to credit balance

**Result:**
- Credit balance: KES 300,000
- Covers 12 months of rent
- No monthly payments needed for 1 year

---

### **Use Case 2: Tenant with Arrears Paying 6 Months**

**Scenario:**
- Tenant has 2 months unpaid (KES 50,000)
- Wants to pay 6 months advance (KES 150,000)
- Monthly rent: KES 25,000

**Process:**
1. Record advance payment for 6 months
2. Amount: KES 150,000
3. Check "Apply to existing invoices"
4. System applies KES 50,000 to arrears
5. Remaining KES 100,000 to credit balance

**Result:**
- Arrears cleared ✅
- Credit balance: KES 100,000
- Covers 4 more months

---

### **Use Case 3: Partial Advance Payment**

**Scenario:**
- Tenant pays KES 100,000 advance
- Monthly rent: KES 30,000
- Not exactly 3 or 4 months

**Process:**
1. Record advance payment
2. Months: 3 (approximate)
3. Amount: KES 100,000 (manual entry)
4. System accepts any amount

**Result:**
- Credit balance: KES 100,000
- Will cover 3 full months + KES 10,000 towards 4th month

---

## 📊 Reports and Tracking

### **Credit Balance Dashboard**

**Location:** Lease Details → View Credit Balance

**Shows:**
- Current balance (large display)
- Total advance payments made
- Total allocated to invoices
- Remaining balance
- Full transaction history

**Example Display:**
```
┌─────────────────────────────────┐
│   CREDIT BALANCE                │
│   KES 120,000.00                │
│   Covers approximately 4 months │
└─────────────────────────────────┘

Summary Statistics:
- Total Paid in Advance: KES 180,000
- Total Allocated: KES 60,000
- Current Balance: KES 120,000
```

---

### **Advance Payments List**

**Shows:**
- Date of payment
- Reference number
- Payment method
- Number of months
- Amount
- Notes

**Example:**
| Date | Reference | Method | Months | Amount |
|------|-----------|--------|--------|---------|
| Oct 3, 2025 | ADV-SMITH-20251003 | M-Pesa | 6 | KES 180,000 |
| Sep 1, 2025 | ADV-SMITH-20250901 | Cash | 3 | KES 90,000 |

---

### **Credit Allocations List**

**Shows:**
- Allocation date
- Payment reference
- Invoice number
- Amount allocated
- Notes

**Example:**
| Date | Payment Ref | Invoice | Amount |
|------|-------------|---------|---------|
| Oct 3, 2025 | ADV-SMITH-20251003 | INV-2025-10-0001 | KES 30,000 |
| Oct 3, 2025 | ADV-SMITH-20251003 | INV-2025-09-0001 | KES 30,000 |

---

## 🔧 Technical Details

### **Database Models**

#### **1. Payment Model (Enhanced)**
```python
class Payment:
    invoice = ForeignKey(Invoice, null=True, blank=True)  # Can be null for advance payments
    lease = ForeignKey(Lease, null=True, blank=True)  # Direct lease reference
    is_advance_payment = BooleanField(default=False)
    months_covered = IntegerField(default=1)
    notes = TextField(blank=True)
    # ... other fields
```

#### **2. TenantCreditBalance Model**
```python
class TenantCreditBalance:
    lease = OneToOneField(Lease)
    balance = DecimalField(max_digits=10, decimal_places=2)
    last_updated = DateTimeField(auto_now=True)
```

#### **3. AdvancePaymentAllocation Model**
```python
class AdvancePaymentAllocation:
    payment = ForeignKey(Payment)
    invoice = ForeignKey(Invoice)
    amount_allocated = DecimalField(max_digits=10, decimal_places=2)
    allocation_date = DateTimeField(auto_now_add=True)
    notes = TextField(blank=True)
```

---

### **URL Endpoints**

```python
# Record advance payment
/test/leases/<lease_id>/advance-payment/

# View credit balance
/test/leases/<lease_id>/credit-balance/

# Apply credit to invoice
/test/invoices/<invoice_id>/apply-credit/

# Calculate advance payment (AJAX)
/test/api/leases/<lease_id>/calculate-advance/
```

---

## 🎨 User Interface

### **Lease Details Page**

**New Buttons:**
- 🟢 "Record Advance Payment" - Opens advance payment form
- 🔵 "View Credit Balance" - Shows full credit balance details

**Credit Balance Card:**
- Shows current balance
- Indicates months covered
- Quick action buttons

---

### **Invoice Details Page**

**New Features:**
- 🔵 "Apply Credit" button (if credit available)
- ℹ️ Credit balance alert (if balance > 0)
- Shows if invoice was paid with credit

---

### **Advance Payment Form**

**Features:**
- Auto-calculation based on months
- All payment methods supported
- Option to apply to existing invoices
- Real-time summary
- Visual feedback

---

## ✅ Benefits

### **For Property Managers:**
- ✅ **Improved Cash Flow** - Receive multiple months rent upfront
- ✅ **Reduced Collection Effort** - Less monthly follow-ups
- ✅ **Better Tenant Retention** - Tenants committed for longer periods
- ✅ **Automated Tracking** - No manual spreadsheets needed
- ✅ **Complete Audit Trail** - All transactions recorded

### **For Tenants:**
- ✅ **Convenience** - Pay once, forget for months
- ✅ **Discounts** - Potential for advance payment discounts
- ✅ **Peace of Mind** - No monthly payment stress
- ✅ **Transparent** - Clear view of credit balance
- ✅ **Flexible** - Can pay any number of months

---

## 🔐 Security & Validation

### **Validations:**
- ✅ Amount must be greater than zero
- ✅ Months must be at least 1
- ✅ Payment method required
- ✅ Lease must be active
- ✅ Credit balance cannot go negative

### **Audit Trail:**
- ✅ All payments logged with timestamp
- ✅ All allocations tracked
- ✅ User who recorded payment tracked
- ✅ Cannot delete payments (data integrity)

---

## 📈 Future Enhancements

### **Planned Features:**
- 🔜 Advance payment discounts (e.g., 5% off for 12 months)
- 🔜 Automatic invoice generation with credit application
- 🔜 Email notifications when credit balance low
- 🔜 Bulk advance payment recording
- 🔜 Credit balance transfer between tenants
- 🔜 Advance payment reports and analytics

---

## 🆘 Troubleshooting

### **Issue: Credit not applied to invoice**
**Solution:** 
- Check if "Apply to existing invoices" was checked
- Manually apply credit from invoice details page
- Verify credit balance is sufficient

### **Issue: Wrong amount calculated**
**Solution:**
- Manually adjust amount in form
- System accepts any amount, not just exact multiples

### **Issue: Cannot see credit balance**
**Solution:**
- Credit balance is created automatically on first advance payment
- Check lease details page for credit balance card

---

## 📞 Support

For questions or issues:
1. Check this documentation
2. View credit balance details for transaction history
3. Contact system administrator

---

**Implementation Date:** October 3, 2025  
**Status:** ✅ Complete and Active  
**Version:** 1.0  
**Developer:** Augment Agent

