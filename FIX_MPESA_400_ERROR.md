# Quick Fix: M-Pesa 400 Bad Request Error

## 🚨 The Problem
```
M-Pesa payment failed: STK Push request failed: 400 Client Error: Bad Request
```

## ✅ The Solution (5 Minutes)

### Step 1: Install ngrok
Download and install ngrok from: https://ngrok.com/download

### Step 2: Start ngrok
Open a **new terminal** and run:
```bash
ngrok http 8000
```

You'll see output like:
```
Forwarding  https://abc123.ngrok.io -> http://localhost:8000
```

**Copy the HTTPS URL** (e.g., `https://abc123.ngrok.io`)

⚠️ **Keep this terminal open!** If you close it, the tunnel stops.

---

### Step 3: Update M-Pesa Configuration

**Option A: Using Django Admin (Easiest)**

1. Go to: http://127.0.0.1:8000/admin/
2. Login with your admin credentials
3. Click on **"Mpesa configurations"**
4. Click on your configuration (for organization "test")
5. Update these fields:

```
Business short code: 174379
Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
Callback url: https://abc123.ngrok.io/payments/callback/
                ↑↑↑ Replace with YOUR ngrok URL
Is sandbox: ✓ (checked)
Is active: ✓ (checked)
```

6. Click **"Save"**

---

**Option B: Using Django Shell**

```bash
python manage.py shell
```

Then paste:
```python
from payments.models import MpesaConfiguration
from core.models import Organization

org = Organization.objects.get(slug='test')
config = MpesaConfiguration.objects.get(organization=org)

# Update with YOUR ngrok URL
config.callback_url = "https://abc123.ngrok.io/payments/callback/"  # ← Change this!
config.business_short_code = "174379"
config.passkey = "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"
config.is_sandbox = True
config.is_active = True
config.save()

print("✅ Configuration updated!")
print(f"Callback URL: {config.callback_url}")
```

Press `Ctrl+D` to exit shell.

---

### Step 4: Verify Configuration

Run the diagnostic script:
```bash
python check_mpesa_config.py
```

You should see:
```
✅ Organization found: Test Organization
✅ M-Pesa configuration found
✅ All configuration fields are valid!
✅ Using HTTPS
✅ Not using localhost
✅ Access token generated successfully!
✅ All checks passed!
```

---

### Step 5: Test Payment

1. Go to an unpaid invoice
2. Click **"Quick Pay"** or **"Pay"** button
3. Select **M-Pesa**
4. Enter test phone number: `************`
5. Click **"Pay Now"**

You should see:
```
✅ Payment request sent to ************. Please enter your M-Pesa PIN to complete the payment.
```

---

## 🎯 Quick Checklist

Before testing payment, verify:

- [ ] ngrok is running in a separate terminal
- [ ] Callback URL is `https://YOUR-ID.ngrok.io/payments/callback/`
- [ ] Business Short Code is `174379`
- [ ] Passkey is the sandbox passkey (starts with `bfb279f9`)
- [ ] `is_sandbox` is checked/True
- [ ] `is_active` is checked/True
- [ ] Django server is running (`python manage.py runserver`)

---

## 🔍 Still Getting 400 Error?

### Check Django Logs

Look at your Django terminal for detailed error messages:
```
INFO - Response Status Code: 400
INFO - Response Body: {"errorCode": "...", "errorMessage": "..."}
```

The `errorMessage` will tell you exactly what's wrong!

### Common Issues:

**1. "Invalid callback URL"**
- ✅ Make sure callback URL starts with `https://`
- ✅ Make sure it's your ngrok URL, not localhost
- ✅ Make sure ngrok is still running

**2. "Invalid business short code"**
- ✅ Use `174379` for sandbox
- ✅ Check for typos

**3. "Invalid passkey"**
- ✅ Use the full sandbox passkey (64 characters)
- ✅ No extra spaces

**4. "Invalid phone number"**
- ✅ Use format: `************`
- ✅ Don't use: `**********` or `+************`

---

## 📱 Test Phone Numbers (Sandbox)

Use these for testing:
- `************`
- `************`
- `************`

---

## 🔄 Every Time You Restart

**Important:** ngrok URLs change every time you restart ngrok (free version).

So every time you:
1. Restart ngrok
2. Get a new URL (e.g., `https://xyz789.ngrok.io`)
3. Update callback URL in M-Pesa configuration
4. Test payment again

**To avoid this:** Get a free ngrok account for a permanent URL.

---

## 🚀 Production Setup

For production (real payments):

1. **Get Production Credentials:**
   - Apply for Go-Live on Safaricom Daraja Portal
   - Get your production consumer key/secret
   - Get your actual business short code (paybill/till number)
   - Get your production passkey

2. **Deploy to Production Server:**
   - Use a real domain (e.g., `https://rentalx.example.com`)
   - Update callback URL: `https://rentalx.example.com/payments/callback/`
   - Set `is_sandbox = False`

3. **Whitelist Your IP:**
   - Contact Safaricom to whitelist your server IP

---

## 📞 Need More Help?

1. **Run diagnostic script:**
   ```bash
   python check_mpesa_config.py
   ```

2. **Check detailed guide:**
   - See `MPESA_TROUBLESHOOTING.md`

3. **Check Django logs:**
   - Look for detailed error messages in terminal

4. **Verify ngrok is running:**
   - Open `http://127.0.0.1:4040` to see ngrok dashboard

---

## ✨ Summary

**The main issue:** M-Pesa cannot reach `localhost` or `127.0.0.1`

**The solution:** Use ngrok to create a public HTTPS URL

**The steps:**
1. Start ngrok: `ngrok http 8000`
2. Copy HTTPS URL
3. Update callback URL in M-Pesa config
4. Test payment

**That's it!** 🎉

---

**Last Updated:** October 3, 2025  
**Status:** Active  
**Version:** 1.0

