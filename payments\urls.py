from django.urls import path
from . import views

urlpatterns = [
    # Payment initiation
    path('invoice/<int:invoice_id>/pay/', views.initiate_payment, name='initiate_payment'),
    path('invoice/<int:invoice_id>/options/', views.invoice_payment_options, name='invoice_payment_options'),
    
    # Payment status and tracking
    path('transaction/<int:transaction_id>/status/', views.payment_status, name='payment_status'),
    path('transaction/<int:transaction_id>/check/', views.check_payment_status, name='check_payment_status'),
    
    # Payment history
    path('history/', views.payment_history, name='payment_history'),
    
    # Tenant payment interface
    path('tenant/', views.TenantPaymentView.as_view(), name='tenant_payment'),
    
    # M-Pesa callback (no org_slug needed)
    path('callback/', views.mpesa_callback, name='mpesa_callback'),
]
