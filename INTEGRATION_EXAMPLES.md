# M-Pesa Integration Examples

This document provides practical examples of how to integrate M-Pesa payments into your existing RentalX templates and views.

## 1. Adding Payment Buttons to Invoice List

### Update your invoice list template

```html
<!-- In templates/billing/invoice_list.html -->
{% extends 'base.html' %}
{% load org_urls %}

{% block content %}
<div class="table-responsive">
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Tenant</th>
                <th>Amount Due</th>
                <th>Amount Paid</th>
                <th>Balance</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for invoice in invoices %}
            <tr>
                <td>{{ invoice.number }}</td>
                <td>{{ invoice.lease.tenant.first_name }} {{ invoice.lease.tenant.last_name }}</td>
                <td>KES {{ invoice.amount_due|floatformat:2 }}</td>
                <td>KES {{ invoice.amount_paid|floatformat:2 }}</td>
                <td>
                    {% if invoice.balance > 0 %}
                        <strong class="text-danger">KES {{ invoice.balance|floatformat:2 }}</strong>
                    {% else %}
                        <span class="text-success">KES 0.00</span>
                    {% endif %}
                </td>
                <td>
                    {% if invoice.is_paid %}
                        <span class="badge badge-success">Paid</span>
                    {% else %}
                        <span class="badge badge-warning">Unpaid</span>
                    {% endif %}
                </td>
                <td>
                    <!-- Include the payment button component -->
                    {% include 'payments/invoice_payment_button.html' %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
```

## 2. Adding M-Pesa Configuration to Admin

### Create a simple admin view for M-Pesa setup

```python
# In core/admin_views.py (create this file)
from django.shortcuts import render, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from payments.models import MpesaConfiguration
from payments.forms import MpesaConfigurationForm

@staff_member_required
def mpesa_config_view(request, org_id):
    organization = get_object_or_404(Organization, id=org_id)
    
    try:
        config = MpesaConfiguration.objects.get(organization=organization)
    except MpesaConfiguration.DoesNotExist:
        config = None
    
    if request.method == 'POST':
        form = MpesaConfigurationForm(request.POST)
        if form.is_valid():
            if config:
                # Update existing
                for field, value in form.cleaned_data.items():
                    setattr(config, field, value)
                config.save()
            else:
                # Create new
                config = MpesaConfiguration.objects.create(
                    organization=organization,
                    **form.cleaned_data
                )
            
            messages.success(request, 'M-Pesa configuration saved successfully!')
            return redirect('admin:index')
    else:
        initial_data = {}
        if config:
            initial_data = {
                'consumer_key': config.consumer_key,
                'business_short_code': config.business_short_code,
                'callback_url': config.callback_url,
                'is_sandbox': config.is_sandbox,
            }
        form = MpesaConfigurationForm(initial=initial_data)
    
    return render(request, 'admin/mpesa_config.html', {
        'form': form,
        'organization': organization,
        'config': config
    })
```

## 3. Custom Payment Processing

### Create a custom payment view

```python
# In your views.py
from payments.services import MpesaService, MpesaAPIException
from django.http import JsonResponse

def process_bulk_payment(request, org_slug):
    """Process payment for multiple invoices"""
    if request.method == 'POST':
        invoice_ids = request.POST.getlist('invoice_ids')
        phone_number = request.POST.get('phone_number')
        
        invoices = Invoice.objects.filter(
            id__in=invoice_ids,
            organization=request.org,
            is_paid=False
        )
        
        total_amount = sum(invoice.balance for invoice in invoices)
        
        try:
            mpesa_service = MpesaService(request.org)
            
            # Create a combined reference
            invoice_numbers = [inv.number for inv in invoices]
            reference = f"BULK-{'-'.join(invoice_numbers[:3])}"
            
            # For bulk payments, you might want to create a separate model
            # or handle it differently based on your business logic
            
            transaction, response = mpesa_service.initiate_stk_push(
                phone_number=phone_number,
                amount=total_amount,
                invoice=invoices.first(),  # Use first invoice as primary
                account_reference=reference
            )
            
            return JsonResponse({
                'success': True,
                'transaction_id': transaction.id,
                'message': 'Payment request sent to your phone'
            })
            
        except MpesaAPIException as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            })
    
    return JsonResponse({'success': False, 'error': 'Invalid request'})
```

## 4. Webhook/Callback Handling

### Custom callback processing

```python
# In payments/services.py - extend MpesaCallbackHandler

class CustomMpesaCallbackHandler(MpesaCallbackHandler):
    
    @staticmethod
    def process_callback(callback_data):
        """Extended callback processing with custom business logic"""
        
        # Call parent method first
        success = MpesaCallbackHandler.process_callback(callback_data)
        
        if success:
            # Add custom processing
            try:
                stk_callback = callback_data.get('Body', {}).get('stkCallback', {})
                checkout_request_id = stk_callback.get('CheckoutRequestID')
                
                transaction = MpesaTransaction.objects.get(
                    checkout_request_id=checkout_request_id
                )
                
                if transaction.is_successful:
                    # Send notification email
                    send_payment_confirmation_email(transaction)
                    
                    # Update tenant payment history
                    update_tenant_payment_history(transaction)
                    
                    # Check if tenant has other overdue invoices
                    check_overdue_invoices(transaction.invoice.lease.tenant)
                
            except Exception as e:
                logger.error(f"Custom callback processing failed: {e}")
        
        return success

def send_payment_confirmation_email(transaction):
    """Send email confirmation to tenant"""
    from django.core.mail import send_mail
    
    tenant = transaction.invoice.lease.tenant
    subject = f'Payment Confirmation - Invoice {transaction.invoice.number}'
    message = f'''
    Dear {tenant.first_name},
    
    Your payment of KES {transaction.amount} for invoice {transaction.invoice.number} 
    has been received successfully.
    
    M-Pesa Receipt: {transaction.mpesa_receipt_number}
    
    Thank you for your payment.
    '''
    
    send_mail(
        subject,
        message,
        '<EMAIL>',
        [tenant.email],
        fail_silently=True
    )
```

## 5. Frontend JavaScript Integration

### Real-time payment status updates

```javascript
// Add to your base template or payment pages
class PaymentStatusChecker {
    constructor(transactionId, orgSlug) {
        this.transactionId = transactionId;
        this.orgSlug = orgSlug;
        this.checkInterval = null;
        this.maxChecks = 60; // Check for 5 minutes (60 * 5 seconds)
        this.checkCount = 0;
    }
    
    start() {
        this.checkInterval = setInterval(() => {
            this.checkStatus();
        }, 5000);
        
        // Stop after max checks
        setTimeout(() => {
            this.stop();
        }, this.maxChecks * 5000);
    }
    
    stop() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }
    
    async checkStatus() {
        try {
            const response = await fetch(
                `/${this.orgSlug}/payments/transaction/${this.transactionId}/check/`
            );
            const data = await response.json();
            
            if (data.is_successful) {
                this.onSuccess(data);
                this.stop();
            } else if (data.is_failed) {
                this.onFailure(data);
                this.stop();
            }
            
            this.checkCount++;
            if (this.checkCount >= this.maxChecks) {
                this.onTimeout();
                this.stop();
            }
            
        } catch (error) {
            console.error('Error checking payment status:', error);
        }
    }
    
    onSuccess(data) {
        // Show success message
        this.showNotification('Payment successful!', 'success');
        // Redirect or update UI
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
    
    onFailure(data) {
        this.showNotification(`Payment failed: ${data.result_desc}`, 'error');
    }
    
    onTimeout() {
        this.showNotification('Payment status check timed out. Please refresh to check status.', 'warning');
    }
    
    showNotification(message, type) {
        // Implement your notification system
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-warning';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// Usage
function initiatePaymentTracking(transactionId, orgSlug) {
    const checker = new PaymentStatusChecker(transactionId, orgSlug);
    checker.start();
}
```

## 6. Environment-Specific Configuration

### Development settings

```python
# In settings/development.py
MPESA_ENVIRONMENT = 'sandbox'
MPESA_CONSUMER_KEY = 'your_sandbox_consumer_key'
MPESA_CONSUMER_SECRET = 'your_sandbox_consumer_secret'
MPESA_BUSINESS_SHORTCODE = '174379'  # Sandbox shortcode
MPESA_PASSKEY = 'your_sandbox_passkey'
MPESA_CALLBACK_URL = 'https://your-ngrok-url.ngrok.io/payments/callback/'
```

### Production settings

```python
# In settings/production.py
import os

MPESA_ENVIRONMENT = 'production'
MPESA_CONSUMER_KEY = os.environ.get('MPESA_CONSUMER_KEY')
MPESA_CONSUMER_SECRET = os.environ.get('MPESA_CONSUMER_SECRET')
MPESA_BUSINESS_SHORTCODE = os.environ.get('MPESA_BUSINESS_SHORTCODE')
MPESA_PASSKEY = os.environ.get('MPESA_PASSKEY')
MPESA_CALLBACK_URL = os.environ.get('MPESA_CALLBACK_URL')

# Ensure all required settings are present
required_mpesa_settings = [
    'MPESA_CONSUMER_KEY', 'MPESA_CONSUMER_SECRET', 
    'MPESA_BUSINESS_SHORTCODE', 'MPESA_PASSKEY', 'MPESA_CALLBACK_URL'
]

for setting in required_mpesa_settings:
    if not globals().get(setting):
        raise ValueError(f'{setting} environment variable is required')
```

## 7. Testing

### Unit tests for M-Pesa integration

```python
# In payments/tests.py
from django.test import TestCase
from unittest.mock import patch, Mock
from payments.services import MpesaService, MpesaAPIException
from payments.models import MpesaConfiguration, MpesaTransaction

class MpesaServiceTest(TestCase):
    def setUp(self):
        self.organization = Organization.objects.create(name='Test Org')
        self.config = MpesaConfiguration.objects.create(
            organization=self.organization,
            consumer_key='test_key',
            consumer_secret='test_secret',
            business_short_code='174379',
            passkey='test_passkey',
            callback_url='https://test.com/callback/',
            is_sandbox=True
        )
        self.service = MpesaService(self.organization)
    
    @patch('payments.services.requests.get')
    def test_get_access_token_success(self, mock_get):
        mock_response = Mock()
        mock_response.json.return_value = {'access_token': 'test_token'}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        token = self.service.get_access_token()
        self.assertEqual(token, 'test_token')
    
    @patch('payments.services.requests.post')
    @patch('payments.services.requests.get')
    def test_initiate_stk_push_success(self, mock_get, mock_post):
        # Mock access token request
        mock_get_response = Mock()
        mock_get_response.json.return_value = {'access_token': 'test_token'}
        mock_get.return_value = mock_get_response
        
        # Mock STK push request
        mock_post_response = Mock()
        mock_post_response.json.return_value = {
            'ResponseCode': '0',
            'CheckoutRequestID': 'test_checkout_id',
            'MerchantRequestID': 'test_merchant_id'
        }
        mock_post.return_value = mock_post_response
        
        invoice = Invoice.objects.create(
            organization=self.organization,
            number='INV001',
            amount_due=1000
        )
        
        transaction, response = self.service.initiate_stk_push(
            phone_number='************',
            amount=1000,
            invoice=invoice
        )
        
        self.assertEqual(transaction.checkout_request_id, 'test_checkout_id')
        self.assertEqual(transaction.status, 'PENDING')
```

This integration guide provides comprehensive examples for implementing M-Pesa payments in your RentalX application. Each example can be adapted to your specific needs and business requirements.
