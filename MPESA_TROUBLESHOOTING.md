# M-Pesa Integration Troubleshooting Guide

## Common Error: 400 Bad Request

### Error Message:
```
M-Pesa payment failed: STK Push request failed: 400 Client Error: Bad Request
```

---

## 🔍 Root Causes & Solutions

### 1. **Invalid Callback URL**

**Problem:** <PERSON><PERSON><PERSON><PERSON><PERSON> cannot reach localhost URLs

**Symptoms:**
- 400 Bad Request error
- Error message: "Invalid callback URL"

**Solution:**

#### For Development/Testing:
Use a tunneling service to expose your local server:

**Option A: ngrok (Recommended)**
```bash
# Install ngrok from https://ngrok.com/download
ngrok http 8000

# You'll get a URL like: https://abc123.ngrok.io
# Use this as your callback URL: https://abc123.ngrok.io/payments/callback/
```

**Option B: Cloudflare Tunnel (Free)**
```bash
# Install cloudflared
# Windows: Download from https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/

cloudflared tunnel --url http://localhost:8000

# You'll get: https://random-name.trycloudflare.com
# Callback URL: https://random-name.trycloudflare.com/payments/callback/
```

**Option C: localtunnel**
```bash
npm install -g localtunnel
lt --port 8000

# You'll get: https://your-subdomain.loca.lt
# Callback URL: https://your-subdomain.loca.lt/payments/callback/
```

#### Update M-Pesa Configuration:
```python
# In Django shell or admin
from payments.models import MpesaConfiguration
from core.models import Organization

org = Organization.objects.get(slug='test')
config = MpesaConfiguration.objects.get(organization=org)
config.callback_url = "https://your-tunnel-url.ngrok.io/payments/callback/"
config.save()
```

---

### 2. **Invalid Business Short Code**

**Problem:** Wrong or test short code format

**Symptoms:**
- 400 Bad Request
- Error: "Invalid business short code"

**Solution:**

For **Sandbox Testing**, use:
```
Business Short Code: 174379
```

**Update Configuration:**
```python
config.business_short_code = "174379"
config.save()
```

---

### 3. **Invalid Passkey**

**Problem:** Wrong passkey for the business short code

**Symptoms:**
- 400 Bad Request
- Authentication errors

**Solution:**

For **Sandbox**, the passkey is:
```
Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
```

**Update Configuration:**
```python
config.passkey = "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"
config.save()
```

---

### 4. **Invalid Phone Number Format**

**Problem:** Phone number not in correct format

**Symptoms:**
- 400 Bad Request
- Error: "Invalid phone number"

**Valid Formats:**
- ✅ `254712345678` (Correct)
- ✅ `0712345678` (Auto-converted to 254712345678)
- ✅ `+254712345678` (Auto-converted to 254712345678)
- ❌ `712345678` (Missing country code)

**For Testing (Sandbox):**
Use test phone numbers:
- `254708374149`
- `254711111111`

---

### 5. **Invalid Amount**

**Problem:** Amount is zero, negative, or too large

**Symptoms:**
- 400 Bad Request
- Error: "Invalid amount"

**Valid Amount:**
- Minimum: KES 1
- Maximum: KES 150,000 (for sandbox)
- Must be a positive integer

**Check:**
```python
# Amount must be > 0
amount = invoice.balance
if amount <= 0:
    # Error!
```

---

### 6. **Expired or Invalid Access Token**

**Problem:** OAuth token expired or invalid credentials

**Symptoms:**
- 401 Unauthorized
- 400 Bad Request

**Solution:**

Check your credentials:
```python
config.consumer_key = "your_consumer_key"
config.consumer_secret = "your_consumer_secret"
config.save()
```

**For Sandbox:**
Get credentials from: https://developer.safaricom.co.ke/

---

## 🧪 Testing Configuration

### Step 1: Verify M-Pesa Configuration

Run this in Django shell:
```python
from payments.models import MpesaConfiguration
from core.models import Organization

org = Organization.objects.get(slug='test')
config = MpesaConfiguration.objects.get(organization=org)

print(f"Business Short Code: {config.business_short_code}")
print(f"Callback URL: {config.callback_url}")
print(f"Is Sandbox: {config.is_sandbox}")
print(f"Is Active: {config.is_active}")
```

**Expected Output (Sandbox):**
```
Business Short Code: 174379
Callback URL: https://your-tunnel.ngrok.io/payments/callback/
Is Sandbox: True
Is Active: True
```

---

### Step 2: Test Access Token

```python
from payments.services import MpesaService

org = Organization.objects.get(slug='test')
service = MpesaService(org)

try:
    token = service.get_access_token()
    print(f"✅ Access Token: {token[:20]}...")
except Exception as e:
    print(f"❌ Error: {e}")
```

---

### Step 3: Test STK Push

```python
from rentals.models import Invoice

invoice = Invoice.objects.first()
phone = "254708374149"  # Test number

try:
    transaction, response = service.initiate_stk_push(
        phone_number=phone,
        amount=10,  # Test with small amount
        invoice=invoice
    )
    print(f"✅ STK Push successful!")
    print(f"Checkout Request ID: {transaction.checkout_request_id}")
except Exception as e:
    print(f"❌ Error: {e}")
```

---

## 📋 Complete Sandbox Configuration Checklist

### ✅ Required Settings:

```python
# M-Pesa Sandbox Configuration
consumer_key = "YOUR_CONSUMER_KEY"  # From Daraja Portal
consumer_secret = "YOUR_CONSUMER_SECRET"  # From Daraja Portal
business_short_code = "174379"  # Sandbox short code
passkey = "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"
callback_url = "https://your-tunnel.ngrok.io/payments/callback/"  # Must be HTTPS
is_sandbox = True
is_active = True
```

---

## 🔧 Quick Fix Script

Create a file `fix_mpesa_config.py` in your project root:

```python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentalx.settings')
django.setup()

from payments.models import MpesaConfiguration
from core.models import Organization

# Get organization
org = Organization.objects.get(slug='test')

# Update or create configuration
config, created = MpesaConfiguration.objects.update_or_create(
    organization=org,
    defaults={
        'consumer_key': 'YOUR_CONSUMER_KEY',  # Replace with your key
        'consumer_secret': 'YOUR_CONSUMER_SECRET',  # Replace with your secret
        'business_short_code': '174379',
        'passkey': 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',
        'callback_url': 'https://your-tunnel.ngrok.io/payments/callback/',  # Update this!
        'is_sandbox': True,
        'is_active': True,
    }
)

print(f"✅ Configuration {'created' if created else 'updated'} successfully!")
print(f"Business Short Code: {config.business_short_code}")
print(f"Callback URL: {config.callback_url}")
print(f"⚠️  Remember to update callback_url with your ngrok/tunnel URL!")
```

Run it:
```bash
python fix_mpesa_config.py
```

---

## 🌐 Setting Up ngrok (Recommended)

### Step 1: Install ngrok
Download from: https://ngrok.com/download

### Step 2: Start ngrok
```bash
ngrok http 8000
```

### Step 3: Copy HTTPS URL
You'll see output like:
```
Forwarding  https://abc123.ngrok.io -> http://localhost:8000
```

### Step 4: Update Callback URL
```python
config.callback_url = "https://abc123.ngrok.io/payments/callback/"
config.save()
```

### Step 5: Test Payment
Try making a payment - it should now work!

---

## 📊 Debugging Checklist

When you get a 400 error, check:

- [ ] Is callback URL HTTPS? (not HTTP)
- [ ] Is callback URL publicly accessible? (not localhost)
- [ ] Is business short code correct? (174379 for sandbox)
- [ ] Is passkey correct?
- [ ] Is phone number in correct format? (254XXXXXXXXX)
- [ ] Is amount > 0 and < 150000?
- [ ] Are consumer key and secret correct?
- [ ] Is M-Pesa configuration active?
- [ ] Is Django server running?
- [ ] Is ngrok/tunnel running?

---

## 🔍 View Detailed Error Logs

Check the Django logs for detailed error information:

```bash
# In your terminal where Django is running
# You should see logs like:

INFO - Sending STK Push request to: https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest
INFO - Payload: {...}
INFO - Response Status Code: 400
INFO - Response Body: {"errorCode": "...", "errorMessage": "..."}
```

The `errorMessage` will tell you exactly what's wrong!

---

## 🎯 Common Error Messages & Solutions

| Error Message | Cause | Solution |
|---------------|-------|----------|
| "Invalid callback URL" | Callback is localhost or HTTP | Use ngrok with HTTPS |
| "Invalid business short code" | Wrong short code | Use 174379 for sandbox |
| "Invalid passkey" | Wrong passkey | Use sandbox passkey |
| "Invalid phone number" | Wrong format | Use 254XXXXXXXXX format |
| "Invalid amount" | Amount is 0 or negative | Check invoice balance |
| "Unauthorized" | Wrong credentials | Check consumer key/secret |
| "Bad Request" | Multiple issues | Check all settings above |

---

## 🚀 Production Setup

For production (not sandbox):

1. **Get Production Credentials:**
   - Apply for Go-Live on Daraja Portal
   - Get production consumer key/secret
   - Get your actual business short code
   - Get your production passkey

2. **Update Configuration:**
```python
config.consumer_key = "PRODUCTION_KEY"
config.consumer_secret = "PRODUCTION_SECRET"
config.business_short_code = "YOUR_SHORTCODE"  # Your actual paybill/till
config.passkey = "YOUR_PRODUCTION_PASSKEY"
config.callback_url = "https://yourdomain.com/payments/callback/"
config.is_sandbox = False
config.save()
```

3. **Whitelist Your IP:**
   - Contact Safaricom to whitelist your server IP
   - Provide your production callback URL

---

## 📞 Need Help?

1. Check Django logs for detailed error messages
2. Verify all configuration settings
3. Test with sandbox credentials first
4. Use ngrok for local testing
5. Check Safaricom Daraja documentation: https://developer.safaricom.co.ke/

---

**Last Updated:** October 3, 2025  
**Status:** Active  
**Version:** 1.0

