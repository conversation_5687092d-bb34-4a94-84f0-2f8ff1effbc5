{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Units{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">

<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Units</h1>
    <a href="{% org_url 'unit_create' %}" class="btn btn-sm btn-primary">Add Unit</a>
</div>
<div class="table-responsive">
<table class="table table-striped">
<thead><tr><th>Code</th><th>Property</th><th>Bedrooms</th><th>Bathrooms</th><th>Active</th><th>Actions</th></tr></thead>
<tbody>
{% for u in items %}
<tr>
<td>{{ u.code }}</td>
<td>{{ u.property.name }}</td>
<td>{{ u.bedrooms }}</td>
<td>{{ u.bathrooms }}</td>
<td>{% if u.is_active %}<span class="badge bg-success">Yes</span>{% else %}<span class="badge bg-secondary">No</span>{% endif %}</td>
<td>
    <div class="btn-group btn-group-sm">
        <a href="{% org_url 'unit_edit' pk=u.pk %}" class="btn btn-outline-primary">Edit</a>
        <a href="{% org_url 'unit_delete' pk=u.pk %}" class="btn btn-outline-danger">Delete</a>
    </div>
</td>
</tr>
{% empty %}
<tr><td colspan="6" class="text-center">No units yet.</td></tr>
{% endfor %}
</tbody>
</table>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
{% include 'footer.html' %}
{% endblock %}