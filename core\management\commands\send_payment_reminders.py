"""
Django management command to send payment reminders for overdue invoices.
Run this command daily (e.g., via cron job or task scheduler) to automatically
send reminders to tenants with overdue invoices.

Usage:
    python manage.py send_payment_reminders
    python manage.py send_payment_reminders --organization=test
    python manage.py send_payment_reminders --dry-run
    python manage.py send_payment_reminders --days-overdue=3
"""
from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import date, timedelta

from core.models import Organization
from rentals.models import Invoice


class Command(BaseCommand):
    help = 'Send payment reminders for overdue invoices'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Send reminders for a specific organization (slug)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate sending reminders without actually sending emails',
        )
        parser.add_argument(
            '--days-overdue',
            type=int,
            default=0,
            help='Minimum days overdue to send reminder (default: 0 = all overdue)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        org_slug = options['organization']
        days_overdue = options['days_overdue']

        self.stdout.write(self.style.SUCCESS('=' * 70))
        self.stdout.write(self.style.SUCCESS('PAYMENT REMINDER SYSTEM'))
        self.stdout.write(self.style.SUCCESS('=' * 70))
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No emails will be sent'))
        
        # Get organizations to process
        if org_slug:
            organizations = Organization.objects.filter(slug=org_slug)
            if not organizations.exists():
                self.stdout.write(self.style.ERROR(f'Organization "{org_slug}" not found'))
                return
        else:
            organizations = Organization.objects.all()
        
        total_sent = 0
        total_skipped = 0
        total_errors = 0
        
        for org in organizations:
            self.stdout.write(f'\nProcessing organization: {org.name} ({org.slug})')
            self.stdout.write('-' * 70)
            
            sent, skipped, errors = self.send_reminders_for_org(org, dry_run, days_overdue)
            total_sent += sent
            total_skipped += skipped
            total_errors += errors
        
        # Summary
        self.stdout.write('\n' + '=' * 70)
        self.stdout.write(self.style.SUCCESS('SUMMARY'))
        self.stdout.write('=' * 70)
        self.stdout.write(f'Total reminders sent: {self.style.SUCCESS(total_sent)}')
        self.stdout.write(f'Total invoices skipped: {self.style.WARNING(total_skipped)}')
        self.stdout.write(f'Total errors: {self.style.ERROR(total_errors)}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\nDRY RUN - No emails were actually sent'))
        else:
            self.stdout.write(self.style.SUCCESS('\n✓ Payment reminders sent!'))

    def send_reminders_for_org(self, organization, dry_run, days_overdue):
        """Send payment reminders for overdue invoices in an organization"""
        sent_count = 0
        skipped_count = 0
        error_count = 0
        
        # Calculate cutoff date
        cutoff_date = date.today() - timedelta(days=days_overdue)
        
        # Get all overdue unpaid invoices
        overdue_invoices = Invoice.objects.filter(
            organization=organization,
            is_paid=False,
            due_date__lt=cutoff_date
        ).select_related('lease', 'lease__tenant', 'lease__unit')
        
        if not overdue_invoices.exists():
            self.stdout.write(self.style.SUCCESS('  No overdue invoices found'))
            return 0, 0, 0
        
        self.stdout.write(f'  Found {overdue_invoices.count()} overdue invoice(s)')
        
        for invoice in overdue_invoices:
            try:
                tenant = invoice.lease.tenant
                days_past_due = (date.today() - invoice.due_date).days
                
                # Check if tenant has email
                if not tenant.email:
                    self.stdout.write(f'  ⊘ Skipped: {invoice.number} - {tenant} (No email address)')
                    skipped_count += 1
                    continue
                
                if dry_run:
                    self.stdout.write(
                        f'  ✓ Would send: {invoice.number} - {tenant} ({tenant.email}) - '
                        f'KES {invoice.balance:,.2f} ({days_past_due} days overdue)'
                    )
                    sent_count += 1
                    continue
                
                # Prepare email
                subject = f'Payment Reminder: Invoice {invoice.number} is Overdue'
                
                message = f"""
Dear {tenant.first_name} {tenant.last_name},

This is a friendly reminder that your invoice is now overdue.

Invoice Details:
- Invoice Number: {invoice.number}
- Unit: {invoice.lease.unit}
- Issue Date: {invoice.issue_date.strftime('%B %d, %Y')}
- Due Date: {invoice.due_date.strftime('%B %d, %Y')}
- Days Overdue: {days_past_due} days

Amount Due: KES {invoice.amount_due:,.2f}
Amount Paid: KES {invoice.amount_paid:,.2f}
Balance: KES {invoice.balance:,.2f}

Please make payment at your earliest convenience to avoid late fees.

Payment Methods:
- M-Pesa: [Payment instructions]
- Bank Transfer: [Bank details]
- Cash: Visit our office

If you have already made payment, please disregard this message.

Thank you for your prompt attention to this matter.

Best regards,
{organization.name}
                """.strip()
                
                # Send email
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[tenant.email],
                    fail_silently=False,
                )
                
                self.stdout.write(self.style.SUCCESS(
                    f'  ✓ Sent: {invoice.number} - {tenant} ({tenant.email}) - '
                    f'KES {invoice.balance:,.2f} ({days_past_due} days overdue)'
                ))
                sent_count += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f'  ✗ Error: {invoice.number} - {tenant} - {str(e)}'
                ))
                error_count += 1
        
        return sent_count, skipped_count, error_count

