{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-calendar-plus"></i> Record Advance Payment</h2>
                <a href="{% org_url 'lease_detail' pk=lease.pk %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Lease
                </a>
            </div>

            <div class="row">
                <!-- Lease Summary Card -->
                <div class="col-md-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-file-contract"></i> Lease Summary</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <th>Tenant:</th>
                                    <td><strong>{{ lease.tenant }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Unit:</th>
                                    <td>{{ lease.unit }}</td>
                                </tr>
                                <tr>
                                    <th>Monthly Rent:</th>
                                    <td><strong class="text-success">KES {{ monthly_rent|floatformat:2 }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Current Credit:</th>
                                    <td><strong class="text-info">KES {{ credit_balance|floatformat:2 }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Unpaid Invoices Card -->
                    {% if unpaid_invoices %}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Unpaid Invoices</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Invoice</th>
                                        <th>Due Date</th>
                                        <th>Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in unpaid_invoices %}
                                    <tr>
                                        <td>{{ invoice.number }}</td>
                                        <td>{{ invoice.due_date|date:"M d, Y" }}</td>
                                        <td>KES {{ invoice.balance|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-warning">
                                        <th colspan="2">Total Unpaid:</th>
                                        <th>KES {{ total_unpaid|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Advance Payment Form Card -->
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Advance Payment Details</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="advancePaymentForm">
                                {% csrf_token %}
                                
                                <!-- Number of Months -->
                                <div class="mb-4">
                                    <label for="months" class="form-label"><strong>Number of Months</strong></label>
                                    <div class="input-group input-group-lg">
                                        <input type="number" 
                                               class="form-control" 
                                               id="months" 
                                               name="months" 
                                               value="6" 
                                               min="1" 
                                               max="24"
                                               required>
                                        <span class="input-group-text">months</span>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> How many months of rent is being paid in advance?
                                    </small>
                                </div>

                                <!-- Amount -->
                                <div class="mb-4">
                                    <label for="amount" class="form-label"><strong>Payment Amount</strong></label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text">KES</span>
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               value="{{ monthly_rent|floatformat:2 }}" 
                                               step="0.01" 
                                               min="0.01"
                                               required>
                                    </div>
                                    <small class="text-muted" id="calculatedAmount">
                                        <i class="fas fa-calculator"></i> Calculated: KES <span id="calcAmount">0.00</span>
                                    </small>
                                </div>

                                <!-- Payment Method -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>Payment Method</strong></label>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="mpesa" value="MPESA" checked>
                                                <label class="form-check-label" for="mpesa">
                                                    <i class="fas fa-mobile-alt text-success"></i> M-Pesa
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="cash" value="CASH">
                                                <label class="form-check-label" for="cash">
                                                    <i class="fas fa-money-bill-wave text-primary"></i> Cash
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="bank" value="BANK_TRANSFER">
                                                <label class="form-check-label" for="bank">
                                                    <i class="fas fa-university text-info"></i> Bank Transfer
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="payment_method" id="cheque" value="CHEQUE">
                                                <label class="form-check-label" for="cheque">
                                                    <i class="fas fa-money-check text-warning"></i> Cheque
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reference Number -->
                                <div class="mb-4">
                                    <label for="reference" class="form-label">Reference Number (Optional)</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="reference" 
                                           name="reference" 
                                           placeholder="e.g., TXN123456, CHQ-789">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> Leave blank to auto-generate
                                    </small>
                                </div>

                                <!-- Apply to Existing Invoices -->
                                {% if unpaid_invoices %}
                                <div class="mb-4">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       id="apply_to_existing" 
                                                       name="apply_to_existing"
                                                       checked>
                                                <label class="form-check-label" for="apply_to_existing">
                                                    <strong>Apply to existing unpaid invoices first</strong>
                                                </label>
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                <i class="fas fa-info-circle"></i> 
                                                If checked, the payment will first be applied to existing unpaid invoices (KES {{ total_unpaid|floatformat:2 }}), 
                                                and any remaining amount will be added to the credit balance.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Notes -->
                                <div class="mb-4">
                                    <label for="notes" class="form-label">Notes (Optional)</label>
                                    <textarea class="form-control" 
                                              id="notes" 
                                              name="notes" 
                                              rows="3" 
                                              placeholder="Any additional notes about this advance payment..."></textarea>
                                </div>

                                <!-- Summary Box -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Payment Summary</h6>
                                    <ul class="mb-0">
                                        <li>Paying for <strong><span id="summaryMonths">6</span> month(s)</strong></li>
                                        <li>Total amount: <strong>KES <span id="summaryAmount">0.00</span></strong></li>
                                        {% if unpaid_invoices %}
                                        <li id="applyToExistingSummary">Will apply to existing invoices: <strong>KES {{ total_unpaid|floatformat:2 }}</strong></li>
                                        <li id="creditBalanceSummary">Remaining credit: <strong>KES <span id="summaryCredit">0.00</span></strong></li>
                                        {% else %}
                                        <li>Will be added to credit balance</li>
                                        {% endif %}
                                    </ul>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-check-circle"></i> Record Advance Payment
                                    </button>
                                    <a href="{% org_url 'lease_detail' pk=lease.pk %}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthsInput = document.getElementById('months');
    const amountInput = document.getElementById('amount');
    const applyToExistingCheckbox = document.getElementById('apply_to_existing');
    const monthlyRent = {{ monthly_rent }};
    const totalUnpaid = {{ total_unpaid }};
    
    function updateCalculations() {
        const months = parseInt(monthsInput.value) || 1;
        const amount = parseFloat(amountInput.value) || 0;
        const calculatedAmount = monthlyRent * months;
        
        // Update calculated amount display
        document.getElementById('calcAmount').textContent = calculatedAmount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        
        // Update summary
        document.getElementById('summaryMonths').textContent = months;
        document.getElementById('summaryAmount').textContent = amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        
        // Calculate credit balance
        {% if unpaid_invoices %}
        const applyToExisting = applyToExistingCheckbox ? applyToExistingCheckbox.checked : false;
        if (applyToExisting) {
            const remainingCredit = Math.max(0, amount - totalUnpaid);
            document.getElementById('summaryCredit').textContent = remainingCredit.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            document.getElementById('applyToExistingSummary').style.display = 'list-item';
            document.getElementById('creditBalanceSummary').style.display = 'list-item';
        } else {
            document.getElementById('summaryCredit').textContent = amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            document.getElementById('applyToExistingSummary').style.display = 'none';
            document.getElementById('creditBalanceSummary').style.display = 'list-item';
        }
        {% endif %}
    }
    
    // Auto-calculate amount when months change
    monthsInput.addEventListener('input', function() {
        const months = parseInt(this.value) || 1;
        const calculatedAmount = monthlyRent * months;
        amountInput.value = calculatedAmount.toFixed(2);
        updateCalculations();
    });
    
    // Update calculations when amount changes
    amountInput.addEventListener('input', updateCalculations);
    
    // Update calculations when checkbox changes
    if (applyToExistingCheckbox) {
        applyToExistingCheckbox.addEventListener('change', updateCalculations);
    }
    
    // Initial calculation
    updateCalculations();
});
</script>
{% endblock %}

