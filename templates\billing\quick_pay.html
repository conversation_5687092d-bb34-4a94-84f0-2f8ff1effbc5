{% extends 'base.html' %}
{% load org_urls %}

{% block title %}Pay Invoice {{ invoice.number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-credit-card"></i> Pay Invoice</h2>
                <a href="{% org_url 'invoice_detail' pk=invoice.pk %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Invoice
                </a>
            </div>

            <!-- Messages -->
            {% if messages %}
                <div class="row mb-3">
                    <div class="col-12">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'error' %}
                                    <i class="fas fa-exclamation-circle"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="fas fa-check-circle"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% else %}
                                    <i class="fas fa-info-circle"></i>
                                {% endif %}
                                <strong>{{ message }}</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <div class="row">
                <!-- Invoice Summary Card -->
                <div class="col-md-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-file-invoice"></i> Invoice Summary</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <th>Invoice #:</th>
                                    <td><strong>{{ invoice.number }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Tenant:</th>
                                    <td>{{ invoice.lease.tenant }}</td>
                                </tr>
                                <tr>
                                    <th>Unit:</th>
                                    <td>{{ invoice.lease.unit }}</td>
                                </tr>
                                <tr>
                                    <th>Due Date:</th>
                                    <td>{{ invoice.due_date|date:"M d, Y" }}</td>
                                </tr>
                                <tr class="table-light">
                                    <th>Total Amount:</th>
                                    <td><strong>KES {{ invoice.amount_due|floatformat:2 }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Amount Paid:</th>
                                    <td>KES {{ invoice.amount_paid|floatformat:2 }}</td>
                                </tr>
                                <tr class="table-success">
                                    <th><strong>Balance Due:</strong></th>
                                    <td><strong class="text-success">KES {{ balance|floatformat:2 }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Payment Form Card -->
                <div class="col-md-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Select Payment Method</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" id="paymentForm">
                                {% csrf_token %}
                                
                                <!-- Amount Field -->
                                <div class="mb-4">
                                    <label for="amount" class="form-label"><strong>Amount to Pay</strong></label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text">KES</span>
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               value="{{ balance }}" 
                                               step="0.01" 
                                               min="0.01" 
                                               max="{{ balance }}"
                                               required>
                                    </div>
                                    <small class="text-muted">Maximum: KES {{ balance|floatformat:2 }}</small>
                                </div>

                                <!-- Payment Method Selection -->
                                <div class="mb-4">
                                    <label class="form-label"><strong>Choose Payment Method</strong></label>
                                    
                                    <!-- M-Pesa Option -->
                                    <div class="card mb-3 payment-method-card" data-method="MPESA">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       name="payment_method" 
                                                       id="mpesa" 
                                                       value="MPESA" 
                                                       checked>
                                                <label class="form-check-label" for="mpesa">
                                                    <h5 class="mb-1">
                                                        <i class="fas fa-mobile-alt text-success"></i> M-Pesa
                                                    </h5>
                                                    <small class="text-muted">Pay instantly via STK Push</small>
                                                </label>
                                            </div>
                                            
                                            <!-- M-Pesa Phone Field -->
                                            <div class="mt-3" id="mpesa-fields">
                                                <label for="phone_number" class="form-label">M-Pesa Phone Number</label>
                                                <input type="tel" 
                                                       class="form-control" 
                                                       id="phone_number" 
                                                       name="phone_number" 
                                                       value="{{ tenant_phone }}" 
                                                       placeholder="0712345678"
                                                       pattern="^(\+?254|0)[17]\d{8}$">
                                                <small class="text-muted">Format: 0712345678 or +254712345678</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cash Option -->
                                    <div class="card mb-3 payment-method-card" data-method="CASH">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       name="payment_method" 
                                                       id="cash" 
                                                       value="CASH">
                                                <label class="form-check-label" for="cash">
                                                    <h5 class="mb-1">
                                                        <i class="fas fa-money-bill-wave text-primary"></i> Cash
                                                    </h5>
                                                    <small class="text-muted">Record cash payment received</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Bank Transfer Option -->
                                    <div class="card mb-3 payment-method-card" data-method="BANK_TRANSFER">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       name="payment_method" 
                                                       id="bank_transfer" 
                                                       value="BANK_TRANSFER">
                                                <label class="form-check-label" for="bank_transfer">
                                                    <h5 class="mb-1">
                                                        <i class="fas fa-university text-info"></i> Bank Transfer
                                                    </h5>
                                                    <small class="text-muted">Record bank transfer payment</small>
                                                </label>
                                            </div>
                                            
                                            <!-- Bank Transfer Reference Field -->
                                            <div class="mt-3 d-none" id="bank-transfer-fields">
                                                <label for="bank_reference" class="form-label">Transaction Reference</label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="bank_reference" 
                                                       name="reference" 
                                                       placeholder="e.g., TXN123456789">
                                                <small class="text-muted">Enter the bank transaction reference number</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cheque Option -->
                                    <div class="card mb-3 payment-method-card" data-method="CHEQUE">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       name="payment_method" 
                                                       id="cheque" 
                                                       value="CHEQUE">
                                                <label class="form-check-label" for="cheque">
                                                    <h5 class="mb-1">
                                                        <i class="fas fa-money-check text-warning"></i> Cheque
                                                    </h5>
                                                    <small class="text-muted">Record cheque payment</small>
                                                </label>
                                            </div>
                                            
                                            <!-- Cheque Reference Field -->
                                            <div class="mt-3 d-none" id="cheque-fields">
                                                <label for="cheque_reference" class="form-label">Cheque Number</label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="cheque_reference" 
                                                       name="reference" 
                                                       placeholder="e.g., CHQ-123456">
                                                <small class="text-muted">Enter the cheque number</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Card Option -->
                                    <div class="card mb-3 payment-method-card" data-method="CARD">
                                        <div class="card-body">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="radio" 
                                                       name="payment_method" 
                                                       id="card" 
                                                       value="CARD">
                                                <label class="form-check-label" for="card">
                                                    <h5 class="mb-1">
                                                        <i class="fas fa-credit-card text-danger"></i> Card Payment
                                                    </h5>
                                                    <small class="text-muted">Record card payment</small>
                                                </label>
                                            </div>
                                            
                                            <!-- Card Reference Field -->
                                            <div class="mt-3 d-none" id="card-fields">
                                                <label for="card_reference" class="form-label">Transaction Reference</label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="card_reference" 
                                                       name="reference" 
                                                       placeholder="e.g., AUTH-123456">
                                                <small class="text-muted">Enter the card transaction reference</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                        <i class="fas fa-check-circle"></i> <span id="btnText">Process Payment</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const mpesaFields = document.getElementById('mpesa-fields');
    const bankTransferFields = document.getElementById('bank-transfer-fields');
    const chequeFields = document.getElementById('cheque-fields');
    const cardFields = document.getElementById('card-fields');
    const phoneInput = document.getElementById('phone_number');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = document.getElementById('btnText');
    
    // Handle payment method selection
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Hide all additional fields
            mpesaFields.classList.add('d-none');
            bankTransferFields.classList.add('d-none');
            chequeFields.classList.add('d-none');
            cardFields.classList.add('d-none');
            
            // Remove required from all reference fields
            phoneInput.removeAttribute('required');
            document.getElementById('bank_reference').removeAttribute('required');
            document.getElementById('cheque_reference').removeAttribute('required');
            document.getElementById('card_reference').removeAttribute('required');
            
            // Show relevant fields and update button text
            if (this.value === 'MPESA') {
                mpesaFields.classList.remove('d-none');
                phoneInput.setAttribute('required', 'required');
                btnText.textContent = 'Send STK Push';
            } else if (this.value === 'CASH') {
                btnText.textContent = 'Record Cash Payment';
            } else if (this.value === 'BANK_TRANSFER') {
                bankTransferFields.classList.remove('d-none');
                document.getElementById('bank_reference').setAttribute('required', 'required');
                btnText.textContent = 'Record Bank Transfer';
            } else if (this.value === 'CHEQUE') {
                chequeFields.classList.remove('d-none');
                document.getElementById('cheque_reference').setAttribute('required', 'required');
                btnText.textContent = 'Record Cheque Payment';
            } else if (this.value === 'CARD') {
                cardFields.classList.remove('d-none');
                document.getElementById('card_reference').setAttribute('required', 'required');
                btnText.textContent = 'Record Card Payment';
            }
        });
    });
    
    // Highlight selected payment method card
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.payment-method-card').forEach(c => {
                c.style.border = '1px solid #dee2e6';
                c.style.backgroundColor = '#fff';
            });
            this.style.border = '2px solid #198754';
            this.style.backgroundColor = '#f8f9fa';
            
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            radio.dispatchEvent(new Event('change'));
        });
    });
    
    // Trigger initial state
    document.querySelector('input[name="payment_method"]:checked').dispatchEvent(new Event('change'));
});
</script>

<style>
.payment-method-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>
{% endblock %}

