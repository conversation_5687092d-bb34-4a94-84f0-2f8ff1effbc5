from django.db import models
from django.contrib.auth.models import User
from core.models import OrgScopedModel
from decimal import Decimal


class Property(OrgScopedModel):
    name = models.CharField(max_length=150)
    address = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Unit(OrgScopedModel):
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    code = models.CharField(max_length=50, blank=True)
    bedrooms = models.PositiveIntegerField()
    bathrooms = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.property.name} - {self.code}"

    @staticmethod
    def generate_unit_code(property_obj, organization):
        """
        Generate auto-incremented unit code for a property.
        Format: {PropertyInitials}-{SequentialNumber}
        Example: NH-001, NH-002, etc.
        """
        # Get property initials (first letter of each word, max 3 letters)
        words = property_obj.name.split()
        if len(words) == 1:
            # Single word - take first 2-3 letters
            initials = property_obj.name[:3].upper()
        else:
            # Multiple words - take first letter of each word (max 3)
            initials = ''.join([word[0].upper() for word in words[:3]])

        # Get the count of existing units for this property
        existing_count = Unit.objects.filter(
            organization=organization,
            property=property_obj
        ).count()

        # Generate sequential number (starting from 001)
        sequence_number = existing_count + 1

        # Format: INITIALS-XXX (e.g., NH-001, NH-002)
        unit_code = f"{initials}-{sequence_number:03d}"

        # Check if code already exists (edge case)
        while Unit.objects.filter(organization=organization, code=unit_code).exists():
            sequence_number += 1
            unit_code = f"{initials}-{sequence_number:03d}"

        return unit_code

    def save(self, *args, **kwargs):
        # Auto-generate unit code if not provided
        if not self.code and self.property_id and self.organization_id:
            self.code = self.generate_unit_code(self.property, self.organization)
        super().save(*args, **kwargs)


class Tenant(OrgScopedModel):
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True)
    id_number = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"


class Lease(OrgScopedModel):
    STATUS = [('ACTIVE','Active'),
              ('ENDED','Ended'),
              ('PENDING','Pending')
              ]
    unit = models.ForeignKey(Unit, on_delete=models.PROTECT)
    tenant = models.ForeignKey(Tenant, on_delete=models.PROTECT)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    rent_amount = models.DecimalField(max_digits=10, decimal_places=2)
    from decimal import Decimal
    deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    billing_day = models.PositiveSmallIntegerField(default=1)
    status = models.CharField(max_length=20, choices=STATUS, default='ACTIVE')

    class Meta:
        unique_together = ('organization', 'unit', 'tenant', 'start_date')

class Invoice(OrgScopedModel):
    INVOICE_TYPES = [
        ('RENT', 'Rent'),
        ('AMENITIES', 'Amenities'),
        ('MIXED', 'Rent + Amenities'),
        ('DEPOSIT', 'Deposit'),
        ('OTHER', 'Other'),
    ]

    lease = models.ForeignKey(Lease, on_delete=models.CASCADE, related_name='invoices')
    number = models.CharField(max_length=30)
    invoice_type = models.CharField(max_length=20, choices=INVOICE_TYPES, default='RENT')
    issue_date = models.DateField()
    due_date = models.DateField()

    # Breakdown of charges
    rent_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    amenities_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    other_charges = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    amount_due = models.DecimalField(max_digits=10, decimal_places=2)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    is_paid = models.BooleanField(default=False)

    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('organization', 'number')
        ordering = ['-issue_date']

    @property
    def balance(self):
        return self.amount_due - self.amount_paid

    @staticmethod
    def generate_invoice_number(organization):
        """Generate a unique invoice number for the organization"""
        from datetime import datetime

        # Get current year and month
        now = datetime.now()
        year = now.year
        month = now.month

        # Format: INV-YYYY-MM-XXXX (e.g., INV-2025-10-0001)
        prefix = f"INV-{year}-{month:02d}-"

        # Find the last invoice with this prefix
        last_invoice = Invoice.objects.filter(
            organization=organization,
            number__startswith=prefix
        ).order_by('-number').first()

        if last_invoice:
            # Extract the sequence number and increment
            try:
                last_seq = int(last_invoice.number.split('-')[-1])
                new_seq = last_seq + 1
            except (ValueError, IndexError):
                new_seq = 1
        else:
            new_seq = 1

        return f"{prefix}{new_seq:04d}"

    def save(self, *args, **kwargs):
        # Auto-calculate total amount if not provided
        if not self.amount_due:
            self.amount_due = self.rent_amount + self.amenities_amount + self.tax_amount + self.other_charges
        super().save(*args, **kwargs)


class InvoiceLineItem(OrgScopedModel):
    """Individual line items for invoices (rent, amenities, taxes, etc.)"""
    ITEM_TYPES = [
        ('RENT', 'Rent'),
        ('WATER', 'Water'),
        ('ELECTRICITY', 'Electricity'),
        ('GARBAGE', 'Garbage Collection'),
        ('INTERNET', 'Internet'),
        ('SECURITY', 'Security'),
        ('PARKING', 'Parking'),
        ('MAINTENANCE', 'Maintenance'),
        ('TAX', 'Tax'),
        ('OTHER', 'Other'),
    ]

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='line_items')
    item_type = models.CharField(max_length=20, choices=ITEM_TYPES)
    description = models.CharField(max_length=200)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    # Reference to amenity or tax if applicable
    amenity_type = models.ForeignKey('AmenityType', on_delete=models.SET_NULL, null=True, blank=True)
    government_tax = models.ForeignKey('GovernmentTax', on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['item_type', 'description']

    def save(self, *args, **kwargs):
        # Auto-calculate amount
        self.amount = self.quantity * self.unit_price

        # Auto-assign organization from invoice if not set
        if not self.organization_id and self.invoice_id:
            self.organization = self.invoice.organization

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.invoice.number} - {self.description}"


class Payment(OrgScopedModel):
    METHODS = [('CASH','Cash'),
               ('MPESA','M-Pesa'),
               ('CHEQUE','Cheque'),
               ('BANK_TRANSFER','Bank Transfer'),
               ('CARD','Card')]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('TIMEOUT', 'Timeout'),
    ]

    invoice = models.ForeignKey(Invoice, on_delete=models.PROTECT, related_name='payments', null=True, blank=True)
    lease = models.ForeignKey(Lease, on_delete=models.PROTECT, related_name='payments', null=True, blank=True)
    date = models.DateField()
    reference = models.CharField(max_length=50, blank=True)
    method = models.CharField(max_length=30, choices=METHODS, default='MPESA')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paid_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    transaction_id = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # Advance payment tracking
    is_advance_payment = models.BooleanField(default=False)
    months_covered = models.IntegerField(default=1, help_text='Number of months this payment covers')
    notes = models.TextField(blank=True, null=True)

    # M-Pesa specific fields
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    mpesa_receipt_number = models.CharField(max_length=50, blank=True, null=True)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    checkout_request_id = models.CharField(max_length=100, blank=True, null=True)
    merchant_request_id = models.CharField(max_length=100, blank=True, null=True)
    result_code = models.CharField(max_length=10, blank=True, null=True)
    result_desc = models.TextField(blank=True, null=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date']
        unique_together = ('organization', 'invoice', 'reference')

    def __str__(self):
        return f"Payment {self.reference} - KES {self.amount:,.2f}"


class TenantCreditBalance(OrgScopedModel):
    """Track tenant credit balances from advance payments"""
    lease = models.OneToOneField(Lease, on_delete=models.CASCADE, related_name='credit_balance')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Tenant Credit Balance'
        verbose_name_plural = 'Tenant Credit Balances'

    def __str__(self):
        return f"{self.lease.tenant} - Credit: KES {self.balance:,.2f}"

    @classmethod
    def get_or_create_for_lease(cls, lease):
        """Get or create credit balance for a lease"""
        balance, created = cls.objects.get_or_create(
            organization=lease.organization,
            lease=lease,
            defaults={'balance': Decimal('0.00')}
        )
        return balance

    def add_credit(self, amount):
        """Add credit to balance"""
        self.balance += amount
        self.save()

    def deduct_credit(self, amount):
        """Deduct credit from balance"""
        if amount > self.balance:
            raise ValueError(f'Insufficient credit balance. Available: {self.balance}, Requested: {amount}')
        self.balance -= amount
        self.save()

    def has_sufficient_credit(self, amount):
        """Check if there's sufficient credit"""
        return self.balance >= amount


class AdvancePaymentAllocation(OrgScopedModel):
    """Track how advance payments are allocated to invoices"""
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='allocations')
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='advance_allocations')
    amount_allocated = models.DecimalField(max_digits=10, decimal_places=2)
    allocation_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-allocation_date']

    def __str__(self):
        return f"{self.payment.reference} → {self.invoice.number}: KES {self.amount_allocated:,.2f}"


class MaintenanceRequest(OrgScopedModel):
    PRIORITY = [('LOW','Low'), ('MEDIUM','Medium'), ('HIGH','High')]
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    title = models.CharField(max_length=150)
    description = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY, default='MEDIUM')
    status = models.CharField(max_length=20, default='Open')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title


class AmenityType(OrgScopedModel):
    """Define different types of amenities (water, electricity, garbage, etc.)"""
    BILLING_CYCLES = [
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('ANNUALLY', 'Annually'),
        ('ONE_TIME', 'One Time'),
        ('ON_DEMAND', 'On Demand'),
    ]

    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLES, default='MONTHLY')
    default_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    is_mandatory = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('organization', 'name')
        ordering = ['name']

    def __str__(self):
        return self.name


class UnitAmenity(OrgScopedModel):
    """Link units to specific amenities with custom pricing"""
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, related_name='amenities')
    amenity_type = models.ForeignKey(AmenityType, on_delete=models.CASCADE)
    custom_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('organization', 'unit', 'amenity_type')

    @property
    def amount(self):
        return self.custom_amount or self.amenity_type.default_amount

    def save(self, *args, **kwargs):
        # Auto-assign organization from unit if not set
        if not self.organization_id and self.unit_id:
            self.organization = self.unit.organization
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.unit} - {self.amenity_type.name}"


class GovernmentTax(OrgScopedModel):
    """Government taxes and statutory deductions"""
    TAX_TYPES = [
        ('RENTAL_INCOME_TAX', 'Rental Income Tax'),
        ('PROPERTY_TAX', 'Property Tax'),
        ('SERVICE_CHARGE_TAX', 'Service Charge Tax'),
        ('VAT', 'Value Added Tax'),
        ('WITHHOLDING_TAX', 'Withholding Tax'),
        ('LAND_RATES', 'Land Rates'),
        ('OTHER', 'Other'),
    ]

    CALCULATION_METHODS = [
        ('PERCENTAGE', 'Percentage of Income'),
        ('FIXED_AMOUNT', 'Fixed Amount'),
        ('PER_UNIT', 'Per Unit'),
        ('PER_SQFT', 'Per Square Foot'),
    ]

    name = models.CharField(max_length=100)
    tax_type = models.CharField(max_length=30, choices=TAX_TYPES)
    calculation_method = models.CharField(max_length=20, choices=CALCULATION_METHODS)
    rate = models.DecimalField(max_digits=5, decimal_places=2, help_text="Percentage rate or fixed amount")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    effective_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('organization', 'name')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_tax_type_display()})"

    def calculate_tax(self, base_amount, unit_count=1, area_sqft=0):
        """Calculate tax based on the method and rate"""
        if self.calculation_method == 'PERCENTAGE':
            return (base_amount * self.rate) / 100
        elif self.calculation_method == 'FIXED_AMOUNT':
            return self.rate
        elif self.calculation_method == 'PER_UNIT':
            return self.rate * unit_count
        elif self.calculation_method == 'PER_SQFT':
            return self.rate * area_sqft
        return Decimal('0.00')