
/* Enhanced DataTables Global Styling */

/* DataTables Buttons Container */
.dt-buttons {
    margin-bottom: 15px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Enhanced Button Styling */
.dt-button {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    border: none !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    text-decoration: none !important;
}

.dt-button:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3) !important;
    color: white !important;
}

.dt-button:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
}

/* Specific button colors */
.dt-button.buttons-copy {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

.dt-button.buttons-csv {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.dt-button.buttons-excel {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.dt-button.buttons-pdf {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.dt-button.buttons-print {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    color: #212529 !important;
}

/* Hover effects for specific buttons */
.dt-button.buttons-copy:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
}

.dt-button.buttons-csv:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%) !important;
}

.dt-button.buttons-excel:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%) !important;
}

.dt-button.buttons-pdf:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%) !important;
}

.dt-button.buttons-print:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%) !important;
}

/* DataTables Search and Length Controls */
.dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 8px 12px;
    margin-left: 8px;
}

.dataTables_length select {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 8px;
}

/* DataTables Info and Pagination */
.dataTables_info {
    color: #6c757d;
    font-size: 14px;
}

.dataTables_paginate {
    margin-top: 15px;
}

.dataTables_paginate .paginate_button {
    padding: 8px 12px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    color: #007bff;
    text-decoration: none;
}

.dataTables_paginate .paginate_button:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.dataTables_paginate .paginate_button.current {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Enhanced Table Styling */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    padding: 15px 12px;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dt-buttons {
        justify-content: center;
    }

    .dt-button {
        padding: 6px 12px !important;
        font-size: 11px !important;
    }

    .dataTables_filter,
    .dataTables_length {
        text-align: center;
        margin-bottom: 10px;
    }
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3VzdG9tLmNzcyIsInNvdXJjZXMiOlsiY3VzdG9tLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiJ9 */

/*# sourceMappingURL=custom.css.map */
