"""
Django management command to generate recurring invoices for active leases.
Run this command monthly (e.g., via cron job or task scheduler) to automatically
generate invoices for all active leases.

Usage:
    python manage.py generate_recurring_invoices
    python manage.py generate_recurring_invoices --organization=test
    python manage.py generate_recurring_invoices --dry-run
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal

from core.models import Organization
from rentals.models import Lease, Invoice, InvoiceLineItem, AmenityType, UnitAmenity


class Command(BaseCommand):
    help = 'Generate recurring invoices for all active leases'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Generate invoices for a specific organization (slug)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Simulate invoice generation without saving to database',
        )
        parser.add_argument(
            '--due-days',
            type=int,
            default=7,
            help='Number of days until invoice is due (default: 7)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        org_slug = options['organization']
        due_days = options['due_days']

        self.stdout.write(self.style.SUCCESS('=' * 70))
        self.stdout.write(self.style.SUCCESS('RECURRING INVOICE GENERATION'))
        self.stdout.write(self.style.SUCCESS('=' * 70))
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be saved'))
        
        # Get organizations to process
        if org_slug:
            organizations = Organization.objects.filter(slug=org_slug)
            if not organizations.exists():
                self.stdout.write(self.style.ERROR(f'Organization "{org_slug}" not found'))
                return
        else:
            organizations = Organization.objects.all()
        
        total_created = 0
        total_skipped = 0
        total_errors = 0
        
        for org in organizations:
            self.stdout.write(f'\nProcessing organization: {org.name} ({org.slug})')
            self.stdout.write('-' * 70)
            
            created, skipped, errors = self.generate_invoices_for_org(org, dry_run, due_days)
            total_created += created
            total_skipped += skipped
            total_errors += errors
        
        # Summary
        self.stdout.write('\n' + '=' * 70)
        self.stdout.write(self.style.SUCCESS('SUMMARY'))
        self.stdout.write('=' * 70)
        self.stdout.write(f'Total invoices created: {self.style.SUCCESS(total_created)}')
        self.stdout.write(f'Total leases skipped: {self.style.WARNING(total_skipped)}')
        self.stdout.write(f'Total errors: {self.style.ERROR(total_errors)}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('\nDRY RUN - No invoices were actually created'))
        else:
            self.stdout.write(self.style.SUCCESS('\n✓ Invoice generation complete!'))

    @transaction.atomic
    def generate_invoices_for_org(self, organization, dry_run, due_days):
        """Generate invoices for all active leases in an organization"""
        created_count = 0
        skipped_count = 0
        error_count = 0
        
        # Get all active leases
        active_leases = Lease.objects.filter(
            organization=organization,
            status='ACTIVE'
        ).select_related('unit', 'tenant', 'unit__property')
        
        if not active_leases.exists():
            self.stdout.write(self.style.WARNING('  No active leases found'))
            return 0, 0, 0
        
        self.stdout.write(f'  Found {active_leases.count()} active lease(s)')
        
        # Get all mandatory amenities for this organization
        mandatory_amenities = AmenityType.objects.filter(
            organization=organization,
            is_active=True,
            is_mandatory=True
        )
        
        self.stdout.write(f'  Found {mandatory_amenities.count()} mandatory amenity/amenities')
        
        # Set invoice dates
        issue_date = date.today()
        due_date = issue_date + timedelta(days=due_days)
        
        for lease in active_leases:
            try:
                # Check if invoice already exists for this month
                current_month_start = date.today().replace(day=1)
                existing_invoice = Invoice.objects.filter(
                    organization=organization,
                    lease=lease,
                    issue_date__gte=current_month_start
                ).first()
                
                if existing_invoice:
                    self.stdout.write(f'  ⊘ Skipped: {lease.tenant} ({lease.unit}) - Invoice already exists ({existing_invoice.number})')
                    skipped_count += 1
                    continue
                
                if dry_run:
                    self.stdout.write(f'  ✓ Would create: {lease.tenant} ({lease.unit}) - Rent: KES {lease.rent_amount}')
                    created_count += 1
                    continue
                
                # Generate invoice number
                invoice_number = Invoice.generate_invoice_number(organization)
                
                # Create invoice
                invoice = Invoice.objects.create(
                    organization=organization,
                    lease=lease,
                    number=invoice_number,
                    invoice_type='MIXED',
                    issue_date=issue_date,
                    due_date=due_date
                )
                
                # Initialize amounts
                total_amount = Decimal('0.00')
                rent_amount = Decimal('0.00')
                amenities_amount = Decimal('0.00')
                
                # Add rent line item
                rent_amount = lease.rent_amount
                InvoiceLineItem.objects.create(
                    organization=organization,
                    invoice=invoice,
                    item_type='RENT',
                    description=f'Monthly Rent - {lease.unit}',
                    quantity=Decimal('1.00'),
                    unit_price=rent_amount,
                    amount=rent_amount
                )
                total_amount += rent_amount
                
                # Add mandatory amenity line items
                for amenity in mandatory_amenities:
                    # Check for unit-specific pricing
                    unit_amenity = UnitAmenity.objects.filter(
                        unit=lease.unit,
                        amenity_type=amenity,
                        is_active=True
                    ).first()
                    
                    amenity_amount = unit_amenity.amount if unit_amenity else amenity.default_amount
                    
                    InvoiceLineItem.objects.create(
                        organization=organization,
                        invoice=invoice,
                        item_type=amenity.name.upper()[:20] if amenity.name.upper() in dict(InvoiceLineItem.ITEM_TYPES) else 'OTHER',
                        description=amenity.name,
                        quantity=Decimal('1.00'),
                        unit_price=amenity_amount,
                        amount=amenity_amount,
                        amenity_type=amenity
                    )
                    amenities_amount += amenity_amount
                    total_amount += amenity_amount
                
                # Update invoice totals
                invoice.rent_amount = rent_amount
                invoice.amenities_amount = amenities_amount
                invoice.amount_due = total_amount
                invoice.save()
                
                self.stdout.write(self.style.SUCCESS(
                    f'  ✓ Created: {invoice.number} - {lease.tenant} ({lease.unit}) - '
                    f'KES {total_amount:,.2f} ({invoice.line_items.count()} items)'
                ))
                created_count += 1
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f'  ✗ Error: {lease.tenant} ({lease.unit}) - {str(e)}'
                ))
                error_count += 1
        
        return created_count, skipped_count, error_count

