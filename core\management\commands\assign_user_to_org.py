from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Organization, Membership


class Command(BaseCommand):
    help = 'Assign a user to an organization with a specific role'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            required=True,
            help='Username to assign to organization',
        )
        parser.add_argument(
            '--org-slug',
            type=str,
            required=True,
            help='Organization slug to assign user to',
        )
        parser.add_argument(
            '--role',
            type=str,
            choices=['ADMIN', 'MANAGER', 'AGENT', 'TENANT'],
            default='ADMIN',
            help='Role to assign to user (default: ADMIN)',
        )

    def handle(self, *args, **options):
        username = options['username']
        org_slug = options['org_slug']
        role = options['role']

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User "{username}" not found')
            )
            return

        try:
            organization = Organization.objects.get(slug=org_slug)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Organization "{org_slug}" not found')
            )
            return

        # Check if membership already exists
        membership, created = Membership.objects.get_or_create(
            user=user,
            organization=organization,
            defaults={'role': role}
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully assigned {username} to {organization.name} as {role}'
                )
            )
        else:
            # Update existing membership role if different
            if membership.role != role:
                old_role = membership.role
                membership.role = role
                membership.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Updated {username}\'s role in {organization.name} from {old_role} to {role}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'{username} is already assigned to {organization.name} as {role}'
                    )
                )
