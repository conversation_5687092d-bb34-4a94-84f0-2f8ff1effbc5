"""
Views for handling advance rent payments
"""
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.db import transaction
from datetime import date, timedelta
from decimal import Decimal
from dateutil.relativedelta import relativedelta

from rentals.models import Lease, Payment, TenantCreditBalance, Invoice, AdvancePaymentAllocation


@login_required
@transaction.atomic
def record_advance_payment(request, org_slug, lease_id):
    """
    Record advance rent payment for multiple months.
    Creates credit balance and optionally applies to existing unpaid invoices.
    """
    lease = get_object_or_404(Lease, pk=lease_id, organization=request.org)
    
    if request.method == 'POST':
        # Get form data
        amount = Decimal(request.POST.get('amount', 0))
        months = int(request.POST.get('months', 1))
        payment_method = request.POST.get('payment_method', 'CASH')
        reference = request.POST.get('reference', '')
        notes = request.POST.get('notes', '')
        apply_to_existing = request.POST.get('apply_to_existing') == 'on'
        
        # Validate
        if amount <= 0:
            messages.error(request, 'Amount must be greater than zero.')
            return redirect('record_advance_payment', org_slug=org_slug, lease_id=lease_id)
        
        if months < 1:
            messages.error(request, 'Months must be at least 1.')
            return redirect('record_advance_payment', org_slug=org_slug, lease_id=lease_id)
        
        # Calculate expected amount
        expected_amount = lease.rent_amount * months
        
        # Create payment record
        if not reference:
            reference = f'ADV-{lease.tenant.last_name.upper()}-{date.today().strftime("%Y%m%d")}'
        
        payment = Payment.objects.create(
            organization=request.org,
            lease=lease,
            invoice=None,  # Not tied to specific invoice
            date=date.today(),
            reference=reference,
            method=payment_method,
            amount=amount,
            is_advance_payment=True,
            months_covered=months,
            notes=notes or f'Advance payment for {months} month(s)',
            status='COMPLETED'
        )
        
        # Get or create credit balance
        credit_balance = TenantCreditBalance.get_or_create_for_lease(lease)
        
        # Apply to existing unpaid invoices if requested
        remaining_amount = amount
        allocated_invoices = []
        
        if apply_to_existing:
            unpaid_invoices = Invoice.objects.filter(
                organization=request.org,
                lease=lease,
                is_paid=False
            ).order_by('due_date')
            
            for invoice in unpaid_invoices:
                if remaining_amount <= 0:
                    break
                
                invoice_balance = invoice.balance
                allocation_amount = min(remaining_amount, invoice_balance)
                
                # Create allocation record
                AdvancePaymentAllocation.objects.create(
                    organization=request.org,
                    payment=payment,
                    invoice=invoice,
                    amount_allocated=allocation_amount,
                    notes=f'Auto-allocated from advance payment {payment.reference}'
                )
                
                # Update invoice
                invoice.amount_paid += allocation_amount
                if invoice.amount_paid >= invoice.amount_due:
                    invoice.is_paid = True
                invoice.save()
                
                remaining_amount -= allocation_amount
                allocated_invoices.append({
                    'number': invoice.number,
                    'amount': allocation_amount
                })
        
        # Add remaining amount to credit balance
        if remaining_amount > 0:
            credit_balance.add_credit(remaining_amount)
        
        # Success message
        success_msg = f'Advance payment of KES {amount:,.2f} for {months} month(s) recorded successfully!'
        
        if allocated_invoices:
            success_msg += f' Applied to {len(allocated_invoices)} invoice(s).'
        
        if remaining_amount > 0:
            success_msg += f' Credit balance: KES {credit_balance.balance:,.2f}'
        
        messages.success(request, success_msg)
        return redirect('lease_detail', org_slug=org_slug, pk=lease_id)
    
    # GET request - show form
    # Get unpaid invoices
    unpaid_invoices = Invoice.objects.filter(
        organization=request.org,
        lease=lease,
        is_paid=False
    ).order_by('due_date')
    
    total_unpaid = sum(inv.balance for inv in unpaid_invoices)
    
    # Get current credit balance
    credit_balance = TenantCreditBalance.get_or_create_for_lease(lease)
    
    context = {
        'lease': lease,
        'unpaid_invoices': unpaid_invoices,
        'total_unpaid': total_unpaid,
        'credit_balance': credit_balance.balance,
        'monthly_rent': lease.rent_amount,
        'title': f'Record Advance Payment - {lease.tenant}',
    }
    
    return render(request, 'billing/advance_payment_form.html', context)


@login_required
def apply_credit_to_invoice(request, org_slug, invoice_id):
    """
    Apply tenant's credit balance to a specific invoice.
    """
    invoice = get_object_or_404(Invoice, pk=invoice_id, organization=request.org)
    lease = invoice.lease
    
    # Get credit balance
    credit_balance = TenantCreditBalance.get_or_create_for_lease(lease)
    
    if credit_balance.balance <= 0:
        messages.error(request, 'No credit balance available for this tenant.')
        return redirect('invoice_detail', org_slug=org_slug, pk=invoice_id)
    
    if invoice.is_paid:
        messages.error(request, 'This invoice is already paid.')
        return redirect('invoice_detail', org_slug=org_slug, pk=invoice_id)
    
    # Calculate amount to apply
    invoice_balance = invoice.balance
    amount_to_apply = min(credit_balance.balance, invoice_balance)
    
    with transaction.atomic():
        # Find the most recent advance payment with available credit
        advance_payment = Payment.objects.filter(
            organization=request.org,
            lease=lease,
            is_advance_payment=True,
            status='COMPLETED'
        ).order_by('-date').first()
        
        if not advance_payment:
            messages.error(request, 'No advance payment found.')
            return redirect('invoice_detail', org_slug=org_slug, pk=invoice_id)
        
        # Create allocation
        AdvancePaymentAllocation.objects.create(
            organization=request.org,
            payment=advance_payment,
            invoice=invoice,
            amount_allocated=amount_to_apply,
            notes=f'Manual allocation to invoice {invoice.number}'
        )
        
        # Update invoice
        invoice.amount_paid += amount_to_apply
        if invoice.amount_paid >= invoice.amount_due:
            invoice.is_paid = True
        invoice.save()
        
        # Deduct from credit balance
        credit_balance.deduct_credit(amount_to_apply)
        
        messages.success(
            request,
            f'Applied KES {amount_to_apply:,.2f} from credit balance to invoice {invoice.number}. '
            f'Remaining credit: KES {credit_balance.balance:,.2f}'
        )
    
    return redirect('invoice_detail', org_slug=org_slug, pk=invoice_id)


@login_required
def tenant_credit_balance_view(request, org_slug, lease_id):
    """
    View tenant's credit balance and allocation history.
    """
    lease = get_object_or_404(Lease, pk=lease_id, organization=request.org)
    credit_balance = TenantCreditBalance.get_or_create_for_lease(lease)
    
    # Get all advance payments
    advance_payments = Payment.objects.filter(
        organization=request.org,
        lease=lease,
        is_advance_payment=True
    ).order_by('-date')
    
    # Get all allocations
    allocations = AdvancePaymentAllocation.objects.filter(
        organization=request.org,
        payment__lease=lease
    ).select_related('payment', 'invoice').order_by('-allocation_date')
    
    # Calculate total paid in advance
    total_advance_paid = sum(p.amount for p in advance_payments)
    
    # Calculate total allocated
    total_allocated = sum(a.amount_allocated for a in allocations)
    
    context = {
        'lease': lease,
        'credit_balance': credit_balance,
        'advance_payments': advance_payments,
        'allocations': allocations,
        'total_advance_paid': total_advance_paid,
        'total_allocated': total_allocated,
        'title': f'Credit Balance - {lease.tenant}',
    }
    
    return render(request, 'billing/tenant_credit_balance.html', context)


@login_required
def calculate_advance_payment(request, org_slug, lease_id):
    """
    AJAX endpoint to calculate advance payment amount based on months.
    """
    lease = get_object_or_404(Lease, pk=lease_id, organization=request.org)
    months = int(request.GET.get('months', 1))
    
    amount = lease.rent_amount * months
    
    return JsonResponse({
        'months': months,
        'monthly_rent': str(lease.rent_amount),
        'total_amount': str(amount),
        'formatted_amount': f'KES {amount:,.2f}'
    })

