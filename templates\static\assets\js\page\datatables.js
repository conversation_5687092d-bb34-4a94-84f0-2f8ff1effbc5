"use strict";

$("[data-checkboxes]").each(function () {
  var me = $(this),
    group = me.data('checkboxes'),
    role = me.data('checkbox-role');

  me.change(function () {
    var all = $('[data-checkboxes="' + group + '"]:not([data-checkbox-role="dad"])'),
      checked = $('[data-checkboxes="' + group + '"]:not([data-checkbox-role="dad"]):checked'),
      dad = $('[data-checkboxes="' + group + '"][data-checkbox-role="dad"]'),
      total = all.length,
      checked_length = checked.length;

    if (role == 'dad') {
      if (me.is(':checked')) {
        all.prop('checked', true);
      } else {
        all.prop('checked', false);
      }
    } else {
      if (checked_length >= total) {
        dad.prop('checked', true);
      } else {
        dad.prop('checked', false);
      }
    }
  });
});

$("#table-1").dataTable({
  "columnDefs": [
    { "sortable": false, "targets": [2, 3] }
  ]
});
$("#table-2").dataTable({
  "columnDefs": [
    { "sortable": false, "targets": [0, 2, 3] }
  ],
  order: [[1, "asc"]] //column indexes is zero based

});
$('#save-stage').DataTable({
  "scrollX": true,
  stateSave: true
});
// Enhanced Export Table Configuration
$('#tableExport').DataTable({
  dom: 'Bfrtip',
  buttons: {
    dom: {
      button: {
        className: 'dt-button'
      }
    },
    buttons: [
      {
        extend: 'copy',
        text: '<i class="fas fa-copy"></i> Copy',
        className: 'dt-button buttons-copy',
        exportOptions: {
          columns: ':not(:last-child)' // Exclude Actions column
        }
      },
      {
        extend: 'csv',
        text: '<i class="fas fa-file-csv"></i> CSV',
        className: 'dt-button buttons-csv',
        filename: function() {
          return 'export_' + new Date().toISOString().slice(0,10);
        },
        exportOptions: {
          columns: ':not(:last-child)'
        }
      },
      {
        extend: 'excel',
        text: '<i class="fas fa-file-excel"></i> Excel',
        className: 'dt-button buttons-excel',
        filename: function() {
          return 'export_' + new Date().toISOString().slice(0,10);
        },
        exportOptions: {
          columns: ':not(:last-child)'
        }
      },
      {
        extend: 'pdf',
        text: '<i class="fas fa-file-pdf"></i> PDF',
        className: 'dt-button buttons-pdf',
        filename: function() {
          return 'export_' + new Date().toISOString().slice(0,10);
        },
        orientation: 'landscape',
        pageSize: 'A4',
        exportOptions: {
          columns: ':not(:last-child)'
        },
        customize: function(doc) {
          doc.defaultStyle.fontSize = 10;
          doc.styles.tableHeader.fontSize = 12;
          doc.styles.tableHeader.fillColor = '#343a40';
          doc.styles.tableHeader.color = 'white';
        }
      },
      {
        extend: 'print',
        text: '<i class="fas fa-print"></i> Print',
        className: 'dt-button buttons-print',
        exportOptions: {
          columns: ':not(:last-child)'
        }
      }
    ]
  },
  responsive: true,
  pageLength: 25,
  lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
  columnDefs: [
    {
      targets: -1, // Last column (usually Actions)
      orderable: false,
      searchable: false
    }
  ]
});
