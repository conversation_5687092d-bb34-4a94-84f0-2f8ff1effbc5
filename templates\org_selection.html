{% extends 'base.html' %}
{% block title %}Select Organization - RentalX{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-12 col-md-8">
        <div class="card">
            <div class="card-body">
                <h1 class="h3 mb-4 text-center">Select Organization</h1>
                <p class="text-center text-muted mb-4">Choose an organization to access:</p>
                
                <div class="list-group">
                    {% for membership in memberships %}
                    <a href="/{{ membership.organization.slug }}/" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ membership.organization.name }}</h5>
                            <small class="text-muted">{{ membership.get_role_display }}</small>
                        </div>
                        <p class="mb-1">{{ membership.organization.slug }}</p>
                        <small>Member since {{ membership.organization.created_at|date:"M d, Y" }}</small>
                    </a>
                    {% empty %}
                    <div class="list-group-item">
                        <p class="mb-0 text-muted">You don't have access to any organizations.</p>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-4">
                    <a href="/admin/" class="btn btn-outline-secondary">
                        Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
