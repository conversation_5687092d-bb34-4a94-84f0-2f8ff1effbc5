#!/usr/bin/env python
"""
Test script to create sample data for the dashboard
Run this script to populate the database with realistic test data
"""

import os
import sys
import django
from decimal import Decimal
from datetime import date, timedelta
import random

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentalx.settings')
django.setup()

from core.models import Organization, Membership
from rentals.models import Property, Unit, Tenant, Lease, Invoice, Payment, MaintenanceRequest
from django.contrib.auth.models import User

def create_test_data():
    """Create comprehensive test data for dashboard"""
    
    # Get or create organization
    org, created = Organization.objects.get_or_create(
        slug='demo-org',
        defaults={'name': 'Demo Organization'}
    )
    
    if created:
        print(f"Created organization: {org.name}")
    else:
        print(f"Using existing organization: {org.name}")
    
    # Create properties
    properties_data = [
        {'name': 'Westlands Heights', 'address': '123 Westlands Road, Nairobi', 'units': 12},
        {'name': '<PERSON>limani Apartments', 'address': '456 Kilimani Street, Nairobi', 'units': 8},
        {'name': 'Eastlands Complex', 'address': '789 Eastlands Avenue, Nairobi', 'units': 15},
        {'name': 'Karen Villas', 'address': '321 Karen Road, Nairobi', 'units': 6},
        {'name': 'Parklands Towers', 'address': '654 Parklands Drive, Nairobi', 'units': 10},
    ]
    
    properties = []
    for prop_data in properties_data:
        prop, created = Property.objects.get_or_create(
            name=prop_data['name'],
            organization=org,
            defaults={'address': prop_data['address']}
        )
        properties.append((prop, prop_data['units']))
        if created:
            print(f"Created property: {prop.name}")
    
    # Create units and tenants
    tenants_data = [
        {'first_name': 'John', 'last_name': 'Doe', 'email': '<EMAIL>', 'phone': '+254701234567'},
        {'first_name': 'Jane', 'last_name': 'Smith', 'email': '<EMAIL>', 'phone': '+254701234568'},
        {'first_name': 'Michael', 'last_name': 'Johnson', 'email': '<EMAIL>', 'phone': '+254701234569'},
        {'first_name': 'Sarah', 'last_name': 'Wilson', 'email': '<EMAIL>', 'phone': '+254701234570'},
        {'first_name': 'David', 'last_name': 'Brown', 'email': '<EMAIL>', 'phone': '+254701234571'},
        {'first_name': 'Lisa', 'last_name': 'Davis', 'email': '<EMAIL>', 'phone': '+254701234572'},
        {'first_name': 'Robert', 'last_name': 'Miller', 'email': '<EMAIL>', 'phone': '+254701234573'},
        {'first_name': 'Emily', 'last_name': 'Garcia', 'email': '<EMAIL>', 'phone': '+254701234574'},
    ]
    
    tenants = []
    for tenant_data in tenants_data:
        tenant, created = Tenant.objects.get_or_create(
            email=tenant_data['email'],
            organization=org,
            defaults=tenant_data
        )
        tenants.append(tenant)
        if created:
            print(f"Created tenant: {tenant.first_name} {tenant.last_name}")
    
    # Create units and leases
    unit_counter = 1
    lease_counter = 0
    
    for prop, unit_count in properties:
        for i in range(1, unit_count + 1):
            # Create unit
            unit, created = Unit.objects.get_or_create(
                code=f"{prop.name[:3].upper()}-{i:02d}",
                property=prop,
                organization=org,
                defaults={
                    'bedrooms': random.choice([1, 2, 3, 4]),
                    'bathrooms': random.choice([1, 2, 3]),
                    'is_active': True
                }
            )
            
            if created:
                print(f"Created unit: {unit.code}")
            
            # Create lease for some units (not all to simulate vacancies)
            if lease_counter < len(tenants) and random.random() > 0.2:  # 80% occupancy
                tenant = tenants[lease_counter]
                
                # Rent amounts based on property and unit size
                base_rent = {
                    'Westlands Heights': 45000,
                    'Kilimani Apartments': 38000,
                    'Eastlands Complex': 25000,
                    'Karen Villas': 65000,
                    'Parklands Towers': 42000,
                }
                
                rent_amount = base_rent[prop.name] + (unit.bedrooms * 5000)
                
                lease, created = Lease.objects.get_or_create(
                    unit=unit,
                    tenant=tenant,
                    organization=org,
                    defaults={
                        'start_date': date.today() - timedelta(days=random.randint(30, 365)),
                        'end_date': date.today() + timedelta(days=random.randint(180, 730)),
                        'rent_amount': Decimal(str(rent_amount)),
                        'deposit_amount': Decimal(str(rent_amount * 2)),
                        'status': 'ACTIVE'
                    }
                )
                
                if created:
                    print(f"Created lease: {tenant.first_name} {tenant.last_name} -> {unit.code}")
                    
                    # Create invoices for the last 6 months
                    for month_offset in range(6):
                        invoice_date = date.today() - timedelta(days=month_offset * 30)
                        due_date = invoice_date + timedelta(days=30)
                        
                        invoice, created = Invoice.objects.get_or_create(
                            lease=lease,
                            organization=org,
                            issue_date=invoice_date,
                            defaults={
                                'number': f"INV-{org.slug.upper()}-{invoice_date.strftime('%Y%m%d')}-{lease_counter:03d}-{month_offset:02d}",
                                'due_date': due_date,
                                'rent_amount': lease.rent_amount,
                                'amenities_amount': Decimal('2500'),  # Water, garbage, etc.
                                'tax_amount': Decimal('0'),
                                'other_charges': Decimal('0'),
                                'amount_due': lease.rent_amount + Decimal('2500'),
                                'amount_paid': lease.rent_amount + Decimal('2500') if random.random() > 0.15 else Decimal('0'),  # 85% payment rate
                                'is_paid': random.random() > 0.15,  # 85% payment rate
                            }
                        )
                        
                        if created and invoice.is_paid:
                            # Create payment record
                            Payment.objects.get_or_create(
                                invoice=invoice,
                                organization=org,
                                defaults={
                                    'amount': invoice.amount_due,
                                    'date': invoice_date + timedelta(days=random.randint(1, 25)),
                                    'method': random.choice(['MPESA', 'BANK_TRANSFER', 'CASH']),
                                    'reference': f"PAY-{random.randint(100000, 999999)}"
                                }
                            )
                
                lease_counter += 1
            
            unit_counter += 1
    
    # Create some maintenance requests
    maintenance_types = [
        'Plumbing repair', 'Electrical issue', 'Painting required', 
        'Door lock replacement', 'Window repair', 'AC maintenance'
    ]
    
    active_leases = Lease.objects.filter(organization=org, status='ACTIVE')
    for i in range(min(8, len(active_leases))):
        lease = random.choice(active_leases)
        MaintenanceRequest.objects.get_or_create(
            unit=lease.unit,
            organization=org,
            defaults={
                'title': random.choice(maintenance_types),
                'description': f"Maintenance required for unit {lease.unit.code}",
                'priority': random.choice(['LOW', 'MEDIUM', 'HIGH']),
                'status': random.choice(['Open', 'In Progress', 'Completed']),
            }
        )
    
    print("\n" + "="*50)
    print("TEST DATA CREATION COMPLETE!")
    print("="*50)
    print(f"Organization: {org.name}")
    print(f"Properties: {Property.objects.filter(organization=org).count()}")
    print(f"Units: {Unit.objects.filter(organization=org).count()}")
    print(f"Tenants: {Tenant.objects.filter(organization=org).count()}")
    print(f"Active Leases: {Lease.objects.filter(organization=org, status='ACTIVE').count()}")
    print(f"Invoices: {Invoice.objects.filter(organization=org).count()}")
    print(f"Payments: {Payment.objects.filter(organization=org).count()}")
    print(f"Maintenance Requests: {MaintenanceRequest.objects.filter(organization=org).count()}")
    print("\nDashboard URL: http://127.0.0.1:8000/demo-org/")
    print("="*50)

if __name__ == '__main__':
    create_test_data()
