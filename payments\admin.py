from django.contrib import admin
from .models import MpesaConfiguration, MpesaTransaction


@admin.register(MpesaConfiguration)
class MpesaConfigurationAdmin(admin.ModelAdmin):
    list_display = ('organization', 'business_short_code', 'is_sandbox', 'is_active', 'created_at')
    list_filter = ('is_sandbox', 'is_active', 'created_at')
    search_fields = ('organization__name', 'business_short_code')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Organization', {
            'fields': ('organization',)
        }),
        ('M-Pesa Configuration', {
            'fields': ('consumer_key', 'consumer_secret', 'business_short_code', 'passkey')
        }),
        ('Settings', {
            'fields': ('callback_url', 'is_sandbox', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MpesaTransaction)
class MpesaTransactionAdmin(admin.ModelAdmin):
    list_display = ('checkout_request_id', 'organization', 'phone_number', 'amount', 'status', 'created_at')
    list_filter = ('status', 'created_at', 'organization')
    search_fields = ('checkout_request_id', 'merchant_request_id', 'mpesa_receipt_number', 'phone_number')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Transaction Info', {
            'fields': ('organization', 'invoice', 'phone_number', 'amount')
        }),
        ('M-Pesa Details', {
            'fields': ('checkout_request_id', 'merchant_request_id', 'mpesa_receipt_number', 'transaction_date')
        }),
        ('Status', {
            'fields': ('status', 'result_code', 'result_desc')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Prevent manual creation of transactions
        return False
