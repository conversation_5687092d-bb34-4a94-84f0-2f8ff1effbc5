from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from core.models import Organization, Membership


class Command(BaseCommand):
    help = 'Assign a role to a user in an organization'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, required=True, help='Username to assign role to')
        parser.add_argument('--org-slug', type=str, required=True, help='Organization slug')
        parser.add_argument('--role', type=str, required=True, 
                          choices=['ADMIN', 'MANAGER', 'AGENT', 'TENANT'],
                          help='Role to assign (ADMIN, MANAGER, AGENT, TENANT)')

    def handle(self, *args, **options):
        username = options['username']
        org_slug = options['org_slug']
        role = options['role']

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            raise CommandError(f'User "{username}" does not exist')

        try:
            organization = Organization.objects.get(slug=org_slug)
        except Organization.DoesNotExist:
            raise CommandError(f'Organization with slug "{org_slug}" does not exist')

        # Create or update membership
        membership, created = Membership.objects.update_or_create(
            user=user,
            organization=organization,
            defaults={'role': role}
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully assigned {user.username} as {role} to {organization.name}'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated {user.username} role to {role} in {organization.name}'
                )
            )

        # Show current memberships for this user
        memberships = Membership.objects.filter(user=user).select_related('organization')
        self.stdout.write('\nCurrent memberships for this user:')
        for m in memberships:
            self.stdout.write(f'  - {m.organization.name} ({m.organization.slug}): {m.role}')
