{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Income Statement{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-file-invoice-dollar text-primary"></i> Income Statement
                        </h1>
                        <div class="btn-group">
                            <a href="{% org_url 'financial_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date|date:'Y-m-d' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="end_date">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date|date:'Y-m-d' }}">
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Generate Report
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Income Statement -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Income Statement</h4>
                            <div class="card-header-action">
                                <span class="text-muted">{{ start_date|date:"M d, Y" }} - {{ end_date|date:"M d, Y" }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <!-- REVENUE SECTION -->
                                        <tr class="table-primary">
                                            <td colspan="2"><strong>REVENUE</strong></td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">Rental Income</td>
                                            <td class="text-right">KES {{ rent_revenue|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">Amenities Income</td>
                                            <td class="text-right">KES {{ amenities_revenue|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">Other Income</td>
                                            <td class="text-right">KES {{ other_revenue|floatformat:2 }}</td>
                                        </tr>
                                        <tr class="table-light">
                                            <td><strong>Total Revenue</strong></td>
                                            <td class="text-right"><strong>KES {{ total_revenue|floatformat:2 }}</strong></td>
                                        </tr>
                                        
                                        <!-- EXPENSES SECTION -->
                                        <tr class="table-warning">
                                            <td colspan="2"><strong>GOVERNMENT TAXES & DEDUCTIONS</strong></td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">Rental Income Tax</td>
                                            <td class="text-right text-danger">KES {{ rental_income_tax|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">Property Tax</td>
                                            <td class="text-right text-danger">KES {{ property_tax|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <td class="pl-4">VAT on Services</td>
                                            <td class="text-right text-danger">KES {{ vat|floatformat:2 }}</td>
                                        </tr>
                                        <tr class="table-light">
                                            <td><strong>Total Taxes</strong></td>
                                            <td class="text-right text-danger"><strong>KES {{ total_taxes|floatformat:2 }}</strong></td>
                                        </tr>
                                        
                                        <!-- NET INCOME -->
                                        <tr class="table-success">
                                            <td><strong>NET INCOME (After Taxes)</strong></td>
                                            <td class="text-right">
                                                <strong class="{% if net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                    KES {{ net_income|floatformat:2 }}
                                                </strong>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Breakdown Chart -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>Revenue Breakdown</h4>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>Tax Breakdown</h4>
                        </div>
                        <div class="card-body">
                            <canvas id="taxChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Ratios -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Key Financial Ratios</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h5 class="text-muted">Tax Rate</h5>
                                    <h3 class="text-warning">
                                        {% if total_revenue > 0 %}
                                            {% widthratio total_taxes total_revenue 100 %}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </h3>
                                    <small class="text-muted">Taxes / Revenue</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="text-muted">Net Margin</h5>
                                    <h3 class="{% if net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {% if total_revenue > 0 %}
                                            {% widthratio net_income total_revenue 100 %}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </h3>
                                    <small class="text-muted">Net Income / Revenue</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="text-muted">Amenities Share</h5>
                                    <h3 class="text-info">
                                        {% if total_revenue > 0 %}
                                            {% widthratio amenities_revenue total_revenue 100 %}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </h3>
                                    <small class="text-muted">Amenities / Total Revenue</small>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h5 class="text-muted">Rent Share</h5>
                                    <h3 class="text-primary">
                                        {% if total_revenue > 0 %}
                                            {% widthratio rent_revenue total_revenue 100 %}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </h3>
                                    <small class="text-muted">Rent / Total Revenue</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Breakdown Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
new Chart(revenueCtx, {
    type: 'doughnut',
    data: {
        labels: ['Rental Income', 'Amenities Income', 'Other Income'],
        datasets: [{
            data: [{{ rent_revenue }}, {{ amenities_revenue }}, {{ other_revenue }}],
            backgroundColor: ['#007bff', '#28a745', '#ffc107'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Tax Breakdown Chart
const taxCtx = document.getElementById('taxChart').getContext('2d');
new Chart(taxCtx, {
    type: 'bar',
    data: {
        labels: ['Rental Income Tax', 'Property Tax', 'VAT'],
        datasets: [{
            label: 'Tax Amount (KES)',
            data: [{{ rental_income_tax }}, {{ property_tax }}, {{ vat }}],
            backgroundColor: ['#dc3545', '#fd7e14', '#e83e8c'],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
@media print {
    .btn, .card-header-action, .main-sidebar, .navbar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

.table-bordered td {
    border: 1px solid #dee2e6;
}

.pl-4 {
    padding-left: 1.5rem;
}
</style>

{% include 'footer.html' %}
{% endblock %}
