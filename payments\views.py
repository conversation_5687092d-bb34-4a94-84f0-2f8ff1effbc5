import json
import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from rentals.models import Invoice, Payment
from .models import MpesaTransaction, MpesaConfiguration
from .services import MpesaService, MpesaAPIException, MpesaCallbackHandler
from .forms import MpesaPaymentForm
from core.decorators import require_any_member

logger = logging.getLogger(__name__)


@require_any_member
def initiate_payment(request, org_slug, invoice_id):
    """Initiate M-Pesa payment for an invoice"""
    invoice = get_object_or_404(Invoice, id=invoice_id, organization=request.org)

    if request.method == 'POST':
        form = MpesaPaymentForm(request.POST)
        if form.is_valid():
            phone_number = form.cleaned_data['phone_number']
            amount = form.cleaned_data['amount']

            # Validate amount doesn't exceed remaining balance
            remaining_balance = invoice.amount_due - invoice.amount_paid
            if amount > remaining_balance:
                messages.error(request, f'Amount cannot exceed remaining balance of KES {remaining_balance}')
                return render(request, 'payments/initiate_payment.html', {
                    'form': form,
                    'invoice': invoice
                })

            try:
                mpesa_service = MpesaService(request.org)
                transaction, response_data = mpesa_service.initiate_stk_push(
                    phone_number=phone_number,
                    amount=amount,
                    invoice=invoice
                )

                messages.success(request,
                    'Payment request sent to your phone. Please enter your M-Pesa PIN to complete the payment.')
                return redirect('payment_status', org_slug=org_slug, transaction_id=transaction.id)

            except MpesaAPIException as e:
                messages.error(request, f'Payment initiation failed: {str(e)}')
            except Exception as e:
                logger.error(f"Unexpected error initiating payment: {e}")
                messages.error(request, 'An unexpected error occurred. Please try again.')
    else:
        # Pre-fill form with invoice balance
        remaining_balance = invoice.amount_due - invoice.amount_paid
        form = MpesaPaymentForm(initial={'amount': remaining_balance})

    return render(request, 'payments/initiate_payment.html', {
        'form': form,
        'invoice': invoice
    })


@require_any_member
def payment_status(request, org_slug, transaction_id):
    """Display payment status"""
    transaction = get_object_or_404(
        MpesaTransaction,
        id=transaction_id,
        organization=request.org
    )

    return render(request, 'payments/payment_status.html', {
        'transaction': transaction
    })


@require_any_member
def check_payment_status(request, org_slug, transaction_id):
    """AJAX endpoint to check payment status"""
    transaction = get_object_or_404(
        MpesaTransaction,
        id=transaction_id,
        organization=request.org
    )

    # If transaction is still pending, query M-Pesa API for status
    if transaction.is_pending:
        try:
            mpesa_service = MpesaService(request.org)
            status_data = mpesa_service.query_transaction_status(transaction.checkout_request_id)

            # Update transaction based on query result
            result_code = status_data.get('ResultCode')
            if result_code is not None:
                transaction.result_code = str(result_code)
                transaction.result_desc = status_data.get('ResultDesc', '')

                if result_code == '0':
                    transaction.status = 'COMPLETED'
                elif result_code in ['1032', '1037']:  # User cancelled or timeout
                    transaction.status = 'CANCELLED'
                else:
                    transaction.status = 'FAILED'

                transaction.save()

        except MpesaAPIException as e:
            logger.error(f"Error checking payment status: {e}")

    return JsonResponse({
        'status': transaction.status,
        'is_successful': transaction.is_successful,
        'is_pending': transaction.is_pending,
        'is_failed': transaction.is_failed,
        'result_desc': transaction.result_desc,
        'mpesa_receipt_number': transaction.mpesa_receipt_number
    })


@csrf_exempt
@require_http_methods(["POST"])
def mpesa_callback(request):
    """Handle M-Pesa callback"""
    try:
        callback_data = json.loads(request.body.decode('utf-8'))
        logger.info(f"Received M-Pesa callback: {callback_data}")

        success = MpesaCallbackHandler.process_callback(callback_data)

        if success:
            return HttpResponse("OK", status=200)
        else:
            return HttpResponse("Error processing callback", status=400)

    except json.JSONDecodeError:
        logger.error("Invalid JSON in M-Pesa callback")
        return HttpResponse("Invalid JSON", status=400)
    except Exception as e:
        logger.error(f"Error processing M-Pesa callback: {e}")
        return HttpResponse("Internal server error", status=500)


@require_any_member
def payment_history(request, org_slug):
    """Display payment history for the organization"""
    payments = Payment.objects.filter(
        organization=request.org,
        method='MPESA'
    ).select_related('invoice', 'invoice__lease', 'invoice__lease__tenant')

    return render(request, 'payments/payment_history.html', {
        'payments': payments
    })


class TenantPaymentView(View):
    """Allow tenants to make payments for their invoices"""

    @method_decorator(login_required)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, org_slug):
        # Get tenant's unpaid invoices
        from core.models import Membership
        try:
            membership = Membership.objects.get(
                user=request.user,
                organization__slug=org_slug,
                role='TENANT'
            )

            # Find tenant's leases and unpaid invoices
            from rentals.models import Tenant, Lease
            tenant = Tenant.objects.filter(
                organization=membership.organization,
                email=request.user.email
            ).first()

            if not tenant:
                messages.error(request, 'Tenant profile not found.')
                return redirect('dashboard', org_slug=org_slug)

            unpaid_invoices = Invoice.objects.filter(
                organization=membership.organization,
                lease__tenant=tenant,
                is_paid=False
            ).select_related('lease', 'lease__unit', 'lease__unit__property')

            return render(request, 'payments/tenant_payment.html', {
                'invoices': unpaid_invoices,
                'tenant': tenant
            })

        except Membership.DoesNotExist:
            messages.error(request, 'Access denied.')
            return redirect('login')


@require_any_member
def invoice_payment_options(request, org_slug, invoice_id):
    """Display payment options for a specific invoice"""
    invoice = get_object_or_404(Invoice, id=invoice_id, organization=request.org)

    # Check if M-Pesa is configured for this organization
    mpesa_configured = MpesaConfiguration.objects.filter(
        organization=request.org,
        is_active=True
    ).exists()

    return render(request, 'payments/payment_options.html', {
        'invoice': invoice,
        'mpesa_configured': mpesa_configured
    })
