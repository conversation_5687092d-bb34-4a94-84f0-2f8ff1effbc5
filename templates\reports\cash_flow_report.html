{% extends 'base.html' %}
{% load org_urls %}
{% load report_filters %}
{% block title %}Cash Flow Report{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-chart-area text-info"></i> Cash Flow Report
                        </h1>
                        <div class="btn-group">
                            <a href="{% org_url 'financial_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                {% for item in cash_flow_data %}
                    {% if forloop.last %}
                    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                        <div class="card card-statistic-1">
                            <div class="card-icon bg-success">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="card-wrap">
                                <div class="card-header">
                                    <h4>Latest Month</h4>
                                </div>
                                <div class="card-body">
                                    KES {{ item.inflow|floatformat:0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
                
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>12-Month Total</h4>
                            </div>
                            <div class="card-body">
                                {% cash_flow_total cash_flow_data %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-info">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Monthly Average</h4>
                            </div>
                            <div class="card-body">
                                {% cash_flow_average cash_flow_data %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-warning">
                            <i class="fas fa-trend-up"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Growth Trend</h4>
                            </div>
                            <div class="card-body">
                                {% growth_trend cash_flow_data %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cash Flow Chart -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>12-Month Cash Flow Trend</h4>
                            <div class="card-header-action">
                                <span class="text-muted">{{ start_date|date:"M Y" }} - {{ end_date|date:"M Y" }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="cashFlowChart" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Breakdown Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Monthly Cash Flow Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Month</th>
                                            <th>Cash Inflow</th>
                                            <th>Cash Outflow</th>
                                            <th>Net Cash Flow</th>
                                            <th>Growth</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in cash_flow_data %}
                                        <tr>
                                            <td>
                                                <strong>{{ item.month_name }}</strong><br>
                                                <small class="text-muted">{{ item.month }}</small>
                                            </td>
                                            <td class="text-success">
                                                <i class="fas fa-arrow-up"></i> KES {{ item.inflow|floatformat:2 }}
                                            </td>
                                            <td class="text-danger">
                                                <i class="fas fa-arrow-down"></i> KES {{ item.outflow|floatformat:2 }}
                                            </td>
                                            <td class="{% if item.net_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                                                <strong>KES {{ item.net_flow|floatformat:2 }}</strong>
                                            </td>
                                            <td>
                                                {% if not forloop.first and forloop.counter0 > 0 %}
                                                    {% with prev_item=cash_flow_data|slice:forloop.counter0|last %}
                                                        {{ item.inflow|growth_indicator:prev_item.inflow|safe }}
                                                    {% endwith %}
                                                {% else %}
                                                    <span class="badge badge-secondary">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if item.net_flow > 50000 %}
                                                    <span class="badge badge-success">Excellent</span>
                                                {% elif item.net_flow > 20000 %}
                                                    <span class="badge badge-info">Good</span>
                                                {% elif item.net_flow > 0 %}
                                                    <span class="badge badge-warning">Fair</span>
                                                {% else %}
                                                    <span class="badge badge-danger">Poor</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                <i class="fas fa-chart-line"></i> No cash flow data available.
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cash Flow Analysis -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-analytics"></i> Cash Flow Analysis</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Key Insights:</h6>
                                    <ul class="list-unstyled">
                                        {% if cash_flow_data %}
                                            {% with latest=cash_flow_data|last %}
                                            <li class="mb-2">
                                                <i class="fas fa-info-circle text-info"></i>
                                                Latest month generated <strong>KES {{ latest.inflow|floatformat:2 }}</strong> in revenue
                                            </li>
                                            {% endwith %}
                                            
                                            {% if cash_flow_data|length >= 2 %}
                                                {% with first=cash_flow_data.0 last=cash_flow_data|last %}
                                                <li class="mb-2">
                                                    {% if last.inflow > first.inflow %}
                                                        <i class="fas fa-arrow-up text-success"></i>
                                                        Revenue increased over 12 months
                                                    {% elif last.inflow < first.inflow %}
                                                        <i class="fas fa-arrow-down text-danger"></i>
                                                        Revenue decreased over 12 months
                                                    {% else %}
                                                        <i class="fas fa-minus text-secondary"></i>
                                                        Revenue remained stable over 12 months
                                                    {% endif %}
                                                </li>
                                                {% endwith %}
                                            {% endif %}
                                            
                                            <li class="mb-2">
                                                <i class="fas fa-calendar text-primary"></i>
                                                Tracking <strong>{{ cash_flow_data|length }}</strong> months of cash flow data
                                            </li>
                                        {% else %}
                                            <li class="mb-2">
                                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                                No cash flow data available for analysis
                                            </li>
                                        {% endif %}
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Recommendations:</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-lightbulb text-warning"></i>
                                            Monitor monthly trends to identify seasonal patterns
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-target text-success"></i>
                                            Set monthly revenue targets based on historical data
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-chart-line text-info"></i>
                                            Track expense outflows to calculate true net cash flow
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-piggy-bank text-primary"></i>
                                            Build cash reserves during high-revenue months
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Cash Flow Chart
const ctx = document.getElementById('cashFlowChart').getContext('2d');
const cashFlowData = {{ cash_flow_json|safe }};

new Chart(ctx, {
    type: 'line',
    data: {
        labels: cashFlowData.map(item => item.month_name),
        datasets: [{
            label: 'Cash Inflow (KES)',
            data: cashFlowData.map(item => item.inflow),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Cash Outflow (KES)',
            data: cashFlowData.map(item => item.outflow),
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Net Cash Flow (KES)',
            data: cashFlowData.map(item => item.net_flow),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'KES ' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            legend: {
                position: 'top'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': KES ' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});
</script>

<style>
.card-statistic-1 {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

.card-statistic-1 .card-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 30px;
    color: white;
    position: absolute;
    right: 20px;
    top: 20px;
}

.card-statistic-1 .card-wrap {
    padding: 20px;
}

.card-statistic-1 .card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 10px;
}

.card-statistic-1 .card-body {
    font-size: 24px;
    font-weight: 700;
    color: #495057;
}

@media print {
    .btn, .card-header-action, .main-sidebar, .navbar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}
</style>

{% include 'footer.html' %}
{% endblock %}
