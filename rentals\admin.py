from django.contrib import admin
from .models import (
    Property, Unit, Tenant, Lease, Invoice, Payment, MaintenanceRequest,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem,
    TenantCreditBalance, AdvancePaymentAllocation
)
from core.models import Organization


class OrgScopedAdmin(admin.ModelAdmin):
    """Base admin class for organization-scoped models"""

    def save_model(self, request, obj, form, change):
        """Auto-assign organization if not set"""
        if not obj.organization_id:
            # Get the first organization or create a default one
            org = Organization.objects.first()
            if not org:
                org = Organization.objects.create(
                    name='Default Organization',
                    slug='default'
                )
            obj.organization = org
        super().save_model(request, obj, form, change)

    def get_list_filter(self, request):
        """Add organization filter to all org-scoped models"""
        filters = list(super().get_list_filter(request))
        if 'organization' not in filters:
            filters.insert(0, 'organization')
        return filters


@admin.register(Property)
class PropertyAdmin(OrgScopedAdmin):
    list_display = ('name', 'organization', 'address')


@admin.register(Unit)
class UnitAdmin(OrgScopedAdmin):
    list_display = ('code', 'property', 'organization', 'bedrooms', 'bathrooms', 'is_active')


@admin.register(Tenant)
class TenantAdmin(OrgScopedAdmin):
    list_display = ('first_name', 'last_name', 'email', 'organization')


@admin.register(Lease)
class LeaseAdmin(OrgScopedAdmin):
    list_display = ('unit', 'tenant', 'start_date', 'end_date', 'rent_amount', 'organization')


@admin.register(Invoice)
class InvoiceAdmin(OrgScopedAdmin):
    list_display = ('number', 'lease', 'issue_date', 'due_date', 'amount_due', 'amount_paid', 'is_paid', 'organization')


@admin.register(Payment)
class PaymentAdmin(OrgScopedAdmin):
    list_display = ('reference', 'date', 'invoice', 'lease', 'method', 'amount', 'is_advance_payment', 'months_covered', 'status', 'organization')
    list_filter = ('is_advance_payment', 'method', 'status', 'date')
    search_fields = ('reference', 'invoice__number', 'lease__tenant__first_name', 'lease__tenant__last_name')


@admin.register(MaintenanceRequest)
class MaintenanceRequestAdmin(OrgScopedAdmin):
    list_display = ('title', 'unit', 'priority', 'status', 'organization', 'created_at')


@admin.register(AmenityType)
class AmenityTypeAdmin(OrgScopedAdmin):
    list_display = ['name', 'billing_cycle', 'default_amount', 'is_mandatory', 'is_active', 'organization', 'created_at']
    list_filter = ['billing_cycle', 'is_mandatory', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('organization', 'name', 'description', 'billing_cycle', 'default_amount')
        }),
        ('Settings', {
            'fields': ('is_mandatory', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UnitAmenity)
class UnitAmenityAdmin(OrgScopedAdmin):
    list_display = ['unit', 'amenity_type', 'amount', 'is_active', 'start_date', 'end_date', 'organization']
    list_filter = ['amenity_type', 'is_active', 'start_date']
    search_fields = ['unit__code', 'amenity_type__name']
    readonly_fields = ['created_at']

    def amount(self, obj):
        return obj.amount
    amount.short_description = 'Amount'


@admin.register(GovernmentTax)
class GovernmentTaxAdmin(OrgScopedAdmin):
    list_display = ['name', 'tax_type', 'calculation_method', 'rate', 'is_active', 'effective_date', 'organization']
    list_filter = ['tax_type', 'calculation_method', 'is_active', 'effective_date']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('organization', 'name', 'tax_type', 'description')
        }),
        ('Calculation', {
            'fields': ('calculation_method', 'rate')
        }),
        ('Settings', {
            'fields': ('is_active', 'effective_date')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class InvoiceLineItemInline(admin.TabularInline):
    model = InvoiceLineItem
    extra = 1
    readonly_fields = ['amount', 'created_at']
    exclude = ['organization']  # Hide organization field, will be auto-set


@admin.register(InvoiceLineItem)
class InvoiceLineItemAdmin(OrgScopedAdmin):
    list_display = ['invoice', 'item_type', 'description', 'quantity', 'unit_price', 'amount', 'organization']
    list_filter = ['item_type', 'created_at']
    search_fields = ['invoice__number', 'description']
    readonly_fields = ['amount', 'created_at']


@admin.register(TenantCreditBalance)
class TenantCreditBalanceAdmin(OrgScopedAdmin):
    list_display = ['lease', 'balance', 'last_updated', 'organization']
    list_filter = ['last_updated']
    search_fields = ['lease__tenant__first_name', 'lease__tenant__last_name', 'lease__unit__code']
    readonly_fields = ['last_updated']


@admin.register(AdvancePaymentAllocation)
class AdvancePaymentAllocationAdmin(OrgScopedAdmin):
    list_display = ['payment', 'invoice', 'amount_allocated', 'allocation_date', 'organization']
    list_filter = ['allocation_date']
    search_fields = ['payment__reference', 'invoice__number']
    readonly_fields = ['allocation_date']