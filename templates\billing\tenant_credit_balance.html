{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-wallet"></i> Tenant Credit Balance</h2>
                <div>
                    <a href="{% org_url 'record_advance_payment' lease_id=lease.pk %}" class="btn btn-success">
                        <i class="fas fa-plus-circle"></i> Record Advance Payment
                    </a>
                    <a href="{% org_url 'lease_detail' pk=lease.pk %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Lease
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Tenant Info Card -->
                <div class="col-md-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-user"></i> Tenant Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <th>Name:</th>
                                    <td><strong>{{ lease.tenant }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Unit:</th>
                                    <td>{{ lease.unit }}</td>
                                </tr>
                                <tr>
                                    <th>Monthly Rent:</th>
                                    <td><strong class="text-success">KES {{ lease.rent_amount|floatformat:2 }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Lease Status:</th>
                                    <td>
                                        {% if lease.status == 'ACTIVE' %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ lease.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Credit Balance Summary Card -->
                    <div class="card shadow-sm mb-4 border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-piggy-bank"></i> Credit Balance</h5>
                        </div>
                        <div class="card-body text-center">
                            <h1 class="display-4 text-success mb-3">
                                KES {{ credit_balance.balance|floatformat:2 }}
                            </h1>
                            <p class="text-muted mb-0">
                                <small>Last updated: {{ credit_balance.last_updated|date:"M d, Y H:i" }}</small>
                            </p>
                            {% if credit_balance.balance > 0 %}
                            <hr>
                            <p class="mb-0">
                                <i class="fas fa-calendar-alt"></i> 
                                Covers approximately <strong>{{ credit_balance.balance|floatformat:0|add:"0"|floatformat:0|divisibleby:lease.rent_amount|yesno:"1,0" }}</strong> month(s) of rent
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Summary Stats Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Summary Statistics</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm mb-0">
                                <tr>
                                    <th>Total Paid in Advance:</th>
                                    <td class="text-end"><strong>KES {{ total_advance_paid|floatformat:2 }}</strong></td>
                                </tr>
                                <tr>
                                    <th>Total Allocated:</th>
                                    <td class="text-end"><strong>KES {{ total_allocated|floatformat:2 }}</strong></td>
                                </tr>
                                <tr class="table-success">
                                    <th>Current Balance:</th>
                                    <td class="text-end"><strong>KES {{ credit_balance.balance|floatformat:2 }}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Transactions Section -->
                <div class="col-md-8">
                    <!-- Advance Payments Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Advance Payments</h5>
                        </div>
                        <div class="card-body">
                            {% if advance_payments %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Reference</th>
                                            <th>Method</th>
                                            <th>Months</th>
                                            <th>Amount</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in advance_payments %}
                                        <tr>
                                            <td>{{ payment.date|date:"M d, Y" }}</td>
                                            <td><code>{{ payment.reference }}</code></td>
                                            <td>
                                                {% if payment.method == 'MPESA' %}
                                                    <i class="fas fa-mobile-alt text-success"></i> M-Pesa
                                                {% elif payment.method == 'CASH' %}
                                                    <i class="fas fa-money-bill-wave text-primary"></i> Cash
                                                {% elif payment.method == 'BANK_TRANSFER' %}
                                                    <i class="fas fa-university text-info"></i> Bank
                                                {% elif payment.method == 'CHEQUE' %}
                                                    <i class="fas fa-money-check text-warning"></i> Cheque
                                                {% else %}
                                                    {{ payment.method }}
                                                {% endif %}
                                            </td>
                                            <td><span class="badge bg-info">{{ payment.months_covered }} month(s)</span></td>
                                            <td><strong class="text-success">KES {{ payment.amount|floatformat:2 }}</strong></td>
                                            <td>
                                                {% if payment.notes %}
                                                    <small class="text-muted">{{ payment.notes|truncatewords:10 }}</small>
                                                {% else %}
                                                    <small class="text-muted">-</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-success">
                                        <tr>
                                            <th colspan="4">Total Advance Payments:</th>
                                            <th colspan="2">KES {{ total_advance_paid|floatformat:2 }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> No advance payments recorded yet.
                                <a href="{% org_url 'record_advance_payment' lease_id=lease.pk %}" class="alert-link">Record one now</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Allocations Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-exchange-alt"></i> Credit Allocations</h5>
                        </div>
                        <div class="card-body">
                            {% if allocations %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Payment Ref</th>
                                            <th>Invoice</th>
                                            <th>Amount</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for allocation in allocations %}
                                        <tr>
                                            <td>{{ allocation.allocation_date|date:"M d, Y H:i" }}</td>
                                            <td><code>{{ allocation.payment.reference }}</code></td>
                                            <td>
                                                <a href="{% org_url 'invoice_detail' pk=allocation.invoice.pk %}">
                                                    {{ allocation.invoice.number }}
                                                </a>
                                            </td>
                                            <td><strong class="text-primary">KES {{ allocation.amount_allocated|floatformat:2 }}</strong></td>
                                            <td>
                                                {% if allocation.notes %}
                                                    <small class="text-muted">{{ allocation.notes|truncatewords:10 }}</small>
                                                {% else %}
                                                    <small class="text-muted">-</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-primary">
                                        <tr>
                                            <th colspan="3">Total Allocated:</th>
                                            <th colspan="2">KES {{ total_allocated|floatformat:2 }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> No credit allocations yet. Credit will be automatically allocated to future invoices.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

