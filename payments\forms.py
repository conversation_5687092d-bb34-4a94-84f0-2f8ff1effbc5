from django import forms
from decimal import Decimal
import re


class MpesaPaymentForm(forms.Form):
    """Form for initiating M-Pesa payments"""
    
    phone_number = forms.CharField(
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '0712345678 or +254712345678',
            'pattern': r'^(\+?254|0)[17]\d{8}$'
        }),
        help_text='Enter your M-Pesa registered phone number'
    )
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=Decimal('1.00'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'min': '1.00'
        }),
        help_text='Enter the amount to pay (minimum KES 1.00)'
    )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data['phone_number'].strip()
        
        # Remove any spaces or dashes
        phone_number = re.sub(r'[\s\-]', '', phone_number)
        
        # Validate Kenyan phone number format
        if not re.match(r'^(\+?254|0)[17]\d{8}$', phone_number):
            raise forms.ValidationError(
                'Please enter a valid Kenyan phone number (e.g., 0712345678 or +254712345678)'
            )
        
        # Normalize to international format
        if phone_number.startswith('0'):
            phone_number = '254' + phone_number[1:]
        elif phone_number.startswith('+254'):
            phone_number = phone_number[1:]
        elif not phone_number.startswith('254'):
            phone_number = '254' + phone_number
        
        return phone_number
    
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        
        if amount < Decimal('1.00'):
            raise forms.ValidationError('Minimum payment amount is KES 1.00')
        
        if amount > Decimal('999999.99'):
            raise forms.ValidationError('Maximum payment amount is KES 999,999.99')
        
        return amount


class MpesaConfigurationForm(forms.Form):
    """Form for configuring M-Pesa settings"""
    
    consumer_key = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter Consumer Key from Daraja'
        }),
        help_text='Consumer Key from your Daraja app'
    )
    
    consumer_secret = forms.CharField(
        max_length=100,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter Consumer Secret from Daraja'
        }),
        help_text='Consumer Secret from your Daraja app'
    )
    
    business_short_code = forms.CharField(
        max_length=10,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g., 174379'
        }),
        help_text='Your business short code (Paybill or Till number)'
    )
    
    passkey = forms.CharField(
        max_length=200,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter Passkey from Daraja'
        }),
        help_text='Passkey from your Daraja app'
    )
    
    callback_url = forms.URLField(
        widget=forms.URLInput(attrs={
            'class': 'form-control',
            'placeholder': 'https://yourdomain.com/payments/callback/'
        }),
        help_text='URL where M-Pesa will send payment notifications'
    )
    
    is_sandbox = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text='Check if using sandbox environment for testing'
    )
    
    def clean_business_short_code(self):
        short_code = self.cleaned_data['business_short_code'].strip()
        
        if not re.match(r'^\d{5,7}$', short_code):
            raise forms.ValidationError(
                'Business short code must be 5-7 digits'
            )
        
        return short_code
    
    def clean_callback_url(self):
        callback_url = self.cleaned_data['callback_url']
        
        if not callback_url.startswith('https://'):
            raise forms.ValidationError(
                'Callback URL must use HTTPS for security'
            )
        
        return callback_url


class QuickPaymentForm(forms.Form):
    """Simplified form for quick payments"""
    
    invoice_id = forms.IntegerField(widget=forms.HiddenInput())
    phone_number = forms.CharField(
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-sm',
            'placeholder': '0712345678'
        })
    )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data['phone_number'].strip()
        phone_number = re.sub(r'[\s\-]', '', phone_number)
        
        if not re.match(r'^(\+?254|0)[17]\d{8}$', phone_number):
            raise forms.ValidationError('Invalid phone number format')
        
        # Normalize to international format
        if phone_number.startswith('0'):
            phone_number = '254' + phone_number[1:]
        elif phone_number.startswith('+254'):
            phone_number = phone_number[1:]
        elif not phone_number.startswith('254'):
            phone_number = '254' + phone_number
        
        return phone_number
