from django.db.models import Sum, Count, Q, Avg, F, Case, When, DecimalField
from django.db.models.functions import <PERSON>runcMonth, TruncYear, TruncQuarter, Coalesce
from django.utils import timezone
from datetime import datetime, timedelta, date
from decimal import Decimal
from .models import (
    Property, Unit, Tenant, Lease, Invoice, Payment,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem, MaintenanceRequest
)
import json


class DashboardService:
    """Service class to provide comprehensive dashboard data for multi-tenant platform"""
    
    def __init__(self, organization):
        self.org = organization
        self.current_date = timezone.now().date()
        self.current_month = self.current_date.replace(day=1)
        self.last_month = (self.current_month - timedelta(days=1)).replace(day=1)
        self.current_year = self.current_date.year
        
    def get_quick_stats(self):
        """Get quick stats for the banner section"""
        # Portfolio Health Score (0-100)
        total_units = Unit.objects.filter(organization=self.org).count()
        occupied_units = self._get_occupied_units_count()
        occupancy_rate = (occupied_units / total_units * 100) if total_units > 0 else 0
        
        # Collection rate
        collection_rate = self._get_collection_rate()
        
        # Maintenance efficiency (fewer open requests = better score)
        open_maintenance = MaintenanceRequest.objects.filter(
            organization=self.org, 
            status__in=['PENDING', 'IN_PROGRESS']
        ).count()
        maintenance_score = max(0, 100 - (open_maintenance * 5))  # Deduct 5 points per open request
        
        # Overall portfolio health (weighted average)
        portfolio_health = (occupancy_rate * 0.4 + collection_rate * 0.4 + maintenance_score * 0.2)
        
        # Monthly revenue
        monthly_revenue = self._get_monthly_revenue()
        
        # Pending actions count
        pending_actions = self._get_pending_actions_count()
        
        return {
            'portfolio_health': round(portfolio_health, 1),
            'monthly_revenue': monthly_revenue,
            'occupancy_rate': round(occupancy_rate, 1),
            'pending_actions': pending_actions
        }
    
    def get_kpi_cards(self):
        """Get KPI cards data with trends"""
        # Get current and previous month data for trends
        current_properties = Property.objects.filter(organization=self.org).count()
        current_units = Unit.objects.filter(organization=self.org).count()
        current_leases = Lease.objects.filter(organization=self.org, status='ACTIVE').count()
        current_unpaid = Invoice.objects.filter(organization=self.org, is_paid=False).count()
        
        # Calculate trends (simplified - in real app, you'd store historical data)
        return {
            'properties_total': current_properties,
            'properties_trend': '+2',  # Could be calculated from historical data
            'units_total': current_units,
            'units_trend': '+5',
            'leases_active': current_leases,
            'leases_trend': '+3',
            'invoices_unpaid': current_unpaid,
            'invoices_trend': '-2'
        }
    
    def get_revenue_overview(self, period='monthly'):
        """Get revenue overview data for different periods"""
        if period == 'monthly':
            return self._get_monthly_revenue_data()
        elif period == 'quarterly':
            return self._get_quarterly_revenue_data()
        elif period == 'yearly':
            return self._get_yearly_revenue_data()
        else:
            return self._get_monthly_revenue_data()
    
    def get_property_performance(self):
        """Get property performance summary"""
        properties = Property.objects.filter(organization=self.org).annotate(
            total_units=Count('unit'),
            occupied_units=Count('unit__lease', filter=Q(
                unit__lease__status='ACTIVE',
                unit__lease__start_date__lte=self.current_date,
                unit__lease__end_date__gte=self.current_date
            )),
            monthly_revenue=Sum('unit__lease__rent_amount', filter=Q(
                unit__lease__status='ACTIVE',
                unit__lease__start_date__lte=self.current_date,
                unit__lease__end_date__gte=self.current_date
            )),
            maintenance_requests=Count('unit__maintenancerequest', filter=Q(
                unit__maintenancerequest__status__in=['PENDING', 'IN_PROGRESS']
            ))
        )
        
        # Calculate performance scores
        performance_data = []
        total_score = 0
        
        for prop in properties:
            occupancy_rate = (prop.occupied_units / prop.total_units * 100) if prop.total_units > 0 else 0

            # Handle None values for monthly_revenue and convert to float
            monthly_revenue = float(prop.monthly_revenue or 0)
            revenue_per_unit = (monthly_revenue / prop.total_units) if prop.total_units > 0 else 0
            maintenance_efficiency = max(0, 100 - (prop.maintenance_requests * 10))

            # Weighted performance score (ensure all values are float)
            performance_score = (
                float(occupancy_rate) * 0.4 +
                min(100, float(revenue_per_unit) / 1000) * 0.4 +  # Normalize revenue
                float(maintenance_efficiency) * 0.2
            )
            
            performance_data.append({
                'property': prop,
                'score': round(float(performance_score), 1),
                'occupancy_rate': round(float(occupancy_rate), 1),
                'revenue_per_unit': float(revenue_per_unit or 0),
                'maintenance_requests': prop.maintenance_requests
            })
            total_score += performance_score
        
        # Categorize properties
        top_performers = [p for p in performance_data if p['score'] >= 80]
        average_performers = [p for p in performance_data if 60 <= p['score'] < 80]
        needs_attention = [p for p in performance_data if p['score'] < 60]
        
        portfolio_score = (total_score / len(performance_data)) if performance_data else 0
        
        return {
            'portfolio_score': round(portfolio_score, 1),
            'top_performers': len(top_performers),
            'average_performers': len(average_performers),
            'needs_attention': len(needs_attention),
            'properties': performance_data
        }
    
    def get_portfolio_insights(self):
        """Get actionable portfolio insights"""
        insights = []
        
        # Revenue growth insight
        current_revenue = float(self._get_monthly_revenue())
        last_month_revenue = float(self._get_monthly_revenue(self.last_month))
        if last_month_revenue > 0:
            growth_rate = ((current_revenue - last_month_revenue) / last_month_revenue) * 100
            insights.append({
                'type': 'success' if growth_rate > 0 else 'warning',
                'icon': 'fas fa-arrow-up' if growth_rate > 0 else 'fas fa-arrow-down',
                'title': 'Revenue Growth',
                'value': f"{'+' if growth_rate > 0 else ''}{growth_rate:.1f}%",
                'description': f"{'Strong' if growth_rate > 5 else 'Moderate' if growth_rate > 0 else 'Declining'} performance this month with {'consistent growth' if growth_rate > 0 else 'revenue decline'} across portfolio."
            })
        
        # Properties needing attention
        low_performing = Property.objects.filter(organization=self.org).annotate(
            occupancy_rate=Case(
                When(unit__isnull=True, then=0),
                default=Count('unit__lease', filter=Q(
                    unit__lease__status='ACTIVE',
                    unit__lease__start_date__lte=self.current_date,
                    unit__lease__end_date__gte=self.current_date
                )) * 100.0 / Count('unit'),
                output_field=DecimalField(max_digits=5, decimal_places=2)
            )
        ).filter(occupancy_rate__lt=70)
        
        if low_performing.exists():
            insights.append({
                'type': 'warning',
                'icon': 'fas fa-exclamation-triangle',
                'title': 'Attention Needed',
                'value': f"{low_performing.count()} Properties",
                'description': f"{low_performing.first().name if low_performing.first() else 'Properties'} require immediate attention for vacancy issues."
            })
        
        # Lease renewals
        upcoming_renewals = Lease.objects.filter(
            organization=self.org,
            status='ACTIVE',
            end_date__lte=self.current_date + timedelta(days=30),
            end_date__gte=self.current_date
        ).count()
        
        if upcoming_renewals > 0:
            insights.append({
                'type': 'info',
                'icon': 'fas fa-calendar-check',
                'title': 'Lease Renewals',
                'value': f"{upcoming_renewals} Due",
                'description': f"{upcoming_renewals} leases expiring in the next 30 days. Start renewal process early."
            })
        
        return insights
    
    def get_latest_invoices(self, limit=10):
        """Get latest invoices with enhanced data"""
        return Invoice.objects.filter(organization=self.org).select_related(
            'lease__unit__property', 'lease__tenant'
        ).order_by('-created_at')[:limit]
    
    # Private helper methods
    def _get_occupied_units_count(self):
        """Get count of currently occupied units"""
        return Unit.objects.filter(
            organization=self.org,
            lease__status='ACTIVE',
            lease__start_date__lte=self.current_date,
            lease__end_date__gte=self.current_date
        ).distinct().count()
    
    def _get_collection_rate(self):
        """Calculate collection rate for current month"""
        current_month_invoices = Invoice.objects.filter(
            organization=self.org,
            issue_date__year=self.current_date.year,
            issue_date__month=self.current_date.month
        )

        total_due = current_month_invoices.aggregate(
            total=Coalesce(Sum('amount_due'), Decimal('0'))
        )['total'] or Decimal('0')

        total_paid = current_month_invoices.aggregate(
            total=Coalesce(Sum('amount_paid'), Decimal('0'))
        )['total'] or Decimal('0')

        if total_due > 0:
            return float((total_paid / total_due) * 100)
        return 100.0
    
    def _get_monthly_revenue(self, month_date=None):
        """Get monthly revenue for specified month or current month"""
        if month_date is None:
            month_date = self.current_date

        monthly_revenue = Invoice.objects.filter(
            organization=self.org,
            issue_date__year=month_date.year,
            issue_date__month=month_date.month,
            is_paid=True
        ).aggregate(
            total=Coalesce(Sum('amount_paid'), Decimal('0'))
        )['total'] or Decimal('0')

        return float(monthly_revenue)
    
    def _get_pending_actions_count(self):
        """Get count of pending actions requiring attention"""
        overdue_invoices = Invoice.objects.filter(
            organization=self.org,
            is_paid=False,
            due_date__lt=self.current_date
        ).count()
        
        open_maintenance = MaintenanceRequest.objects.filter(
            organization=self.org,
            status__in=['PENDING', 'IN_PROGRESS']
        ).count()
        
        expiring_leases = Lease.objects.filter(
            organization=self.org,
            status='ACTIVE',
            end_date__lte=self.current_date + timedelta(days=30),
            end_date__gte=self.current_date
        ).count()
        
        return overdue_invoices + open_maintenance + expiring_leases
    
    def _get_monthly_revenue_data(self):
        """Get monthly revenue data for the current year"""
        monthly_data = Invoice.objects.filter(
            organization=self.org,
            issue_date__year=self.current_year,
            is_paid=True
        ).annotate(
            month=TruncMonth('issue_date')
        ).values('month').annotate(
            revenue=Coalesce(Sum('amount_paid'), Decimal('0'))
        ).order_by('month')
        
        # Create 12-month data array
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        revenue_data = [0] * 12
        
        for item in monthly_data:
            month_index = item['month'].month - 1
            revenue_data[month_index] = float(item['revenue'] or 0)

        # Calculate targets (could be from a targets table)
        avg_revenue = sum(revenue_data) / len([r for r in revenue_data if r > 0]) if any(revenue_data) else 0
        target_data = [avg_revenue * 1.1] * 12  # 10% above average as target
        
        return {
            'labels': months,
            'actual': revenue_data,
            'target': target_data,
            'metrics': self._calculate_revenue_metrics(revenue_data)
        }
    
    def _get_quarterly_revenue_data(self):
        """Get quarterly revenue data"""
        quarterly_data = Invoice.objects.filter(
            organization=self.org,
            issue_date__year=self.current_year,
            is_paid=True
        ).annotate(
            quarter=TruncQuarter('issue_date')
        ).values('quarter').annotate(
            revenue=Coalesce(Sum('amount_paid'), Decimal('0'))
        ).order_by('quarter')
        
        labels = [f'Q{i} {self.current_year}' for i in range(1, 5)]
        revenue_data = [0] * 4
        
        for item in quarterly_data:
            quarter_index = ((item['quarter'].month - 1) // 3)
            revenue_data[quarter_index] = float(item['revenue'] or 0)

        avg_revenue = sum(revenue_data) / len([r for r in revenue_data if r > 0]) if any(revenue_data) else 0
        target_data = [avg_revenue * 1.1] * 4
        
        return {
            'labels': labels,
            'actual': revenue_data,
            'target': target_data,
            'metrics': self._calculate_revenue_metrics(revenue_data, 'quarterly')
        }
    
    def _get_yearly_revenue_data(self):
        """Get yearly revenue data for the last 5 years"""
        start_year = self.current_year - 4
        yearly_data = Invoice.objects.filter(
            organization=self.org,
            issue_date__year__gte=start_year,
            is_paid=True
        ).annotate(
            year=TruncYear('issue_date')
        ).values('year').annotate(
            revenue=Coalesce(Sum('amount_paid'), Decimal('0'))
        ).order_by('year')
        
        labels = [str(start_year + i) for i in range(5)]
        revenue_data = [0] * 5
        
        for item in yearly_data:
            year_index = item['year'].year - start_year
            if 0 <= year_index < 5:
                revenue_data[year_index] = float(item['revenue'] or 0)

        avg_revenue = sum(revenue_data) / len([r for r in revenue_data if r > 0]) if any(revenue_data) else 0
        target_data = [avg_revenue * 1.05] * 5  # 5% above average as target
        
        return {
            'labels': labels,
            'actual': revenue_data,
            'target': target_data,
            'metrics': self._calculate_revenue_metrics(revenue_data, 'yearly')
        }
    
    def _calculate_revenue_metrics(self, revenue_data, period='monthly'):
        """Calculate revenue metrics for different periods"""
        current_revenue = float(revenue_data[-1]) if revenue_data else 0.0
        previous_revenue = float(revenue_data[-2]) if len(revenue_data) > 1 else 0.0

        growth_rate = 0.0
        if previous_revenue > 0:
            growth_rate = ((current_revenue - previous_revenue) / previous_revenue) * 100

        # Calculate average per unit
        total_units = Unit.objects.filter(organization=self.org).count()
        avg_per_unit = (current_revenue / total_units) if total_units > 0 else 0.0

        # Collection rate
        collection_rate = float(self._get_collection_rate())
        
        period_labels = {
            'monthly': 'This Month',
            'quarterly': 'This Quarter', 
            'yearly': 'This Year'
        }
        
        return {
            'current': f"KES {current_revenue/1000000:.1f}M" if current_revenue >= 1000000 else f"KES {current_revenue/1000:.0f}K",
            'growth': f"{'+' if growth_rate > 0 else ''}{growth_rate:.1f}%",
            'avg_per_unit': f"KES {avg_per_unit/1000:.0f}K" if avg_per_unit >= 1000 else f"KES {avg_per_unit:.0f}",
            'collection': f"{collection_rate:.1f}%",
            'period_label': period_labels.get(period, 'This Month')
        }
