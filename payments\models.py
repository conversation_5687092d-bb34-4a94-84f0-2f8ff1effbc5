from django.db import models
from core.models import OrgScopedModel
from rentals.models import Invoice
from decimal import Decimal


class MpesaConfiguration(OrgScopedModel):
    """Store M-Pesa API configuration per organization"""
    consumer_key = models.CharField(max_length=100)
    consumer_secret = models.CharField(max_length=100)
    business_short_code = models.CharField(max_length=10)
    passkey = models.CharField(max_length=200)
    callback_url = models.URLField()
    is_sandbox = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('organization',)

    def __str__(self):
        return f"M-Pesa Config for {self.organization.name}"


class MpesaTransaction(OrgScopedModel):
    """Track M-Pesa transaction details"""
    STATUS_CHOICES = [
        ('INITIATED', 'Initiated'),
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('TIMEOUT', 'Timeout'),
    ]

    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='mpesa_transactions')
    phone_number = models.CharField(max_length=15)
    amount = models.DecimalField(max_digits=10, decimal_places=2)

    # M-Pesa API fields
    checkout_request_id = models.CharField(max_length=100, unique=True)
    merchant_request_id = models.CharField(max_length=100, blank=True, null=True)
    mpesa_receipt_number = models.CharField(max_length=50, blank=True, null=True)
    transaction_date = models.DateTimeField(blank=True, null=True)

    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='INITIATED')
    result_code = models.CharField(max_length=10, blank=True, null=True)
    result_desc = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"M-Pesa Transaction {self.checkout_request_id} - {self.status}"

    @property
    def is_successful(self):
        return self.status == 'COMPLETED' and self.result_code == '0'

    @property
    def is_pending(self):
        return self.status in ['INITIATED', 'PENDING']

    @property
    def is_failed(self):
        return self.status in ['FAILED', 'CANCELLED', 'TIMEOUT'] or (
            self.result_code and self.result_code != '0'
        )
