{% load static %}
{% load org_urls %}
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title>RentalX - {% block title %}{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'assets/img/favicon.ico' %}">

    <!-- Core CSS -->
    <link rel="stylesheet" href="{% static 'assets/css/app.min.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/style.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/components.css' %}">
    <link rel="stylesheet" href="{% static 'assets/css/custom.css' %}">

    <!-- Vendor CSS -->
    <link rel="stylesheet" href="{% static 'assets/bundles/bootstrap-social/bootstrap-social.css' %}">
    <link rel="stylesheet" href="{% static 'assets/bundles/izitoast/css/iziToast.min.css' %}">
    <link rel="stylesheet" href="{% static 'assets/bundles/datatables/datatables.min.css' %}">   
    
</head>
<body>
    <div class="container-fluid p-0">
            <!-- Django Messages -->
            {% if messages %}
                <div class="position-fixed top-0 end-0 p-3" style="z-index: 9999;">
                    {% for message in messages %}
                        <div class="toast show align-items-center text-white bg-{{ message.tags|default:'info' }} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    {% if message.tags == 'error' or message.tags == 'danger' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                    {% elif message.tags == 'success' %}
                                        <i class="fas fa-check-circle"></i>
                                    {% elif message.tags == 'warning' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle"></i>
                                    {% endif %}
                                    <strong>{{ message }}</strong>
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            {% block content %}
            {% endblock %}
    </div>

    <!-- Scripts -->
    <script src="{% static 'assets/js/app.min.js' %}"></script>
    <script src="{% static 'assets/js/scripts.js' %}"></script>
    <script src="{% static 'assets/js/custom.js' %}"></script>
    <script src="{% static 'assets/js/page/datatables.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/datatables.min.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/dataTables.buttons.min.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/buttons.flash.min.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/jszip.min.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/pdfmake.min.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/vfs_fonts.js' %}"></script>
    <script src="{% static 'assets/bundles/datatables/export-tables/buttons.print.min.js' %}"></script>
    <script src="{% static 'assets/bundles/izitoast/js/iziToast.min.js' %}"></script>
    <!-- Auto-dismiss messages after 5 seconds -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var toasts = document.querySelectorAll('.toast');
            toasts.forEach(function(toast) {
                setTimeout(function() {
                    var bsToast = new bootstrap.Toast(toast);
                    bsToast.hide();
                }, 5000); // 5 seconds
            });
        });
    </script>

    

</body>
</html>
