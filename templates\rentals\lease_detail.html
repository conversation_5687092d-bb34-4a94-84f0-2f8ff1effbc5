{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <!-- Page Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-file-contract"></i> Lease Details</h2>
                        <div>
                            <a href="{% org_url 'record_advance_payment' lease_id=lease.pk %}" class="btn btn-success">
                                <i class="fas fa-calendar-plus"></i> Record Advance Payment
                            </a>
                            <a href="{% org_url 'tenant_credit_balance' lease_id=lease.pk %}" class="btn btn-info">
                                <i class="fas fa-wallet"></i> View Credit Balance
                            </a>
                            <a href="{% org_url 'lease_edit' pk=lease.pk %}" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Lease
                            </a>
                            <a href="{% org_url 'lease_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Leases
                            </a>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Lease Information Card -->
                        <div class="col-md-4">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Lease Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <th>Tenant:</th>
                                            <td><strong>{{ lease.tenant }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Unit:</th>
                                            <td>{{ lease.unit.property.name }} - {{ lease.unit.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>Start Date:</th>
                                            <td>{{ lease.start_date|date:"M d, Y" }}</td>
                                        </tr>
                                        <tr>
                                            <th>End Date:</th>
                                            <td>{{ lease.end_date|date:"M d, Y"|default:"Ongoing" }}</td>
                                        </tr>
                                        <tr>
                                            <th>Monthly Rent:</th>
                                            <td><strong class="text-success">KES {{ lease.rent_amount|floatformat:2 }}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>Deposit:</th>
                                            <td>KES {{ lease.deposit_amount|floatformat:2 }}</td>
                                        </tr>
                                        <tr>
                                            <th>Billing Day:</th>
                                            <td>{{ lease.billing_day }} of each month</td>
                                        </tr>
                                        <tr>
                                            <th>Status:</th>
                                            <td>
                                                {% if lease.status == 'ACTIVE' %}
                                                    <span class="badge bg-success">Active</span>
                                                {% elif lease.status == 'PENDING' %}
                                                    <span class="badge bg-warning">Pending</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ lease.status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Credit Balance Card -->
                            <div class="card shadow-sm mb-4 {% if credit_balance.balance > 0 %}border-success{% endif %}">
                                <div class="card-header {% if credit_balance.balance > 0 %}bg-success text-white{% else %}bg-secondary text-white{% endif %}">
                                    <h5 class="mb-0"><i class="fas fa-piggy-bank"></i> Credit Balance</h5>
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="display-5 {% if credit_balance.balance > 0 %}text-success{% else %}text-muted{% endif %} mb-3">
                                        KES {{ credit_balance.balance|floatformat:2 }}
                                    </h2>
                                    {% if credit_balance.balance > 0 %}
                                    <p class="text-muted mb-3">
                                        <i class="fas fa-calendar-alt"></i> 
                                        Covers approximately <strong>{{ credit_balance.balance|floatformat:0|add:"0"|floatformat:0|divisibleby:lease.rent_amount|yesno:"1,0" }}</strong> month(s)
                                    </p>
                                    <a href="{% org_url 'tenant_credit_balance' lease_id=lease.pk %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                    {% else %}
                                    <p class="text-muted mb-3">No advance payments on record</p>
                                    <a href="{% org_url 'record_advance_payment' lease_id=lease.pk %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> Record Advance Payment
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Invoices and Payments Section -->
                        <div class="col-md-8">
                            <!-- Invoices Card -->
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0"><i class="fas fa-file-invoice-dollar"></i> Invoices</h5>
                                </div>
                                <div class="card-body">
                                    {% if invoices %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Invoice #</th>
                                                    <th>Issue Date</th>
                                                    <th>Due Date</th>
                                                    <th>Amount</th>
                                                    <th>Paid</th>
                                                    <th>Balance</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for invoice in invoices %}
                                                <tr>
                                                    <td><code>{{ invoice.number }}</code></td>
                                                    <td>{{ invoice.issue_date|date:"M d, Y" }}</td>
                                                    <td>{{ invoice.due_date|date:"M d, Y" }}</td>
                                                    <td>KES {{ invoice.amount_due|floatformat:2 }}</td>
                                                    <td>KES {{ invoice.amount_paid|floatformat:2 }}</td>
                                                    <td>
                                                        {% if invoice.balance > 0 %}
                                                            <strong class="text-danger">KES {{ invoice.balance|floatformat:2 }}</strong>
                                                        {% else %}
                                                            <span class="text-muted">-</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if invoice.is_paid %}
                                                            <span class="badge bg-success">Paid</span>
                                                        {% else %}
                                                            <span class="badge bg-warning">Unpaid</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="{% org_url 'invoice_detail' pk=invoice.pk %}" class="btn btn-outline-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            {% if not invoice.is_paid %}
                                                            <a href="{% org_url 'quick_pay_invoice' invoice_id=invoice.pk %}" class="btn btn-outline-success">
                                                                <i class="fas fa-money-bill-wave"></i> Pay
                                                            </a>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> No invoices found for this lease.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Payments Card -->
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-money-bill-wave"></i> Payments</h5>
                                </div>
                                <div class="card-body">
                                    {% if payments %}
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Reference</th>
                                                    <th>Method</th>
                                                    <th>Amount</th>
                                                    <th>Type</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for payment in payments %}
                                                <tr>
                                                    <td>{{ payment.date|date:"M d, Y" }}</td>
                                                    <td><code>{{ payment.reference }}</code></td>
                                                    <td>
                                                        {% if payment.method == 'MPESA' %}
                                                            <i class="fas fa-mobile-alt text-success"></i> M-Pesa
                                                        {% elif payment.method == 'CASH' %}
                                                            <i class="fas fa-money-bill-wave text-primary"></i> Cash
                                                        {% elif payment.method == 'BANK_TRANSFER' %}
                                                            <i class="fas fa-university text-info"></i> Bank
                                                        {% elif payment.method == 'CHEQUE' %}
                                                            <i class="fas fa-money-check text-warning"></i> Cheque
                                                        {% else %}
                                                            {{ payment.method }}
                                                        {% endif %}
                                                    </td>
                                                    <td><strong class="text-success">KES {{ payment.amount|floatformat:2 }}</strong></td>
                                                    <td>
                                                        {% if payment.is_advance_payment %}
                                                            <span class="badge bg-info">
                                                                <i class="fas fa-calendar-plus"></i> Advance ({{ payment.months_covered }} month{{ payment.months_covered|pluralize }})
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">Regular</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if payment.status == 'COMPLETED' %}
                                                            <span class="badge bg-success">Completed</span>
                                                        {% elif payment.status == 'PENDING' %}
                                                            <span class="badge bg-warning">Pending</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">{{ payment.status }}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle"></i> No payments found for this lease.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'footer.html' %}
{% endblock %}

