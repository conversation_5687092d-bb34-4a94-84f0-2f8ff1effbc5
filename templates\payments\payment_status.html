{% extends 'base.html' %}
{% load org_urls %}

{% block title %}Payment Status{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header text-center">
                            <h4>Payment Status</h4>
                        </div>
                        <div class="card-body text-center">
                            <!-- Status Display -->
                            <div id="status-container">
                                {% if transaction.is_successful %}
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <h5>Payment Successful!</h5>
                                        <p>Your payment has been processed successfully.</p>
                                        {% if transaction.mpesa_receipt_number %}
                                            <p><strong>M-Pesa Receipt:</strong> {{ transaction.mpesa_receipt_number }}</p>
                                        {% endif %}
                                    </div>
                                {% elif transaction.is_failed %}
                                    <div class="alert alert-danger">
                                        <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                                        <h5>Payment Failed</h5>
                                        <p>{{ transaction.result_desc|default:"Payment could not be processed." }}</p>
                                    </div>
                                {% else %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                        <h5>Payment Pending</h5>
                                        <p>Please check your phone for the M-Pesa payment request and enter your PIN.</p>
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- Transaction Details -->
                            <div class="card mt-4">
                                <div class="card-body">
                                    <h6 class="card-title">Transaction Details</h6>
                                    <div class="row text-left">
                                        <div class="col-sm-6">
                                            <p><strong>Invoice:</strong> {{ transaction.invoice.number }}</p>
                                            <p><strong>Amount:</strong> KES {{ transaction.amount|floatformat:2 }}</p>
                                            <p><strong>Phone:</strong> +{{ transaction.phone_number }}</p>
                                        </div>
                                        <div class="col-sm-6">
                                            <p><strong>Status:</strong> 
                                                <span class="badge 
                                                    {% if transaction.is_successful %}badge-success
                                                    {% elif transaction.is_failed %}badge-danger
                                                    {% else %}badge-warning{% endif %}">
                                                    {{ transaction.get_status_display }}
                                                </span>
                                            </p>
                                            <p><strong>Date:</strong> {{ transaction.created_at|date:"M d, Y H:i" }}</p>
                                            {% if transaction.mpesa_receipt_number %}
                                                <p><strong>Receipt:</strong> {{ transaction.mpesa_receipt_number }}</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="mt-4">
                                {% if transaction.is_successful %}
                                    <a href="{% org_url 'invoice_list' %}" class="btn btn-primary">
                                        <i class="fas fa-list"></i> View Invoices
                                    </a>
                                {% elif transaction.is_failed %}
                                    <a href="{% org_url 'initiate_payment' invoice_id=transaction.invoice.id %}" class="btn btn-primary">
                                        <i class="fas fa-redo"></i> Try Again
                                    </a>
                                {% else %}
                                    <button id="refresh-status" class="btn btn-outline-primary">
                                        <i class="fas fa-sync"></i> Refresh Status
                                    </button>
                                {% endif %}
                                <a href="{% org_url 'payment_history' %}" class="btn btn-secondary">
                                    <i class="fas fa-history"></i> Payment History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const transactionId = {{ transaction.id }};
    const orgSlug = '{{ org.slug }}';
    let checkInterval;
    
    function checkPaymentStatus() {
        fetch(`/${orgSlug}/payments/transaction/${transactionId}/check/`)
            .then(response => response.json())
            .then(data => {
                if (data.is_successful) {
                    // Payment successful - reload page to show success state
                    location.reload();
                } else if (data.is_failed) {
                    // Payment failed - reload page to show failure state
                    location.reload();
                } else if (!data.is_pending) {
                    // Status changed but not to success/failure - reload
                    location.reload();
                }
                // If still pending, continue checking
            })
            .catch(error => {
                console.error('Error checking payment status:', error);
            });
    }
    
    // Auto-refresh status for pending payments
    {% if transaction.is_pending %}
        checkInterval = setInterval(checkPaymentStatus, 5000); // Check every 5 seconds
        
        // Stop checking after 5 minutes
        setTimeout(() => {
            if (checkInterval) {
                clearInterval(checkInterval);
            }
        }, 300000);
    {% endif %}
    
    // Manual refresh button
    document.getElementById('refresh-status')?.addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
        this.disabled = true;
        
        checkPaymentStatus();
        
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-sync"></i> Refresh Status';
            this.disabled = false;
        }, 2000);
    });
});
</script>

<style>
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.fa-3x {
    font-size: 3rem;
}

.badge {
    font-size: 0.9em;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
}
</style>
{% endblock %}
