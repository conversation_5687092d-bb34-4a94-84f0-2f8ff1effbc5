{% extends 'base.html' %}
{% load org_urls %}

{% block title %}Delete Unit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">Delete Unit</h1>
    <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Back to Units</a>
</div>

<div class="card">
    <div class="card-body">
        <div class="alert alert-warning">
            <h5>Are you sure you want to delete this unit?</h5>
            <p><strong>{{ object.property.name }} - {{ object.code }}</strong></p>
            <p>{{ object.bedrooms }} bed, {{ object.bathrooms }} bath</p>
            <p class="mb-0"><small class="text-muted">This action cannot be undone.</small></p>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-danger">Yes, Delete</button>
                <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
