{% load static %}
{% load org_urls %}
<nav class="navbar navbar-expand-lg main-navbar sticky">
        <div class="form-inline mr-auto">
          <ul class="navbar-nav mr-3">
            <li><a href="#" data-toggle="sidebar" class="nav-link nav-link-lg
									collapse-btn"> <i data-feather="align-justify"></i></a></li>
            <li><a href="#" class="nav-link nav-link-lg fullscreen-btn">
                <i data-feather="maximize"></i>
              </a></li>
            <li>
              <form class="form-inline mr-auto">
                <div class="search-element">
                  <input class="form-control" type="search" placeholder="Search properties, tenants..." aria-label="Search" data-width="200">
                  <button class="btn" type="submit">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </form>
            </li>
          </ul>
        </div>
        <ul class="navbar-nav navbar-right">
          <!-- Quick Actions -->
          {% if perms.can_manage_properties or perms.can_manage_tenants or perms.can_manage_finances %}
          <li class="dropdown">
            <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
              <i data-feather="plus-circle"></i> <span class="d-sm-none d-lg-inline-block">Quick Add</span>
            </a>
            <div class="dropdown-menu dropdown-menu-right pullDown">
              <div class="dropdown-title">Quick Actions</div>
              {% if perms.can_manage_properties %}
              <a href="{% org_url 'property_create' %}" class="dropdown-item has-icon">
                <i data-feather="home"></i> Add Property
              </a>
              <a href="{% org_url 'unit_create' %}" class="dropdown-item has-icon">
                <i data-feather="grid"></i> Add Unit
              </a>
              {% endif %}
              {% if perms.can_manage_tenants %}
              <a href="{% org_url 'tenant_create' %}" class="dropdown-item has-icon">
                <i data-feather="user-plus"></i> Add Tenant
              </a>
              <a href="{% org_url 'lease_create' %}" class="dropdown-item has-icon">
                <i data-feather="file-text"></i> Create Lease
              </a>
              {% endif %}
              {% if perms.can_manage_finances %}
              <a href="{% org_url 'invoice_create' %}" class="dropdown-item has-icon">
                <i data-feather="file-plus"></i> Create Invoice
              </a>
              {% endif %}
            </div>
          </li>
          {% endif %}
          <!-- User Menu -->
          <li class="dropdown">
            <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
              <img alt="image" src="{% static 'assets/img/users/user-1.png' %}" class="user-img-radious-style">
              <span class="d-sm-none d-lg-inline-block">{{ user.username }}</span>
            </a>
            <div class="dropdown-menu dropdown-menu-right pullDown">
              <div class="dropdown-title">{{ user.get_full_name|default:user.username }}</div>
              <a href="#" class="dropdown-item has-icon">
                <i class="far fa-user"></i> Profile
              </a>
              <a href="#" class="dropdown-item has-icon">
                <i class="fas fa-cog"></i> Settings
              </a>
              <div class="dropdown-divider"></div>
              <form method="post" action="{% url 'root_logout' %}">
                {% csrf_token %}
                <button type="submit" class="dropdown-item has-icon text-danger">
                  <i class="fas fa-sign-out-alt"></i> Logout
                </button>
              </form>
            </div>
          </li>
        </ul>
      </nav>