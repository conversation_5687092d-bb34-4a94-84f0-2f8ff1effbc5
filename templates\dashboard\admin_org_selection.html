{% extends 'base.html' %}
{% load static%}
{% block title %}Admin - Select Organization{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-12 col-md-10">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">
                    <i class="bi bi-shield-check"></i>
                    Superuser Access
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Admin Access:</strong> As a superuser, you can access any organization in the system.
                </div>
                
                <div class="row">
                    {% for org in organizations %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="bi bi-building display-4 text-warning"></i>
                                </div>
                                <h5 class="card-title">{{ org.name }}</h5>
                                <p class="card-text text-muted">
                                    <span class="badge bg-warning text-dark">Admin Access</span>
                                </p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        Created {{ org.created_at|date:"M d, Y" }}
                                    </small>
                                </p>
                                <a href="/{{ org.slug }}/" class="btn btn-warning text-dark">
                                    <i class="bi bi-arrow-right"></i>
                                    Access Organization
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-4">
                    <a href="/admin/" class="btn btn-outline-secondary">
                        <i class="bi bi-gear"></i>
                        Django Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
