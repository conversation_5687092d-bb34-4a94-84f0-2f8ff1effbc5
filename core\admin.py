from django.contrib import admin
from .models import Organization, Membership, RecurringInvoiceSchedule


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'owner', 'created_at')
    prepopulated_fields = {'slug': ('name',)}


@admin.register(Membership)
class MembershipAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'role')


@admin.register(RecurringInvoiceSchedule)
class RecurringInvoiceScheduleAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'frequency', 'is_active', 'day_of_month', 'last_run_date', 'last_run_count')
    list_filter = ('organization', 'frequency', 'is_active')
    search_fields = ('name', 'organization__name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('organization', 'name', 'frequency', 'is_active')
        }),
        ('Schedule Settings', {
            'fields': ('day_of_month', 'due_days', 'include_mandatory_amenities')
        }),
        ('Tracking', {
            'fields': ('last_run_date', 'last_run_count'),
            'classes': ('collapse',)
        }),
    )