{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="h5 mb-0">Invoice {{ invoice.number }}
                                        {% if invoice.is_paid %}<span class="badge bg-success">Paid</span>{% else %}<span class="badge bg-warning">Unpaid</span>{% endif %}
                                    </div>
                                    <div class="text-muted small">Lease: {{ invoice.lease.tenant }} – Unit {{ invoice.lease.unit.code }}</div>
                                </div>
                                <div>
                                    <a href="{% org_url 'invoice_pdf_download' pk=invoice.pk %}" class="btn btn-danger" target="_blank">
                                        <i class="fas fa-file-pdf"></i> Download PDF
                                    </a>
                                    {% if not invoice.is_paid %}
                                        {% if credit_balance.balance > 0 and credit_balance.balance >= invoice.balance %}
                                            <a href="{% org_url 'apply_credit_to_invoice' invoice_id=invoice.pk %}" class="btn btn-info btn-lg ml-2">
                                                <i class="fas fa-wallet"></i> Apply Credit (KES {{ credit_balance.balance|floatformat:2 }})
                                            </a>
                                        {% endif %}
                                        <a href="{% org_url 'quick_pay_invoice' invoice_id=invoice.pk %}" class="btn btn-success btn-lg ml-2">
                                            <i class="fas fa-bolt"></i> Quick Pay
                                        </a>
                                        <a href="{% org_url 'invoice_edit' pk=invoice.pk %}" class="btn btn-outline-primary ml-2">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                    {% else %}
                                        <span class="badge badge-success badge-lg">
                                            <i class="fas fa-check-circle"></i> Invoice Fully Paid
                                        </span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Credit Balance Alert -->
                            {% if credit_balance.balance > 0 and not invoice.is_paid %}
                            <div class="alert alert-info mx-3 mb-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-piggy-bank"></i>
                                        <strong>Tenant has a credit balance of KES {{ credit_balance.balance|floatformat:2 }}</strong>
                                        {% if credit_balance.balance >= invoice.balance %}
                                            - Sufficient to cover this invoice!
                                        {% else %}
                                            - Can partially cover this invoice (KES {{ invoice.balance|floatformat:2 }} due)
                                        {% endif %}
                                    </div>
                                    <div>
                                        <a href="{% org_url 'apply_credit_to_invoice' invoice_id=invoice.pk %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> Apply Credit Now
                                        </a>
                                        <a href="{% org_url 'tenant_credit_balance' lease_id=invoice.lease.pk %}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex justify-content-between"><span>Issue Date</span><strong>{{ invoice.issue_date }}</strong></li>
                                            <li class="list-group-item d-flex justify-content-between"><span>Due Date</span><strong>{{ invoice.due_date }}</strong></li>
                                            <li class="list-group-item d-flex justify-content-between"><span>Amount Due</span><strong>{{ invoice.amount_due }}</strong></li>
                                            <li class="list-group-item d-flex justify-content-between"><span>Amount Paid</span><strong>{{ invoice.amount_paid }}</strong></li>
                                            <li class="list-group-item d-flex justify-content-between"><span>Balance</span><strong>{{ invoice.balance }}</strong></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6>Payment History</h6>
                                            {% if not invoice.is_paid %}
                                                <button class="btn btn-sm btn-success quick-pay-btn"
                                                        data-invoice-id="{{ invoice.pk }}"
                                                        data-invoice-number="{{ invoice.number }}"
                                                        data-amount="{{ invoice.balance }}">
                                                    <i class="fas fa-mobile-alt"></i> Quick Pay
                                                </button>
                                            {% endif %}
                                        </div>
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Date</th>
                                                    <th>Method</th>
                                                    <th>Reference</th>
                                                    <th>Amount</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for p in invoice.payments.all %}
                                                <tr>
                                                    <td>{{ forloop.counter }}</td>
                                                    <td>{{ p.date|date:"M d, Y" }}</td>
                                                    <td>
                                                        {% if p.method == 'MPESA' %}
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-mobile-alt"></i> M-Pesa
                                                            </span>
                                                        {% else %}
                                                            <span class="badge badge-secondary">{{ p.method }}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        {% if p.mpesa_receipt_number %}
                                                            <code>{{ p.mpesa_receipt_number }}</code>
                                                        {% else %}
                                                            {{ p.reference|default:'—' }}
                                                        {% endif %}
                                                    </td>
                                                    <td>KES {{ p.amount|floatformat:2 }}</td>
                                                    <td>
                                                        {% if p.status == 'COMPLETED' %}
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-check"></i> Completed
                                                            </span>
                                                        {% elif p.status == 'PENDING' %}
                                                            <span class="badge badge-warning">
                                                                <i class="fas fa-clock"></i> Pending
                                                            </span>
                                                        {% elif p.status == 'FAILED' %}
                                                            <span class="badge badge-danger">
                                                                <i class="fas fa-times"></i> Failed
                                                            </span>
                                                        {% else %}
                                                            <span class="badge badge-info">{{ p.get_status_display|default:'Completed' }}</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted">
                                                        <i class="fas fa-receipt"></i> No payments yet.
                                                        {% if not invoice.is_paid %}
                                                            <br><small>Click "Quick Pay" to make a payment.</small>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<!-- Quick Pay Modal -->
<div class="modal fade" id="quickPayModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-mobile-alt text-success"></i> Quick M-Pesa Payment
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="quickPayForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Invoice:</strong> <span id="modal-invoice-number"></span><br>
                        <strong>Amount:</strong> KES <span id="modal-amount"></span>
                    </div>

                    <div class="form-group">
                        <label for="modal-phone">M-Pesa Phone Number</label>
                        <input type="tel"
                               class="form-control"
                               id="modal-phone"
                               name="phone_number"
                               placeholder="0712345678"
                               pattern="^(\+?254|0)[17]\d{8}$"
                               required>
                        <small class="form-text text-muted">Enter your M-Pesa registered phone number</small>
                    </div>

                    <input type="hidden" id="modal-invoice-id" name="invoice_id">
                    <input type="hidden" id="modal-amount-value" name="amount">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-credit-card"></i> Pay Now
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quickPayButtons = document.querySelectorAll('.quick-pay-btn');
    const orgSlug = '{{ org.slug }}';

    quickPayButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const invoiceId = this.dataset.invoiceId;
            const invoiceNumber = this.dataset.invoiceNumber;
            const amount = this.dataset.amount;

            document.getElementById('modal-invoice-id').value = invoiceId;
            document.getElementById('modal-invoice-number').textContent = invoiceNumber;
            document.getElementById('modal-amount').textContent = parseFloat(amount).toFixed(2);
            document.getElementById('modal-amount-value').value = amount;

            // Set form action
            const form = document.getElementById('quickPayForm');
            form.action = `/${orgSlug}/payments/invoice/${invoiceId}/pay/`;

            $('#quickPayModal').modal('show');
        });
    });

    // Auto-format phone number input
    document.getElementById('modal-phone')?.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');

        if (value.startsWith('254')) {
            e.target.value = value;
        } else if (value.startsWith('0')) {
            e.target.value = '0' + value.slice(1);
        } else if (value.length > 0) {
            e.target.value = '0' + value;
        }
    });
});
</script>

<style>
.dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

.badge {
    font-size: 0.8rem;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.85rem;
}

.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
}
</style>

{% include 'footer.html' %}
{% endblock %}