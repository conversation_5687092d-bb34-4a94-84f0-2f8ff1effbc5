{% extends 'base.html' %}
{% load static %}
{% block title %}Select Organization{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-12 col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="bi bi-buildings"></i>
                    Select Organization
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">You have access to multiple organizations. Choose one to continue:</p>
                
                <div class="row">
                    {% for membership in memberships %}
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="bi bi-building display-4 text-primary"></i>
                                </div>
                                <h5 class="card-title">{{ membership.organization.name }}</h5>
                                <p class="card-text text-muted">
                                    <span class="badge bg-secondary">{{ membership.get_role_display }}</span>
                                </p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        Member since {{ membership.created_at|date:"M d, Y" }}
                                    </small>
                                </p>
                                <a href="/{{ membership.organization.slug }}/" class="btn btn-primary">
                                    <i class="bi bi-arrow-right"></i>
                                    Access Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
