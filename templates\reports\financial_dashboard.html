{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Financial Dashboard{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-chart-line text-primary"></i> Financial Dashboard
                        </h1>
                        <div class="btn-group">
                            <a href="{% org_url 'income_statement' %}" class="btn btn-outline-primary">
                                <i class="fas fa-file-invoice-dollar"></i> Income Statement
                            </a>
                            <a href="{% org_url 'cash_flow_report' %}" class="btn btn-outline-info">
                                <i class="fas fa-chart-area"></i> Cash Flow
                            </a>
                            <a href="{% org_url 'occupancy_report' %}" class="btn btn-outline-success">
                                <i class="fas fa-building"></i> Occupancy Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-primary">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Properties</h4>
                            </div>
                            <div class="card-body">
                                {{ total_properties }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-success">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Units ({{ occupancy_rate }}% Occupied)</h4>
                            </div>
                            <div class="card-body">
                                {{ occupied_units }}/{{ total_units }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-warning">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>{{ current_month }} Revenue</h4>
                            </div>
                            <div class="card-body">
                                KES {{ current_month_revenue|floatformat:2 }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Outstanding</h4>
                            </div>
                            <div class="card-body">
                                KES {{ outstanding_amount|floatformat:2 }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Comparison and Outstanding Details -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-chart-bar"></i> Revenue Comparison</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <h5 class="text-muted">Current Month</h5>
                                        <h3 class="text-success">KES {{ current_month_revenue|floatformat:2 }}</h3>
                                        <small class="text-muted">{{ current_month }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <h5 class="text-muted">Last Month</h5>
                                        <h3 class="text-info">KES {{ last_month_revenue|floatformat:2 }}</h3>
                                        {% if current_month_revenue > last_month_revenue %}
                                            <span class="badge badge-success">
                                                <i class="fas fa-arrow-up"></i> 
                                                {% widthratio current_month_revenue last_month_revenue 100 %}% increase
                                            </span>
                                        {% elif current_month_revenue < last_month_revenue %}
                                            <span class="badge badge-warning">
                                                <i class="fas fa-arrow-down"></i> 
                                                {% widthratio last_month_revenue current_month_revenue 100 %}% decrease
                                            </span>
                                        {% else %}
                                            <span class="badge badge-secondary">No change</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-clock"></i> Outstanding Invoices</h4>
                        </div>
                        <div class="card-body text-center">
                            <h2 class="text-danger">{{ outstanding_count }}</h2>
                            <p class="text-muted">Unpaid Invoices</p>
                            <h4 class="text-warning">KES {{ outstanding_amount|floatformat:2 }}</h4>
                            <p class="text-muted">Total Outstanding</p>
                            <a href="{% org_url 'invoice_list' %}?status=unpaid" class="btn btn-primary btn-sm">
                                <i class="fas fa-list"></i> View Unpaid Invoices
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Government Taxes -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-university"></i> Government Taxes Due</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h5 class="text-danger">Total Taxes Due</h5>
                                        <h3>KES {{ taxes_due.total|floatformat:2 }}</h3>
                                        <small class="text-muted">For {{ current_month }}</small>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <h6>Tax Breakdown:</h6>
                                    <div class="row">
                                        {% for tax_name, amount in taxes_due.breakdown.items %}
                                        <div class="col-md-6 mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ tax_name }}:</span>
                                                <strong>KES {{ amount|floatformat:2 }}</strong>
                                            </div>
                                        </div>
                                        {% empty %}
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle"></i>
                                                No government taxes configured.
                                                <a href="/admin/rentals/governmenttax/add/" target="_blank">Add tax configurations</a>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-bolt"></i> Quick Actions</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="{% org_url 'invoice_create' %}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> Create Invoice
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% org_url 'payment_create' %}" class="btn btn-success btn-block">
                                        <i class="fas fa-money-bill"></i> Record Payment
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% org_url 'tenant_list' %}" class="btn btn-info btn-block">
                                        <i class="fas fa-users"></i> Manage Tenants
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{% org_url 'property_list' %}" class="btn btn-warning btn-block">
                                        <i class="fas fa-building"></i> Manage Properties
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-statistic-1 {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

.card-statistic-1 .card-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 30px;
    color: white;
    position: absolute;
    right: 20px;
    top: 20px;
}

.card-statistic-1 .card-wrap {
    padding: 20px;
}

.card-statistic-1 .card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 10px;
}

.card-statistic-1 .card-body {
    font-size: 32px;
    font-weight: 700;
    color: #495057;
}
</style>

{% include 'footer.html' %}
{% endblock %}
