from django.urls import path
from . import views, reports


urlpatterns = [
    path('properties/', views.property_list, name='property_list'),
    path('units/', views.unit_list, name='unit_list'),
    path('tenants/', views.tenant_list, name='tenant_list'),
    path('leases/', views.lease_list, name='lease_list'),
    path('leases/<int:pk>/', views.lease_detail, name='lease_detail'),
    path('billing/invoices/', views.invoice_list, name='invoice_list'),
    path('billing/invoices/<int:pk>/', views.invoice_detail, name='invoice_detail'),
    path('billing/payments/', views.payment_list, name='payment_list'),

    # Reports
    path('reports/', reports.financial_dashboard, name='financial_dashboard'),
    path('reports/income-statement/', reports.income_statement, name='income_statement'),
    path('reports/occupancy/', reports.occupancy_report, name='occupancy_report'),
    path('reports/cash-flow/', reports.cash_flow_report, name='cash_flow_report'),
    path('reports/property-performance/', reports.property_performance_report, name='property_performance_report'),
]