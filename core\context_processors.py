from .models import Organization, Membership
from .decorators import (
    user_has_role, get_user_role, can_manage_properties,
    can_manage_tenants, can_manage_finances, can_view_reports
)


def global_org(request):
    """Make organization available in all templates"""
    return {'org': getattr(request, 'org', None)}


def user_permissions(request):
    """Make user permissions available in all templates"""
    if not request.user.is_authenticated:
        return {}

    org = getattr(request, 'org', None)
    if not org:
        return {}

    # Get user's role and permissions
    user_role = get_user_role(request.user, org)

    permissions = {
        'user_role': user_role,
        'is_admin': user_has_role(request.user, org, ['ADMIN']),
        'is_manager': user_has_role(request.user, org, ['MANAGER']),
        'is_agent': user_has_role(request.user, org, ['AGENT']),
        'is_tenant': user_has_role(request.user, org, ['TENANT']),
        'can_manage_properties': can_manage_properties(request.user, org),
        'can_manage_tenants': can_manage_tenants(request.user, org),
        'can_manage_finances': can_manage_finances(request.user, org),
        'can_view_reports': can_view_reports(request.user, org),
    }

    return {'perms': permissions}