{% extends 'base.html' %}
{% load org_urls %}
{% block title %}Occupancy Report{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-building text-success"></i> Occupancy Report
                        </h1>
                        <div class="btn-group">
                            <a href="{% org_url 'financial_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Overall Summary -->
            <div class="row mb-4">
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-primary">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Total Units</h4>
                            </div>
                            <div class="card-body">
                                {{ total_units }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Occupied Units</h4>
                            </div>
                            <div class="card-body">
                                {{ total_occupied }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-warning">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Vacant Units</h4>
                            </div>
                            <div class="card-body">
                                {{ total_vacant }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Occupancy Rate</h4>
                            </div>
                            <div class="card-body">
                                {{ overall_occupancy }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tenant Analytics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Total Tenants</h5>
                            <h2 class="text-primary">{{ total_tenants }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Active Leases</h5>
                            <h2 class="text-success">{{ active_leases }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Expiring Soon</h5>
                            <h2 class="text-warning">{{ expiring_soon }}</h2>
                            <small class="text-muted">Next 90 days</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Monthly Revenue</h5>
                            <h2 class="text-info">KES {{ total_revenue|floatformat:2 }}</h2>
                            <small class="text-muted">{{ current_month }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property-wise Occupancy -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Property-wise Occupancy Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Property</th>
                                            <th>Total Units</th>
                                            <th>Occupied</th>
                                            <th>Vacant</th>
                                            <th>Occupancy Rate</th>
                                            <th>Avg. Rent</th>
                                            <th>Monthly Revenue</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for data in property_data %}
                                        <tr>
                                            <td>
                                                <strong>{{ data.property.name }}</strong><br>
                                                <small class="text-muted">{{ data.property.address }}</small>
                                            </td>
                                            <td>{{ data.total_units }}</td>
                                            <td>
                                                <span class="badge badge-success">{{ data.occupied_units }}</span>
                                            </td>
                                            <td>
                                                {% if data.vacant_units > 0 %}
                                                    <span class="badge badge-warning">{{ data.vacant_units }}</span>
                                                {% else %}
                                                    <span class="badge badge-secondary">0</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar 
                                                        {% if data.occupancy_rate >= 90 %}bg-success
                                                        {% elif data.occupancy_rate >= 70 %}bg-info
                                                        {% elif data.occupancy_rate >= 50 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                        role="progressbar" 
                                                        style="width: {{ data.occupancy_rate }}%">
                                                        {{ data.occupancy_rate }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>KES {{ data.avg_rent|floatformat:2 }}</td>
                                            <td>KES {{ data.revenue|floatformat:2 }}</td>
                                            <td>
                                                {% if data.occupancy_rate >= 90 %}
                                                    <span class="badge badge-success">Excellent</span>
                                                {% elif data.occupancy_rate >= 70 %}
                                                    <span class="badge badge-info">Good</span>
                                                {% elif data.occupancy_rate >= 50 %}
                                                    <span class="badge badge-warning">Fair</span>
                                                {% else %}
                                                    <span class="badge badge-danger">Poor</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">
                                                <i class="fas fa-building"></i> No properties found.
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Occupancy Chart -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>Overall Occupancy</h4>
                        </div>
                        <div class="card-body">
                            <canvas id="occupancyChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4>Property Performance</h4>
                        </div>
                        <div class="card-body">
                            <canvas id="propertyChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Items -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-tasks"></i> Action Items</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if total_vacant > 0 %}
                                <div class="col-md-4">
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle"></i> Vacant Units</h6>
                                        <p>{{ total_vacant }} units are currently vacant. Consider:</p>
                                        <ul class="mb-0">
                                            <li>Marketing campaigns</li>
                                            <li>Rent adjustments</li>
                                            <li>Property improvements</li>
                                        </ul>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if expiring_soon > 0 %}
                                <div class="col-md-4">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-calendar-alt"></i> Lease Renewals</h6>
                                        <p>{{ expiring_soon }} leases expire in 90 days:</p>
                                        <ul class="mb-0">
                                            <li>Contact tenants early</li>
                                            <li>Prepare renewal terms</li>
                                            <li>Schedule inspections</li>
                                        </ul>
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if overall_occupancy < 80 %}
                                <div class="col-md-4">
                                    <div class="alert alert-danger">
                                        <h6><i class="fas fa-chart-line"></i> Low Occupancy</h6>
                                        <p>Overall occupancy is {{ overall_occupancy }}%:</p>
                                        <ul class="mb-0">
                                            <li>Review pricing strategy</li>
                                            <li>Improve property conditions</li>
                                            <li>Enhance marketing efforts</li>
                                        </ul>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Overall Occupancy Chart
const occupancyCtx = document.getElementById('occupancyChart').getContext('2d');
new Chart(occupancyCtx, {
    type: 'doughnut',
    data: {
        labels: ['Occupied', 'Vacant'],
        datasets: [{
            data: [{{ total_occupied }}, {{ total_vacant }}],
            backgroundColor: ['#28a745', '#ffc107'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Property Performance Chart
const propertyCtx = document.getElementById('propertyChart').getContext('2d');
new Chart(propertyCtx, {
    type: 'bar',
    data: {
        labels: [{% for data in property_data %}'{{ data.property.name }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Occupancy Rate (%)',
            data: [{% for data in property_data %}{{ data.occupancy_rate }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: [{% for data in property_data %}
                {% if data.occupancy_rate >= 90 %}'#28a745'
                {% elif data.occupancy_rate >= 70 %}'#17a2b8'
                {% elif data.occupancy_rate >= 50 %}'#ffc107'
                {% else %}'#dc3545'{% endif %}{% if not forloop.last %},{% endif %}
            {% endfor %}],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

<style>
.card-statistic-1 {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

.card-statistic-1 .card-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 30px;
    color: white;
    position: absolute;
    right: 20px;
    top: 20px;
}

.card-statistic-1 .card-wrap {
    padding: 20px;
}

.card-statistic-1 .card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 10px;
}

.card-statistic-1 .card-body {
    font-size: 32px;
    font-weight: 700;
    color: #495057;
}

@media print {
    .btn, .card-header-action, .main-sidebar, .navbar {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}
</style>

{% include 'footer.html' %}
{% endblock %}
