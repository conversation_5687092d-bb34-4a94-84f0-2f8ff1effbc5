from functools import wraps
from django.http import HttpResponseForbidden
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from .models import Membership


def require_membership_role(allowed_roles):
    """
    Decorator to check if user has required role in the organization.
    Usage: @require_membership_role(['ADMIN', 'MANAGER'])
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, org_slug, *args, **kwargs):
            # Get user's membership for this organization
            try:
                membership = Membership.objects.get(
                    user=request.user,
                    organization=request.org
                )
                
                # Check if user's role is in allowed roles
                if membership.role in allowed_roles:
                    return view_func(request, org_slug, *args, **kwargs)
                else:
                    return render(request, 'errors/403.html', {
                        'message': f'Access denied. Required role: {", ".join(allowed_roles)}. Your role: {membership.role}'
                    }, status=403)
                    
            except Membership.DoesNotExist:
                return render(request, 'errors/403.html', {
                    'message': 'Access denied. You are not a member of this organization.'
                }, status=403)
                
        return _wrapped_view
    return decorator


def require_admin_or_manager(view_func):
    """Shortcut decorator for admin or manager access"""
    return require_membership_role(['ADMIN', 'MANAGER'])(view_func)


def require_admin_only(view_func):
    """Shortcut decorator for admin-only access"""
    return require_membership_role(['ADMIN'])(view_func)


def require_any_member(view_func):
    """Decorator to check if user is any member of the organization"""
    return require_membership_role(['ADMIN', 'MANAGER', 'AGENT', 'TENANT'])(view_func)


# Permission checking utility functions
def user_has_role(user, organization, required_roles):
    """Check if user has any of the required roles in the organization"""
    try:
        membership = Membership.objects.get(user=user, organization=organization)
        return membership.role in required_roles
    except Membership.DoesNotExist:
        return False


def get_user_role(user, organization):
    """Get user's role in the organization"""
    try:
        membership = Membership.objects.get(user=user, organization=organization)
        return membership.role
    except Membership.DoesNotExist:
        return None


def can_manage_properties(user, organization):
    """Check if user can manage properties"""
    return user_has_role(user, organization, ['ADMIN', 'MANAGER'])


def can_manage_tenants(user, organization):
    """Check if user can manage tenants"""
    return user_has_role(user, organization, ['ADMIN', 'MANAGER'])


def can_manage_finances(user, organization):
    """Check if user can manage invoices and payments"""
    return user_has_role(user, organization, ['ADMIN', 'MANAGER'])


def can_view_reports(user, organization):
    """Check if user can view reports"""
    return user_has_role(user, organization, ['ADMIN', 'MANAGER', 'AGENT'])
