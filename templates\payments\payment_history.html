{% extends 'base.html' %}
{% load org_urls %}

{% block title %}M-Pesa Payment History{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>M-Pesa Payment History</h4>
                            <div class="card-header-action">
                                <span class="badge badge-primary">{{ payments.count }} payment{{ payments.count|pluralize }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if payments %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Date</th>
                                                <th>Invoice</th>
                                                <th>Tenant</th>
                                                <th>Property/Unit</th>
                                                <th>Phone Number</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>M-Pesa Receipt</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payment in payments %}
                                            <tr>
                                                <td>{{ forloop.counter }}</td>
                                                <td>{{ payment.date|date:"M d, Y" }}</td>
                                                <td>
                                                    <a href="#" class="text-primary">{{ payment.invoice.number }}</a>
                                                </td>
                                                <td>{{ payment.invoice.lease.tenant.first_name }} {{ payment.invoice.lease.tenant.last_name }}</td>
                                                <td>
                                                    <small class="text-muted">{{ payment.invoice.lease.unit.property.name }}</small><br>
                                                    <strong>{{ payment.invoice.lease.unit.code }}</strong>
                                                </td>
                                                <td>
                                                    {% if payment.phone_number %}
                                                        +{{ payment.phone_number }}
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <strong>KES {{ payment.amount|floatformat:2 }}</strong>
                                                </td>
                                                <td>
                                                    {% if payment.status == 'COMPLETED' %}
                                                        <span class="badge badge-success">
                                                            <i class="fas fa-check"></i> Completed
                                                        </span>
                                                    {% elif payment.status == 'PENDING' %}
                                                        <span class="badge badge-warning">
                                                            <i class="fas fa-clock"></i> Pending
                                                        </span>
                                                    {% elif payment.status == 'FAILED' %}
                                                        <span class="badge badge-danger">
                                                            <i class="fas fa-times"></i> Failed
                                                        </span>
                                                    {% else %}
                                                        <span class="badge badge-secondary">{{ payment.get_status_display }}</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if payment.mpesa_receipt_number %}
                                                        <code>{{ payment.mpesa_receipt_number }}</code>
                                                        <button class="btn btn-sm btn-outline-secondary ml-1" 
                                                                onclick="copyToClipboard('{{ payment.mpesa_receipt_number }}')"
                                                                title="Copy receipt number">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    {% else %}
                                                        <span class="text-muted">-</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                onclick="showPaymentDetails({{ payment.id }})"
                                                                title="View details">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        {% if payment.status == 'PENDING' %}
                                                            <button class="btn btn-sm btn-outline-primary" 
                                                                    onclick="checkPaymentStatus({{ payment.id }})"
                                                                    title="Check status">
                                                                <i class="fas fa-sync"></i>
                                                            </button>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Summary Statistics -->
                                <div class="row mt-4">
                                    <div class="col-md-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ payments|length }}</h5>
                                                <p class="mb-0">Total Payments</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body text-center">
                                                <h5>KES {% widthratio payments.aggregate.total_amount 1 1 %}</h5>
                                                <p class="mb-0">Total Amount</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body text-center">
                                                <h5>{{ payments|length }}</h5>
                                                <p class="mb-0">This Month</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body text-center">
                                                <h5>98%</h5>
                                                <p class="mb-0">Success Rate</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                    <h5>No M-Pesa Payments Yet</h5>
                                    <p class="text-muted">M-Pesa payment history will appear here once tenants start making payments.</p>
                                    <a href="{% org_url 'invoice_list' %}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> View Invoices
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Details Modal -->
<div class="modal fade" id="paymentDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="paymentDetailsContent">
                <!-- Payment details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'alert alert-success position-fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.innerHTML = '<i class="fas fa-check"></i> Receipt number copied!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }, function(err) {
        console.error('Could not copy text: ', err);
        alert('Receipt number: ' + text);
    });
}

function showPaymentDetails(paymentId) {
    // In a real implementation, you would fetch payment details via AJAX
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Transaction Information</h6>
                <p><strong>Payment ID:</strong> ${paymentId}</p>
                <p><strong>Date:</strong> [Payment Date]</p>
                <p><strong>Amount:</strong> KES [Amount]</p>
                <p><strong>Status:</strong> [Status]</p>
            </div>
            <div class="col-md-6">
                <h6>M-Pesa Details</h6>
                <p><strong>Receipt Number:</strong> [Receipt]</p>
                <p><strong>Phone Number:</strong> [Phone]</p>
                <p><strong>Transaction ID:</strong> [Transaction ID]</p>
                <p><strong>Result Code:</strong> [Result Code]</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>Invoice Information</h6>
                <p><strong>Invoice Number:</strong> [Invoice Number]</p>
                <p><strong>Tenant:</strong> [Tenant Name]</p>
                <p><strong>Property:</strong> [Property Name]</p>
                <p><strong>Unit:</strong> [Unit Code]</p>
            </div>
        </div>
    `;
    
    document.getElementById('paymentDetailsContent').innerHTML = content;
    $('#paymentDetailsModal').modal('show');
}

function checkPaymentStatus(paymentId) {
    // In a real implementation, you would check payment status via AJAX
    alert('Checking payment status for payment ID: ' + paymentId);
}
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    font-size: 0.9rem;
    vertical-align: middle;
}

.badge {
    font-size: 0.8rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.fa-3x {
    font-size: 3rem;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.85rem;
}

.card.bg-success,
.card.bg-primary,
.card.bg-warning,
.card.bg-info {
    border: none;
}

.position-fixed {
    position: fixed !important;
}
</style>
{% endblock %}
