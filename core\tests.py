from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from core.models import Organization, Membership
from rentals.models import Property, Unit, Tenant, Lease, Invoice, Payment
from decimal import Decimal
from datetime import date, timedelta


class RentalXMVPTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )

        # Create test organization
        self.org = Organization.objects.create(
            name='Test Property Management',
            slug='test-org',
            owner=self.user
        )

        # Create membership
        self.membership = Membership.objects.create(
            user=self.user,
            organization=self.org,
            role='ADMIN'
        )

        # Create test property
        self.property = Property.objects.create(
            organization=self.org,
            name='Test Apartments',
            address='123 Test St, Test City, TC 12345'
        )

        # Create test unit
        self.unit = Unit.objects.create(
            organization=self.org,
            property=self.property,
            code='A101',
            bedrooms=2,
            bathrooms=1,
            is_active=True
        )

        # Create test tenant
        self.tenant = Tenant.objects.create(
            organization=self.org,
            first_name='<PERSON>',
            last_name='Doe',
            email='<EMAIL>',
            phone='555-0123'
        )

        # Create test lease
        self.lease = Lease.objects.create(
            organization=self.org,
            unit=self.unit,
            tenant=self.tenant,
            start_date=date.today() - timedelta(days=30),
            rent_amount=Decimal('1500.00'),
            deposit_amount=Decimal('1500.00'),
            billing_day=1,
            status='ACTIVE'
        )

        self.client = Client()

    def test_models_creation(self):
        """Test that all models can be created successfully"""
        self.assertEqual(self.org.name, 'Test Property Management')
        self.assertEqual(self.property.name, 'Test Apartments')
        self.assertEqual(self.unit.code, 'A101')
        self.assertEqual(self.tenant.first_name, 'John')
        self.assertEqual(self.lease.rent_amount, Decimal('1500.00'))

    def test_organization_slug_generation(self):
        """Test that organization slug is generated correctly"""
        org = Organization.objects.create(name='New Test Org')
        self.assertEqual(org.slug, 'new-test-org')

    def test_invoice_creation(self):
        """Test invoice creation and balance calculation"""
        invoice = Invoice.objects.create(
            organization=self.org,
            lease=self.lease,
            number='INV-202509-0001',
            issue_date=date.today(),
            due_date=date.today() + timedelta(days=30),
            amount_due=Decimal('1500.00')
        )

        self.assertEqual(invoice.balance, Decimal('1500.00'))
        self.assertFalse(invoice.is_paid)

    def test_payment_creation_and_invoice_update(self):
        """Test payment creation and invoice balance update"""
        # Create invoice
        invoice = Invoice.objects.create(
            organization=self.org,
            lease=self.lease,
            number='INV-202509-0002',
            issue_date=date.today(),
            due_date=date.today() + timedelta(days=30),
            amount_due=Decimal('1500.00')
        )

        # Create payment
        payment = Payment.objects.create(
            organization=self.org,
            invoice=invoice,
            date=date.today(),
            reference='PAY-001',
            method='Bank Transfer',
            amount=Decimal('1500.00')
        )

        # Manually update invoice (as would be done in the view)
        invoice.amount_paid = Decimal('1500.00')
        invoice.is_paid = True
        invoice.save()

        self.assertEqual(payment.amount, Decimal('1500.00'))
        self.assertEqual(invoice.balance, Decimal('0.00'))
        self.assertTrue(invoice.is_paid)

    def test_login_required_views(self):
        """Test that views require authentication"""
        # Test dashboard redirect
        response = self.client.get(f'/{self.org.slug}/')
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Test property list redirect
        response = self.client.get(f'/{self.org.slug}/rentals/properties/')
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_authenticated_dashboard_access(self):
        """Test dashboard access when authenticated"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dashboard')

    def test_property_list_view(self):
        """Test property list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/rentals/properties/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Apartments')

    def test_unit_list_view(self):
        """Test unit list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/rentals/units/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'A101')

    def test_tenant_list_view(self):
        """Test tenant list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/rentals/tenants/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe')

    def test_lease_list_view(self):
        """Test lease list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/rentals/leases/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe')

    def test_invoice_list_view(self):
        """Test invoice list view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(f'/{self.org.slug}/rentals/billing/invoices/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invoices')
