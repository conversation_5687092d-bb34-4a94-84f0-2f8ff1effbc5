{% extends 'base.html' %}
{% block title %}Admin - Select Organization{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-12 col-md-8">
        <div class="card">
            <div class="card-body">
                <h1 class="h3 mb-4 text-center">Admin Access</h1>
                <p class="text-center text-muted mb-4">As a superuser, you can access any organization:</p>
                
                <div class="list-group">
                    {% for org in organizations %}
                    <a href="/{{ org.slug }}/" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ org.name }}</h5>
                            <small>{{ org.created_at|date:"M d, Y" }}</small>
                        </div>
                        <p class="mb-1">Organization Slug: {{ org.slug }}</p>
                        <small class="text-muted">Admin Access</small>
                    </a>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-4">
                    <a href="/admin/" class="btn btn-outline-secondary">
                        Django Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
