from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db import models, transaction
from django.http import JsonResponse, HttpResponse
from .forms import UnitForm, TenantForm, LeaseForm, InvoiceForm, PaymentForm
from rentals.models import (
    Unit, Tenant, Lease, Invoice, Payment,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem
)
from decimal import Decimal
from datetime import date, timedelta
from .pdf_utils import generate_invoice_pdf


# Unit CRUD Views
@login_required
def unit_create(request, org_slug):
    if request.method == 'POST':
        form = UnitForm(request.POST, organization=request.org)
        if form.is_valid():
            unit = form.save(commit=False)
            unit.organization = request.org
            unit.save()
            messages.success(request, 'Unit created successfully!')
            return redirect('unit_list', org_slug=org_slug)
    else:
        form = UnitForm(organization=request.org)
    return render(request, 'rentals/unit_form.html', {'form': form, 'title': 'Add Unit'})


@login_required
def unit_edit(request, org_slug, pk):
    unit = get_object_or_404(Unit, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = UnitForm(request.POST, instance=unit, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Unit updated successfully!')
            return redirect('unit_list', org_slug=org_slug)
    else:
        form = UnitForm(instance=unit, organization=request.org)
    return render(request, 'rentals/unit_form.html', {'form': form, 'title': 'Edit Unit'})


@login_required
def unit_delete(request, org_slug, pk):
    unit = get_object_or_404(Unit, pk=pk, organization=request.org)
    if request.method == 'POST':
        unit.delete()
        messages.success(request, 'Unit deleted successfully!')
        return redirect('unit_list', org_slug=org_slug)
    return render(request, 'rentals/unit_confirm_delete.html', {'object': unit})


# Tenant CRUD Views
@login_required
def tenant_create(request, org_slug):
    if request.method == 'POST':
        form = TenantForm(request.POST)
        if form.is_valid():
            tenant = form.save(commit=False)
            tenant.organization = request.org
            tenant.save()
            messages.success(request, 'Tenant created successfully!')
            return redirect('tenant_list', org_slug=org_slug)
    else:
        form = TenantForm()
    return render(request, 'rentals/tenant_form.html', {'form': form, 'title': 'Add Tenant'})


@login_required
def tenant_edit(request, org_slug, pk):
    tenant = get_object_or_404(Tenant, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = TenantForm(request.POST, instance=tenant)
        if form.is_valid():
            form.save()
            messages.success(request, 'Tenant updated successfully!')
            return redirect('tenant_list', org_slug=org_slug)
    else:
        form = TenantForm(instance=tenant)
    return render(request, 'rentals/tenant_form.html', {'form': form, 'title': 'Edit Tenant'})


@login_required
def tenant_delete(request, org_slug, pk):
    tenant = get_object_or_404(Tenant, pk=pk, organization=request.org)
    if request.method == 'POST':
        tenant.delete()
        messages.success(request, 'Tenant deleted successfully!')
        return redirect('tenant_list', org_slug=org_slug)
    return render(request, 'rentals/tenant_confirm_delete.html', {'object': tenant})


# Lease CRUD Views
@login_required
def lease_create(request, org_slug):
    if request.method == 'POST':
        form = LeaseForm(request.POST, organization=request.org)
        if form.is_valid():
            lease = form.save(commit=False)
            lease.organization = request.org
            lease.save()
            messages.success(request, 'Lease created successfully!')
            return redirect('lease_list', org_slug=org_slug)
    else:
        form = LeaseForm(organization=request.org)
    return render(request, 'rentals/lease_form.html', {'form': form, 'title': 'Add Lease'})


@login_required
def lease_edit(request, org_slug, pk):
    lease = get_object_or_404(Lease, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = LeaseForm(request.POST, instance=lease, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Lease updated successfully!')
            return redirect('lease_list', org_slug=org_slug)
    else:
        form = LeaseForm(instance=lease, organization=request.org)
    return render(request, 'rentals/lease_form.html', {'form': form, 'title': 'Edit Lease'})


@login_required
def lease_delete(request, org_slug, pk):
    lease = get_object_or_404(Lease, pk=pk, organization=request.org)
    if request.method == 'POST':
        lease.delete()
        messages.success(request, 'Lease deleted successfully!')
        return redirect('lease_list', org_slug=org_slug)
    return render(request, 'rentals/lease_confirm_delete.html', {'object': lease})


# Invoice CRUD Views
@login_required
@transaction.atomic
def invoice_create(request, org_slug):
    if request.method == 'POST':
        form = InvoiceForm(request.POST, organization=request.org)
        if form.is_valid():
            invoice = form.save(commit=False)
            invoice.organization = request.org

            # Initialize amounts
            total_amount = Decimal('0.00')
            rent_amount = Decimal('0.00')
            amenities_amount = Decimal('0.00')
            tax_amount = Decimal('0.00')

            # Get the lease to access rent and unit
            lease = invoice.lease

            # Save invoice first to get an ID for line items
            invoice.save()

            # Create line item for rent if included
            if form.cleaned_data.get('include_rent', True):
                rent_amount = lease.rent_amount
                InvoiceLineItem.objects.create(
                    organization=request.org,
                    invoice=invoice,
                    item_type='RENT',
                    description=f'Monthly Rent - {lease.unit}',
                    quantity=Decimal('1.00'),
                    unit_price=rent_amount,
                    amount=rent_amount
                )
                total_amount += rent_amount

            # Process selected amenities
            amenities = AmenityType.objects.filter(organization=request.org, is_active=True)
            for amenity in amenities:
                field_name = f'amenity_{amenity.id}'
                amount_field_name = f'amenity_amount_{amenity.id}'

                if form.cleaned_data.get(field_name, False):
                    # Get custom amount or use default
                    amenity_amount = form.cleaned_data.get(amount_field_name, amenity.default_amount)
                    if amenity_amount and amenity_amount > 0:
                        # Check if unit has custom pricing for this amenity
                        unit_amenity = UnitAmenity.objects.filter(
                            unit=lease.unit,
                            amenity_type=amenity,
                            is_active=True
                        ).first()

                        if unit_amenity:
                            amenity_amount = unit_amenity.amount

                        InvoiceLineItem.objects.create(
                            organization=request.org,
                            invoice=invoice,
                            item_type=amenity.name.upper()[:20] if amenity.name.upper() in dict(InvoiceLineItem.ITEM_TYPES) else 'OTHER',
                            description=amenity.name,
                            quantity=Decimal('1.00'),
                            unit_price=amenity_amount,
                            amount=amenity_amount,
                            amenity_type=amenity
                        )
                        amenities_amount += amenity_amount
                        total_amount += amenity_amount

            # Process selected taxes
            taxes = GovernmentTax.objects.filter(organization=request.org, is_active=True)
            for tax in taxes:
                field_name = f'tax_{tax.id}'

                if form.cleaned_data.get(field_name, False):
                    # Calculate tax on the subtotal (rent + amenities)
                    subtotal = rent_amount + amenities_amount
                    tax_value = tax.calculate_tax(subtotal)

                    if tax_value > 0:
                        InvoiceLineItem.objects.create(
                            organization=request.org,
                            invoice=invoice,
                            item_type='TAX',
                            description=tax.name,
                            quantity=Decimal('1.00'),
                            unit_price=tax_value,
                            amount=tax_value,
                            government_tax=tax
                        )
                        tax_amount += tax_value
                        total_amount += tax_value

            # Update invoice with calculated amounts
            invoice.rent_amount = rent_amount
            invoice.amenities_amount = amenities_amount
            invoice.tax_amount = tax_amount
            invoice.amount_due = total_amount
            invoice.save()

            messages.success(request, f'Invoice {invoice.number} created successfully with {invoice.line_items.count()} line items!')
            return redirect('invoice_list', org_slug=org_slug)
    else:
        form = InvoiceForm(organization=request.org)

    # Get amenities and taxes for template context
    amenities = AmenityType.objects.filter(organization=request.org, is_active=True).order_by('is_mandatory', 'name')
    taxes = GovernmentTax.objects.filter(organization=request.org, is_active=True).order_by('name')

    # Prepare amenity and tax data with form fields
    amenities_data = []
    for amenity in amenities:
        amenities_data.append({
            'obj': amenity,
            'checkbox_field': form[f'amenity_{amenity.id}'],
            'amount_field': form[f'amenity_amount_{amenity.id}'],
        })

    taxes_data = []
    for tax in taxes:
        taxes_data.append({
            'obj': tax,
            'checkbox_field': form[f'tax_{tax.id}'],
        })

    return render(request, 'billing/invoice_form.html', {
        'form': form,
        'title': 'Add Invoice',
        'amenities_data': amenities_data,
        'taxes_data': taxes_data,
    })


@login_required
def invoice_edit(request, org_slug, pk):
    invoice = get_object_or_404(Invoice, pk=pk, organization=request.org)
    if request.method == 'POST':
        form = InvoiceForm(request.POST, instance=invoice, organization=request.org)
        if form.is_valid():
            form.save()
            messages.success(request, 'Invoice updated successfully!')
            return redirect('invoice_list', org_slug=org_slug)
    else:
        form = InvoiceForm(instance=invoice, organization=request.org)
    return render(request, 'billing/invoice_form.html', {'form': form, 'title': 'Edit Invoice'})


# Payment CRUD Views
@login_required
def payment_create(request, org_slug):
    if request.method == 'POST':
        form = PaymentForm(request.POST, organization=request.org)
        if form.is_valid():
            payment = form.save(commit=False)
            payment.organization = request.org
            payment.save()

            # Update invoice payment status
            invoice = payment.invoice
            total_payments = Payment.objects.filter(invoice=invoice).aggregate(
                total=models.Sum('amount'))['total'] or 0
            invoice.amount_paid = total_payments
            invoice.is_paid = invoice.amount_paid >= invoice.amount_due
            invoice.save()

            messages.success(request, 'Payment recorded successfully!')
            return redirect('payment_list', org_slug=org_slug)
    else:
        form = PaymentForm(organization=request.org)
    return render(request, 'billing/payment_form.html', {'form': form, 'title': 'Record Payment'})


@login_required
def get_invoice_details(request, org_slug, invoice_id):
    """API endpoint to get invoice details for payment form"""
    invoice = get_object_or_404(Invoice, pk=invoice_id, organization=request.org)

    # Get line items if they exist
    line_items = []
    if hasattr(invoice, 'line_items'):
        line_items = [
            {
                'item_type': item.get_item_type_display(),
                'description': item.description,
                'quantity': str(item.quantity),
                'unit_price': str(item.unit_price),
                'amount': str(item.amount),
            }
            for item in invoice.line_items.all()
        ]

    # If no line items, create breakdown from invoice fields
    if not line_items:
        if invoice.rent_amount > 0:
            line_items.append({
                'item_type': 'Rent',
                'description': 'Monthly Rent',
                'quantity': '1',
                'unit_price': str(invoice.rent_amount),
                'amount': str(invoice.rent_amount),
            })
        if invoice.amenities_amount > 0:
            line_items.append({
                'item_type': 'Amenities',
                'description': 'Amenities Charges',
                'quantity': '1',
                'unit_price': str(invoice.amenities_amount),
                'amount': str(invoice.amenities_amount),
            })
        if invoice.tax_amount > 0:
            line_items.append({
                'item_type': 'Tax',
                'description': 'Tax',
                'quantity': '1',
                'unit_price': str(invoice.tax_amount),
                'amount': str(invoice.tax_amount),
            })
        if invoice.other_charges > 0:
            line_items.append({
                'item_type': 'Other',
                'description': 'Other Charges',
                'quantity': '1',
                'unit_price': str(invoice.other_charges),
                'amount': str(invoice.other_charges),
            })

    data = {
        'number': invoice.number,
        'lease': str(invoice.lease),
        'tenant': f"{invoice.lease.tenant.first_name} {invoice.lease.tenant.last_name}",
        'unit': str(invoice.lease.unit),
        'issue_date': invoice.issue_date.strftime('%Y-%m-%d'),
        'due_date': invoice.due_date.strftime('%Y-%m-%d'),
        'amount_due': str(invoice.amount_due),
        'amount_paid': str(invoice.amount_paid),
        'balance': str(invoice.balance),
        'line_items': line_items,
    }

    return JsonResponse(data)


# Bulk Invoice Generation
@login_required
@transaction.atomic
def bulk_invoice_generate(request, org_slug):
    """Generate invoices for all active leases with auto-captured amenities"""
    if request.method == 'POST':
        # Get parameters from form
        issue_date = request.POST.get('issue_date', date.today())
        due_date = request.POST.get('due_date', date.today() + timedelta(days=7))
        invoice_type = request.POST.get('invoice_type', 'MIXED')
        include_rent = request.POST.get('include_rent', 'on') == 'on'

        # Convert string dates to date objects if needed
        if isinstance(issue_date, str):
            issue_date = date.fromisoformat(issue_date)
        if isinstance(due_date, str):
            due_date = date.fromisoformat(due_date)

        # Get all active leases
        active_leases = Lease.objects.filter(
            organization=request.org,
            status='ACTIVE'
        ).select_related('unit', 'tenant')

        if not active_leases.exists():
            messages.warning(request, 'No active leases found to generate invoices for.')
            return redirect('invoice_list', org_slug=org_slug)

        # Get all active amenities
        amenities = AmenityType.objects.filter(
            organization=request.org,
            is_active=True
        )

        # Get selected amenities from form
        selected_amenity_ids = []
        for amenity in amenities:
            if request.POST.get(f'amenity_{amenity.id}') == 'on':
                selected_amenity_ids.append(amenity.id)

        # Generate invoices
        created_count = 0
        skipped_count = 0
        errors = []

        for lease in active_leases:
            try:
                # Check if invoice already exists for this month
                invoice_number = Invoice.generate_invoice_number(request.org)

                # Create invoice
                invoice = Invoice.objects.create(
                    organization=request.org,
                    lease=lease,
                    number=invoice_number,
                    invoice_type=invoice_type,
                    issue_date=issue_date,
                    due_date=due_date
                )

                # Initialize amounts
                total_amount = Decimal('0.00')
                rent_amount = Decimal('0.00')
                amenities_amount = Decimal('0.00')

                # Add rent line item
                if include_rent:
                    rent_amount = lease.rent_amount
                    InvoiceLineItem.objects.create(
                        organization=request.org,
                        invoice=invoice,
                        item_type='RENT',
                        description=f'Monthly Rent - {lease.unit}',
                        quantity=Decimal('1.00'),
                        unit_price=rent_amount,
                        amount=rent_amount
                    )
                    total_amount += rent_amount

                # Add amenity line items
                for amenity in amenities.filter(id__in=selected_amenity_ids):
                    # Check for unit-specific pricing
                    unit_amenity = UnitAmenity.objects.filter(
                        unit=lease.unit,
                        amenity_type=amenity,
                        is_active=True
                    ).first()

                    amenity_amount = unit_amenity.amount if unit_amenity else amenity.default_amount

                    InvoiceLineItem.objects.create(
                        organization=request.org,
                        invoice=invoice,
                        item_type=amenity.name.upper()[:20] if amenity.name.upper() in dict(InvoiceLineItem.ITEM_TYPES) else 'OTHER',
                        description=amenity.name,
                        quantity=Decimal('1.00'),
                        unit_price=amenity_amount,
                        amount=amenity_amount,
                        amenity_type=amenity
                    )
                    amenities_amount += amenity_amount
                    total_amount += amenity_amount

                # Update invoice totals
                invoice.rent_amount = rent_amount
                invoice.amenities_amount = amenities_amount
                invoice.amount_due = total_amount
                invoice.save()

                created_count += 1

            except Exception as e:
                skipped_count += 1
                errors.append(f"{lease.tenant} ({lease.unit}): {str(e)}")

        # Show results
        if created_count > 0:
            messages.success(request, f'Successfully generated {created_count} invoice(s)!')
        if skipped_count > 0:
            messages.warning(request, f'Skipped {skipped_count} lease(s). Errors: {", ".join(errors[:3])}')

        return redirect('invoice_list', org_slug=org_slug)

    # GET request - show form
    amenities = AmenityType.objects.filter(
        organization=request.org,
        is_active=True
    ).order_by('is_mandatory', 'name')

    active_leases = Lease.objects.filter(
        organization=request.org,
        status='ACTIVE'
    ).select_related('unit', 'tenant')

    context = {
        'title': 'Bulk Invoice Generation',
        'amenities': amenities,
        'active_leases': active_leases,
        'suggested_issue_date': date.today(),
        'suggested_due_date': date.today() + timedelta(days=7),
    }

    return render(request, 'billing/bulk_invoice_form.html', context)


# Invoice PDF Export
@login_required
def invoice_pdf_download(request, org_slug, pk):
    """Generate and download invoice as PDF"""
    invoice = get_object_or_404(Invoice, pk=pk, organization=request.org)

    # Generate PDF
    pdf = generate_invoice_pdf(invoice)

    # Create HTTP response with PDF
    response = HttpResponse(pdf, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="Invoice_{invoice.number}.pdf"'

    return response
