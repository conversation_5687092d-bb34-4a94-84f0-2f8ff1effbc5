from django import forms
from rentals.models import (
    Property, Unit, Tenant, Lease, Invoice, Payment, MaintenanceRequest,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem
)
from decimal import Decimal

class LoginForm(forms.Form):
    username = forms.CharField()
    password = forms.CharField(widget=forms.PasswordInput)


class PropertyForm(forms.ModelForm):
    class Meta:
        model = Property
        fields = ['name', 'address']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name.strip()) < 2:
            raise forms.ValidationError('Property name must be at least 2 characters long.')
        return name.strip()


class UnitForm(forms.ModelForm):
    class Meta:
        model = Unit
        fields = ['property', 'code', 'bedrooms', 'bathrooms', 'is_active']
        widgets = {
            'property': forms.Select(attrs={'class': 'form-select'}),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly',
                'style': 'background-color: #e9ecef; cursor: not-allowed;',
                'placeholder': 'Auto-generated'
            }),
            'bedrooms': forms.NumberInput(attrs={'class': 'form-control'}),
            'bathrooms': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['property'].queryset = Property.objects.filter(organization=organization)

        # Make code field not required and add help text
        self.fields['code'].required = False
        self.fields['code'].help_text = 'Unit code will be auto-generated based on property name'

        # If editing existing unit, show the code
        if self.instance and self.instance.pk:
            self.fields['code'].help_text = 'Unit code (auto-generated, cannot be changed)'

    def clean_bedrooms(self):
        bedrooms = self.cleaned_data['bedrooms']
        if bedrooms < 0 or bedrooms > 10:
            raise forms.ValidationError('Bedrooms must be between 0 and 10.')
        return bedrooms

    def clean_bathrooms(self):
        bathrooms = self.cleaned_data['bathrooms']
        if bathrooms < 0 or bathrooms > 10:
            raise forms.ValidationError('Bathrooms must be between 0 and 10.')
        return bathrooms


class TenantForm(forms.ModelForm):
    class Meta:
        model = Tenant
        fields = ['first_name', 'last_name', 'email', 'phone', 'id_number', 'notes']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'id_number': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def clean_email(self):
        email = self.cleaned_data['email']
        # Check if email is unique within the organization (if we have access to it)
        return email.lower()

    def clean_phone(self):
        phone = self.cleaned_data.get('phone')
        if phone:
            # Remove non-digit characters for validation
            digits_only = ''.join(filter(str.isdigit, phone))
            if len(digits_only) < 10:
                raise forms.ValidationError('Phone number must have at least 10 digits.')
        return phone


class LeaseForm(forms.ModelForm):
    class Meta:
        model = Lease
        fields = ['unit', 'tenant', 'start_date', 'end_date', 'rent_amount', 'deposit_amount', 'billing_day', 'status']
        widgets = {
            'unit': forms.Select(attrs={'class': 'form-select'}),
            'tenant': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'rent_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'deposit_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'billing_day': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '31'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['unit'].queryset = Unit.objects.filter(organization=organization)
            self.fields['tenant'].queryset = Tenant.objects.filter(organization=organization)

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        rent_amount = cleaned_data.get('rent_amount')
        deposit_amount = cleaned_data.get('deposit_amount')
        billing_day = cleaned_data.get('billing_day')

        # Validate date range
        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('End date must be after start date.')

        # Validate rent amount
        if rent_amount and rent_amount <= 0:
            raise forms.ValidationError('Rent amount must be greater than 0.')

        # Validate deposit amount
        if deposit_amount and deposit_amount < 0:
            raise forms.ValidationError('Deposit amount cannot be negative.')

        # Validate billing day
        if billing_day and (billing_day < 1 or billing_day > 31):
            raise forms.ValidationError('Billing day must be between 1 and 31.')

        return cleaned_data


class InvoiceForm(forms.ModelForm):
    # Additional fields for amenities and taxes
    include_rent = forms.BooleanField(
        initial=True,
        required=False,
        label='Include Rent',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input', 'checked': 'checked'})
    )

    # Dynamic amenity fields will be added in __init__
    # Dynamic tax fields will be added in __init__

    class Meta:
        model = Invoice
        fields = ['lease', 'number', 'invoice_type', 'issue_date', 'due_date', 'notes']
        widgets = {
            'lease': forms.Select(attrs={'class': 'form-select'}),
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'invoice_type': forms.Select(attrs={'class': 'form-select'}),
            'issue_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if self.organization:
            self.fields['lease'].queryset = Lease.objects.filter(
                organization=self.organization,
                status='ACTIVE'
            ).select_related('unit', 'tenant')

            # Customize lease display
            self.fields['lease'].label_from_instance = lambda obj: (
                f"{obj.tenant.first_name} {obj.tenant.last_name} - {obj.unit} "
                f"(Rent: KES {obj.rent_amount:.2f})"
            )

            # For new invoices, suggest a unique invoice number
            if not self.instance.pk:
                suggested_number = Invoice.generate_invoice_number(self.organization)
                self.fields['number'].widget.attrs['placeholder'] = f'Suggested: {suggested_number}'
                self.fields['number'].help_text = 'Leave blank to auto-generate, or enter a custom number'
                self.fields['number'].required = False

                # Set default dates
                from datetime import date, timedelta
                today = date.today()
                self.fields['issue_date'].initial = today
                self.fields['due_date'].initial = today + timedelta(days=7)

                # Add amenity checkboxes for active amenities
                amenities = AmenityType.objects.filter(
                    organization=self.organization,
                    is_active=True
                ).order_by('is_mandatory', 'name')

                for amenity in amenities:
                    field_name = f'amenity_{amenity.id}'
                    self.fields[field_name] = forms.BooleanField(
                        initial=amenity.is_mandatory,  # Auto-check mandatory amenities
                        required=False,
                        label=f'{amenity.name} (KES {amenity.default_amount:.2f})',
                        widget=forms.CheckboxInput(attrs={
                            'class': 'form-check-input amenity-checkbox',
                            'data-amenity-id': amenity.id,
                            'data-amount': str(amenity.default_amount),
                            'data-mandatory': 'true' if amenity.is_mandatory else 'false'
                        })
                    )

                    # Add custom amount field for each amenity
                    amount_field_name = f'amenity_amount_{amenity.id}'
                    self.fields[amount_field_name] = forms.DecimalField(
                        initial=amenity.default_amount,
                        required=False,
                        max_digits=10,
                        decimal_places=2,
                        widget=forms.NumberInput(attrs={
                            'class': 'form-control form-control-sm amenity-amount',
                            'step': '0.01',
                            'min': '0',
                            'data-amenity-id': amenity.id
                        })
                    )

                # Add tax checkboxes for active taxes
                taxes = GovernmentTax.objects.filter(
                    organization=self.organization,
                    is_active=True
                ).order_by('name')

                for tax in taxes:
                    field_name = f'tax_{tax.id}'
                    self.fields[field_name] = forms.BooleanField(
                        initial=False,
                        required=False,
                        label=f'{tax.name} ({tax.rate}%)',
                        widget=forms.CheckboxInput(attrs={
                            'class': 'form-check-input tax-checkbox',
                            'data-tax-id': tax.id,
                            'data-rate': str(tax.rate)
                        })
                    )

    def clean_number(self):
        """Validate that invoice number is unique within the organization"""
        number = self.cleaned_data.get('number')

        # Auto-generate invoice number if not provided (for new invoices only)
        if not number and not self.instance.pk:
            number = Invoice.generate_invoice_number(self.organization)
            return number

        # If editing and number is empty, keep the original
        if not number and self.instance.pk:
            return self.instance.number

        # Check if this is an edit (instance exists) or a new invoice
        if self.instance and self.instance.pk:
            # Editing existing invoice - exclude current instance from check
            existing = Invoice.objects.filter(
                organization=self.organization,
                number=number
            ).exclude(pk=self.instance.pk).exists()
        else:
            # Creating new invoice
            existing = Invoice.objects.filter(
                organization=self.organization,
                number=number
            ).exists()

        if existing:
            raise forms.ValidationError(
                f'Invoice number "{number}" already exists for this organization. '
                'Please use a different invoice number.'
            )

        return number

    def clean(self):
        cleaned_data = super().clean()
        issue_date = cleaned_data.get('issue_date')
        due_date = cleaned_data.get('due_date')
        amount_due = cleaned_data.get('amount_due')

        # Validate date range
        if issue_date and due_date and due_date < issue_date:
            raise forms.ValidationError('Due date cannot be before issue date.')

        # Validate amount
        if amount_due and amount_due <= 0:
            raise forms.ValidationError('Amount due must be greater than 0.')

        return cleaned_data


class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['invoice', 'date', 'reference', 'method', 'amount']
        widgets = {
            'invoice': forms.Select(attrs={'class': 'form-select'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reference': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Receipt #, Transaction ID'}),
            'method': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            # Only show unpaid invoices
            self.fields['invoice'].queryset = Invoice.objects.filter(
                organization=organization,
                is_paid=False
            ).select_related('lease', 'lease__tenant', 'lease__unit', 'lease__unit__property')

            # Customize invoice display to show more details
            self.fields['invoice'].label_from_instance = lambda obj: (
                f"{obj.number} - {obj.lease.tenant.first_name} {obj.lease.tenant.last_name} "
                f"({obj.lease.unit}) - Balance: KES {obj.balance:.2f}"
            )

        # Set default date to today
        from datetime import date
        if not self.instance.pk:
            self.fields['date'].initial = date.today()

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError('Payment amount must be greater than 0.')

        # Check if payment amount doesn't exceed remaining balance
        invoice = self.cleaned_data.get('invoice')
        if invoice:
            remaining_balance = invoice.amount_due - invoice.amount_paid
            if amount > remaining_balance:
                raise forms.ValidationError(
                    f'Payment amount cannot exceed remaining balance of ${remaining_balance}.'
                )

        return amount