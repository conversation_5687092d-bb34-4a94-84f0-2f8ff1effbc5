from django import forms
from decimal import Decimal
from .models import (
    Property, Unit, Tenant, Lease, Invoice, Payment, 
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem
)


class PropertyForm(forms.ModelForm):
    class Meta:
        model = Property
        fields = ['name', 'address', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }


class UnitForm(forms.ModelForm):
    class Meta:
        model = Unit
        fields = ['property', 'code', 'unit_type', 'bedrooms', 'bathrooms', 'area_sqft', 'rent_amount']
        widgets = {
            'property': forms.Select(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly',
                'style': 'background-color: #e9ecef; cursor: not-allowed;',
                'placeholder': 'Auto-generated'
            }),
            'unit_type': forms.Select(attrs={'class': 'form-control'}),
            'bedrooms': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'bathrooms': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'area_sqft': forms.NumberInput(attrs={'class': 'form-control', 'min': 0}),
            'rent_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['property'].queryset = Property.objects.filter(organization=organization)

        # Make code field not required and add help text
        self.fields['code'].required = False
        self.fields['code'].help_text = 'Unit code will be auto-generated based on property name'

        # If editing existing unit, show the code
        if self.instance and self.instance.pk:
            self.fields['code'].help_text = 'Unit code (auto-generated, cannot be changed)'


class TenantForm(forms.ModelForm):
    class Meta:
        model = Tenant
        fields = ['first_name', 'last_name', 'email', 'phone', 'id_number']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={'class': 'form-control'}),
            'id_number': forms.TextInput(attrs={'class': 'form-control'}),
        }


class LeaseForm(forms.ModelForm):
    class Meta:
        model = Lease
        fields = ['unit', 'tenant', 'start_date', 'end_date', 'rent_amount', 'deposit_amount', 'status']
        widgets = {
            'unit': forms.Select(attrs={'class': 'form-control'}),
            'tenant': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'rent_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'deposit_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }


class InvoiceForm(forms.ModelForm):
    class Meta:
        model = Invoice
        fields = [
            'lease', 'number', 'invoice_type', 'issue_date', 'due_date',
            'rent_amount', 'amenities_amount', 'tax_amount', 'other_charges', 'notes'
        ]
        widgets = {
            'lease': forms.Select(attrs={'class': 'form-control'}),
            'number': forms.TextInput(attrs={'class': 'form-control'}),
            'invoice_type': forms.Select(attrs={'class': 'form-control'}),
            'issue_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'rent_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'amenities_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'tax_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'other_charges': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['lease'].queryset = Lease.objects.filter(organization=organization)


class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['invoice', 'amount', 'date', 'method', 'reference', 'phone_number']
        widgets = {
            'invoice': forms.Select(attrs={'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'method': forms.Select(attrs={'class': 'form-control'}),
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '0712345678'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['invoice'].queryset = Invoice.objects.filter(
                organization=organization, is_paid=False
            )


class AmenityTypeForm(forms.ModelForm):
    class Meta:
        model = AmenityType
        fields = ['name', 'description', 'billing_cycle', 'default_amount', 'is_mandatory', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'billing_cycle': forms.Select(attrs={'class': 'form-control'}),
            'default_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'is_mandatory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class UnitAmenityForm(forms.ModelForm):
    class Meta:
        model = UnitAmenity
        fields = ['unit', 'amenity_type', 'custom_amount', 'is_active', 'start_date', 'end_date']
        widgets = {
            'unit': forms.Select(attrs={'class': 'form-control'}),
            'amenity_type': forms.Select(attrs={'class': 'form-control'}),
            'custom_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['unit'].queryset = Unit.objects.filter(organization=organization)
            self.fields['amenity_type'].queryset = AmenityType.objects.filter(
                organization=organization, is_active=True
            )


class GovernmentTaxForm(forms.ModelForm):
    class Meta:
        model = GovernmentTax
        fields = [
            'name', 'tax_type', 'calculation_method', 'rate', 
            'description', 'is_active', 'effective_date'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'tax_type': forms.Select(attrs={'class': 'form-control'}),
            'calculation_method': forms.Select(attrs={'class': 'form-control'}),
            'rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'effective_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }


class InvoiceLineItemForm(forms.ModelForm):
    class Meta:
        model = InvoiceLineItem
        fields = [
            'item_type', 'description', 'quantity', 'unit_price', 
            'amenity_type', 'government_tax'
        ]
        widgets = {
            'item_type': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.TextInput(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'amenity_type': forms.Select(attrs={'class': 'form-control'}),
            'government_tax': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['amenity_type'].queryset = AmenityType.objects.filter(
                organization=organization, is_active=True
            )
            self.fields['government_tax'].queryset = GovernmentTax.objects.filter(
                organization=organization, is_active=True
            )


class BulkInvoiceForm(forms.Form):
    """Form for generating bulk invoices for all active leases"""
    invoice_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        help_text="Date to issue the invoices"
    )
    due_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        help_text="Payment due date"
    )
    include_rent = forms.BooleanField(
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Include monthly rent charges"
    )
    include_amenities = forms.BooleanField(
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Include amenity charges"
    )
    include_taxes = forms.BooleanField(
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Include government taxes"
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        help_text="Optional notes to include in all invoices"
    )


class ReportFilterForm(forms.Form):
    """Form for filtering financial reports"""
    start_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        help_text="Report start date"
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        help_text="Report end date"
    )
    property_filter = forms.ModelChoiceField(
        queryset=Property.objects.none(),
        required=False,
        empty_label="All Properties",
        widget=forms.Select(attrs={'class': 'form-control'}),
        help_text="Filter by specific property"
    )
    
    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        if organization:
            self.fields['property_filter'].queryset = Property.objects.filter(
                organization=organization
            )
