{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h1 class="h4 mb-0">{{ title }}</h1>
                                <a href="{% org_url 'lease_list' %}" class="btn btn-secondary">Back to Leases</a>
                            </div>
                            <div class="card">
                                <div class="card-body">
                                    <form method="post">
                                        {% csrf_token %}
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="{{ form.unit.id_for_label }}" class="form-label">Unit</label>
                                                    {{ form.unit }}
                                                    {% if form.unit.errors %}
                                                        <div class="text-danger">{{ form.unit.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="{{ form.tenant.id_for_label }}" class="form-label">Tenant</label>
                                                    {{ form.tenant }}
                                                    {% if form.tenant.errors %}
                                                        <div class="text-danger">{{ form.tenant.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date</label>
                                                    {{ form.start_date }}
                                                    {% if form.start_date.errors %}
                                                        <div class="text-danger">{{ form.start_date.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                                                    {{ form.end_date }}
                                                    {% if form.end_date.errors %}
                                                        <div class="text-danger">{{ form.end_date.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="{{ form.rent_amount.id_for_label }}" class="form-label">Rent Amount</label>
                                                    {{ form.rent_amount }}
                                                    {% if form.rent_amount.errors %}
                                                        <div class="text-danger">{{ form.rent_amount.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="{{ form.deposit_amount.id_for_label }}" class="form-label">Deposit Amount</label>
                                                    {{ form.deposit_amount }}
                                                    {% if form.deposit_amount.errors %}
                                                        <div class="text-danger">{{ form.deposit_amount.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="{{ form.billing_day.id_for_label }}" class="form-label">Billing Day</label>
                                                    {{ form.billing_day }}
                                                    {% if form.billing_day.errors %}
                                                        <div class="text-danger">{{ form.billing_day.errors }}</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                            {{ form.status }}
                                            {% if form.status.errors %}
                                                <div class="text-danger">{{ form.status.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">Save Lease</button>
                                            <a href="{% org_url 'lease_list' %}" class="btn btn-secondary">Cancel</a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% include 'footer.html' %}
{% endblock %}
