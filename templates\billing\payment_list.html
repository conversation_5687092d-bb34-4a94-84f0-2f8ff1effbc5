{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h1 class="h4 mb-0">Payments</h1>
                                    <a href="{% org_url 'payment_create' %}" class="btn btn-sm btn-primary">Record Payment</a>
                                </div>
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Date</th>
                                            <th>Invoice</th>
                                            <th>Tenant</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Reference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for payment in items %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>{{ payment.date }}</td>
                                            <td><a href="{% org_url 'invoice_detail' pk=payment.invoice.pk %}">{{ payment.invoice.number }}</a></td>
                                            <td>{{ payment.invoice.lease.tenant }}</td>
                                            <td>${{ payment.amount }}</td>
                                            <td>{{ payment.method }}</td>
                                            <td>{{ payment.reference|default:'—' }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="7" class="text-center">No payments yet.</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% include 'footer.html' %}
{% endblock %}
