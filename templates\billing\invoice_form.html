{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h1 class="h4 mb-0">{{ title }}</h1>
                                    <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">Back to Invoices</a>
                                </div>
                                <div class="card">
                                    <div class="card-body">
                                        <form method="post" id="invoice-form">
                                            {% csrf_token %}

                                            <!-- Lease Selection -->
                                            <div class="mb-3">
                                                <label for="{{ form.lease.id_for_label }}" class="form-label">Lease *</label>
                                                {{ form.lease }}
                                                {% if form.lease.errors %}
                                                    <div class="text-danger">{{ form.lease.errors }}</div>
                                                {% endif %}
                                            </div>

                                            <!-- Invoice Details -->
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="{{ form.number.id_for_label }}" class="form-label">Invoice Number</label>
                                                        {{ form.number }}
                                                        {% if form.number.help_text %}
                                                            <div class="form-text">{{ form.number.help_text }}</div>
                                                        {% endif %}
                                                        {% if form.number.errors %}
                                                            <div class="text-danger">{{ form.number.errors }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="{{ form.invoice_type.id_for_label }}" class="form-label">Invoice Type</label>
                                                        {{ form.invoice_type }}
                                                        {% if form.invoice_type.errors %}
                                                            <div class="text-danger">{{ form.invoice_type.errors }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="{{ form.issue_date.id_for_label }}" class="form-label">Issue Date *</label>
                                                        {{ form.issue_date }}
                                                        {% if form.issue_date.errors %}
                                                            <div class="text-danger">{{ form.issue_date.errors }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="{{ form.due_date.id_for_label }}" class="form-label">Due Date *</label>
                                                        {{ form.due_date }}
                                                        {% if form.due_date.errors %}
                                                            <div class="text-danger">{{ form.due_date.errors }}</div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Billing Items Section -->
                                            <div class="card mb-3">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">Billing Items</h6>
                                                </div>
                                                <div class="card-body">
                                                    <!-- Rent -->
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            {{ form.include_rent }}
                                                            <label class="form-check-label" for="{{ form.include_rent.id_for_label }}">
                                                                <strong>{{ form.include_rent.label }}</strong>
                                                            </label>
                                                        </div>
                                                        <small class="text-muted">Monthly rent will be automatically calculated from the lease</small>
                                                    </div>

                                                    <!-- Amenities -->
                                                    <div class="mb-3">
                                                        <h6 class="border-bottom pb-2">Amenities & Services</h6>
                                                        {% if amenities_data %}
                                                        <div class="row">
                                                            {% for item in amenities_data %}
                                                            <div class="col-md-6 mb-2">
                                                                <div class="card">
                                                                    <div class="card-body p-2">
                                                                        <div class="form-check">
                                                                            {{ item.checkbox_field }}
                                                                            <label class="form-check-label" for="{{ item.checkbox_field.id_for_label }}">
                                                                                {{ item.obj.name }}
                                                                                {% if item.obj.is_mandatory %}
                                                                                    <span class="badge bg-warning text-dark">Mandatory</span>
                                                                                {% endif %}
                                                                            </label>
                                                                        </div>
                                                                        <div class="input-group input-group-sm mt-1">
                                                                            <span class="input-group-text">KES</span>
                                                                            {{ item.amount_field }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                        {% else %}
                                                        <p class="text-muted">No amenities configured. <a href="/admin/rentals/amenitytype/add/" target="_blank">Add amenities</a> in the admin panel.</p>
                                                        {% endif %}
                                                    </div>

                                                    <!-- Taxes -->
                                                    <div class="mb-3">
                                                        <h6 class="border-bottom pb-2">Taxes</h6>
                                                        {% if taxes_data %}
                                                        {% for item in taxes_data %}
                                                        <div class="form-check">
                                                            {{ item.checkbox_field }}
                                                            <label class="form-check-label" for="{{ item.checkbox_field.id_for_label }}">
                                                                {{ item.obj.name }} ({{ item.obj.rate }}% {{ item.obj.get_calculation_method_display }})
                                                            </label>
                                                        </div>
                                                        {% endfor %}
                                                        {% else %}
                                                        <p class="text-muted">No taxes configured. <a href="/admin/rentals/governmenttax/add/" target="_blank">Add taxes</a> in the admin panel if needed.</p>
                                                        {% endif %}
                                                    </div>

                                                    <!-- Total Preview -->
                                                    <div class="alert alert-info mt-3">
                                                        <div class="d-flex justify-content-between">
                                                            <strong>Estimated Total:</strong>
                                                            <strong id="total-preview">KES 0.00</strong>
                                                        </div>
                                                        <small class="text-muted">This is calculated automatically based on selected items</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Notes -->
                                            <div class="mb-3">
                                                <label for="{{ form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                                                {{ form.notes }}
                                                {% if form.notes.errors %}
                                                    <div class="text-danger">{{ form.notes.errors }}</div>
                                                {% endif %}
                                            </div>

                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">Create Invoice</button>
                                                <a href="{% org_url 'invoice_list' %}" class="btn btn-secondary">Cancel</a>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% include 'footer.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const leaseSelect = document.getElementById('{{ form.lease.id_for_label }}');
    const includeRentCheckbox = document.getElementById('{{ form.include_rent.id_for_label }}');
    const amenityCheckboxes = document.querySelectorAll('.amenity-checkbox');
    const amenityAmounts = document.querySelectorAll('.amenity-amount');
    const taxCheckboxes = document.querySelectorAll('.tax-checkbox');
    const totalPreview = document.getElementById('total-preview');

    let currentRentAmount = 0;

    // Extract rent amount from lease selection
    function updateRentAmount() {
        const selectedOption = leaseSelect.options[leaseSelect.selectedIndex];
        if (selectedOption && selectedOption.text) {
            const match = selectedOption.text.match(/Rent: KES ([\d,]+\.?\d*)/);
            if (match) {
                currentRentAmount = parseFloat(match[1].replace(/,/g, ''));
            }
        }
        calculateTotal();
    }

    // Calculate total amount
    function calculateTotal() {
        let total = 0;

        // Add rent if included
        if (includeRentCheckbox && includeRentCheckbox.checked) {
            total += currentRentAmount;
        }

        // Add selected amenities
        amenityCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const amenityId = checkbox.dataset.amenityId;
                const amountInput = document.querySelector(`input[data-amenity-id="${amenityId}"].amenity-amount`);
                if (amountInput && amountInput.value) {
                    total += parseFloat(amountInput.value) || 0;
                }
            }
        });

        // Calculate subtotal before tax
        const subtotal = total;

        // Add selected taxes
        taxCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const taxRate = parseFloat(checkbox.dataset.rate) || 0;
                total += (subtotal * taxRate) / 100;
            }
        });

        // Update preview
        totalPreview.textContent = `KES ${total.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
    }

    // Event listeners
    if (leaseSelect) {
        leaseSelect.addEventListener('change', updateRentAmount);
        updateRentAmount(); // Initial calculation
    }

    if (includeRentCheckbox) {
        includeRentCheckbox.addEventListener('change', calculateTotal);
    }

    amenityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', calculateTotal);
    });

    amenityAmounts.forEach(input => {
        input.addEventListener('input', calculateTotal);
    });

    taxCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', calculateTotal);
    });

    // Initial calculation
    calculateTotal();
});
</script>

{% endblock %}
