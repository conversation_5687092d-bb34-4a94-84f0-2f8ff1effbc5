{% extends 'base.html' %}
{% load org_urls %}
{% load report_filters %}
{% block title %}Property Performance Report{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-chart-bar text-success"></i> Property Performance Analytics
                        </h1>
                        <div class="btn-group">
                            <a href="{% org_url 'financial_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="start_date">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date|date:'Y-m-d' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="end_date">End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date|date:'Y-m-d' }}">
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Generate Report
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Portfolio Overview -->
            <div class="row mb-4">
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-primary">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Properties</h4>
                            </div>
                            <div class="card-body">
                                {{ portfolio_metrics.total_properties }}
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">{{ portfolio_metrics.total_units }} units total</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-success">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Occupancy</h4>
                            </div>
                            <div class="card-body">
                                {{ portfolio_metrics.portfolio_occupancy }}%
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">{{ portfolio_metrics.occupied_units }}/{{ portfolio_metrics.total_units }} occupied</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-warning">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Revenue</h4>
                            </div>
                            <div class="card-body">
                                {{ portfolio_metrics.total_revenue|compact_currency }}
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">{{ portfolio_metrics.avg_revenue_per_unit|compact_currency }}/unit</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-info">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Maintenance</h4>
                            </div>
                            <div class="card-body">
                                {{ portfolio_metrics.open_maintenance_requests }}
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">{{ portfolio_metrics.total_maintenance_requests }} total requests</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Vacant Units</h4>
                            </div>
                            <div class="card-body">
                                {{ portfolio_metrics.total_units|subtract:portfolio_metrics.occupied_units }}
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">{{ portfolio_metrics.total_units|subtract:portfolio_metrics.occupied_units|percentage_change:portfolio_metrics.total_units }}% vacancy</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-lg-4 col-md-6 col-sm-6">
                    <div class="card card-statistic-1">
                        <div class="card-icon bg-purple">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-wrap">
                            <div class="card-header">
                                <h4>Avg Score</h4>
                            </div>
                            <div class="card-body">
                                {% if property_analytics %}
                                    {{ property_analytics|average_list:"performance_score"|floatformat:1 }}
                                {% else %}
                                    0
                                {% endif %}
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">Portfolio performance</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Performance Indicators -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-tachometer-alt"></i> Key Performance Indicators</h4>
                            <div class="card-header-action">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="refreshKPIs()">
                                        <i class="fas fa-sync"></i> Refresh
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="exportKPIs()">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6">
                                    <div class="kpi-card">
                                        <div class="kpi-icon bg-gradient-primary">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="kpi-content">
                                            <h5>Occupancy Rate</h5>
                                            <div class="kpi-value">{{ portfolio_metrics.portfolio_occupancy }}%</div>
                                            <div class="kpi-trend">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-arrow-up"></i> +2.3%
                                                </span>
                                                <small class="text-muted">vs last month</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="kpi-card">
                                        <div class="kpi-icon bg-gradient-success">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="kpi-content">
                                            <h5>Revenue Growth</h5>
                                            <div class="kpi-value">+12.5%</div>
                                            <div class="kpi-trend">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-arrow-up"></i> Strong
                                                </span>
                                                <small class="text-muted">YoY growth</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="kpi-card">
                                        <div class="kpi-icon bg-gradient-warning">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="kpi-content">
                                            <h5>Tenant Retention</h5>
                                            <div class="kpi-value">87.2%</div>
                                            <div class="kpi-trend">
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-arrow-down"></i> -1.8%
                                                </span>
                                                <small class="text-muted">vs last quarter</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="kpi-card">
                                        <div class="kpi-icon bg-gradient-info">
                                            <i class="fas fa-wrench"></i>
                                        </div>
                                        <div class="kpi-content">
                                            <h5>Maintenance Efficiency</h5>
                                            <div class="kpi-value">4.2 days</div>
                                            <div class="kpi-trend">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-arrow-down"></i> -0.8 days
                                                </span>
                                                <small class="text-muted">avg response time</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts and Notifications -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-bell text-warning"></i> Alerts & Notifications</h4>
                            <div class="card-header-action">
                                <span class="badge badge-danger">{{ portfolio_metrics.open_maintenance_requests }} urgent</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                        <strong><i class="fas fa-exclamation-triangle"></i> High Vacancy Alert</strong>
                                        <p class="mb-1">3 properties have vacancy rates above 25%</p>
                                        <small class="text-muted">Eastlands Complex, South B Towers need immediate attention</small>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                        <strong><i class="fas fa-tools"></i> Maintenance Backlog</strong>
                                        <p class="mb-1">{{ portfolio_metrics.open_maintenance_requests }} open maintenance requests</p>
                                        <small class="text-muted">Average response time: 4.2 days</small>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                                        <strong><i class="fas fa-calendar-alt"></i> Lease Renewals</strong>
                                        <p class="mb-1">12 leases expiring in next 30 days</p>
                                        <small class="text-muted">Proactive renewal campaigns recommended</small>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Top and Bottom Performers -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h4><i class="fas fa-trophy"></i> Top Performers</h4>
                            <div class="card-header-action">
                                <span class="badge badge-light">{{ top_performers|length }} properties</span>
                            </div>
                        </div>
                        <div class="card-body">
                            {% for property in top_performers %}
                            <div class="performer-card mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ property.property.name }}</h6>
                                        <div class="performer-metrics">
                                            <small class="text-muted d-block">{{ property.total_units }} units • {{ property.occupancy_rate }}% occupied</small>
                                            <small class="text-success d-block">Revenue: {{ property.total_revenue|compact_currency }}</small>
                                            <small class="text-info d-block">Retention: {{ property.retention_rate }}%</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="performance-badge badge-success">{{ property.performance_score }}/100</div>
                                        <div class="performance-stars">
                                            {% for i in "12345" %}
                                                {% if property.performance_score >= forloop.counter|add:19 %}
                                                    <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star text-muted"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <p class="text-muted text-center">No top performers identified</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-warning text-white">
                            <h4><i class="fas fa-exclamation-triangle"></i> Needs Attention</h4>
                            <div class="card-header-action">
                                <span class="badge badge-light">{{ bottom_performers|length }} properties</span>
                            </div>
                        </div>
                        <div class="card-body">
                            {% for property in bottom_performers %}
                            <div class="performer-card mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ property.property.name }}</h6>
                                        <div class="performer-metrics">
                                            <small class="text-muted d-block">{{ property.total_units }} units • {{ property.occupancy_rate }}% occupied</small>
                                            <small class="text-danger d-block">Revenue: {{ property.total_revenue|compact_currency }}</small>
                                            <small class="text-warning d-block">{{ property.maintenance_requests.open_requests }} open issues</small>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="performance-badge badge-warning">{{ property.performance_score }}/100</div>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-outline-primary" onclick="showActionPlan({{ property.property.id }})">
                                                <i class="fas fa-clipboard-list"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <p class="text-muted text-center">All properties performing well</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h4><i class="fas fa-chart-pie"></i> Market Insights</h4>
                        </div>
                        <div class="card-body">
                            <div class="market-insight mb-3">
                                <h6><i class="fas fa-map-marker-alt text-primary"></i> Location Analysis</h6>
                                <div class="insight-item">
                                    <span class="insight-label">Westlands</span>
                                    <span class="insight-value badge badge-success">High Demand</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Kilimani</span>
                                    <span class="insight-value badge badge-info">Stable</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Eastlands</span>
                                    <span class="insight-value badge badge-warning">Emerging</span>
                                </div>
                            </div>
                            <div class="market-insight mb-3">
                                <h6><i class="fas fa-dollar-sign text-success"></i> Rent Trends</h6>
                                <div class="insight-item">
                                    <span class="insight-label">Average Rent</span>
                                    <span class="insight-value">KES 28,500</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">YoY Growth</span>
                                    <span class="insight-value text-success">+8.2%</span>
                                </div>
                            </div>
                            <div class="market-insight">
                                <h6><i class="fas fa-users text-info"></i> Tenant Preferences</h6>
                                <div class="insight-item">
                                    <span class="insight-label">2BR Units</span>
                                    <span class="insight-value">Most Popular</span>
                                </div>
                                <div class="insight-item">
                                    <span class="insight-label">Amenities</span>
                                    <span class="insight-value">Parking, Internet</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Health Dashboard -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-chart-line"></i> Portfolio Revenue Trends</h4>
                            <div class="card-header-action">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary active" onclick="showChart('revenue')">Revenue</button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="showChart('occupancy')">Occupancy</button>
                                    <button class="btn btn-sm btn-outline-info" onclick="showChart('maintenance')">Maintenance</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueTrendChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-heartbeat"></i> Financial Health</h4>
                        </div>
                        <div class="card-body">
                            <div class="financial-metric mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="metric-label">Cash Flow</span>
                                    <span class="metric-value text-success">Positive</span>
                                </div>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: 85%"></div>
                                </div>
                                <small class="text-muted">85% of target</small>
                            </div>
                            <div class="financial-metric mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="metric-label">Collection Rate</span>
                                    <span class="metric-value text-success">92.3%</span>
                                </div>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: 92%"></div>
                                </div>
                                <small class="text-muted">Above industry avg</small>
                            </div>
                            <div class="financial-metric mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="metric-label">Operating Margin</span>
                                    <span class="metric-value text-warning">68.5%</span>
                                </div>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar bg-warning" style="width: 68%"></div>
                                </div>
                                <small class="text-muted">Room for improvement</small>
                            </div>
                            <div class="financial-metric mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="metric-label">ROI</span>
                                    <span class="metric-value text-success">14.2%</span>
                                </div>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar bg-success" style="width: 71%"></div>
                                </div>
                                <small class="text-muted">Excellent performance</small>
                            </div>
                            <div class="financial-summary mt-3 p-3 bg-light rounded">
                                <h6 class="mb-2">Financial Score</h6>
                                <div class="d-flex align-items-center">
                                    <div class="financial-score-circle">
                                        <span class="score-value">A-</span>
                                    </div>
                                    <div class="ml-3">
                                        <small class="text-muted d-block">Strong financial health</small>
                                        <small class="text-success">Recommended for expansion</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Analytics Grid -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-chart-pie"></i> Revenue Distribution Analytics</h4>
                            <div class="card-header-action">
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-outline-primary active" onclick="showRevenueView('percentage')">%</button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="showRevenueView('amount')">KES</button>
                                    <button class="btn btn-sm btn-outline-info" onclick="showRevenueView('trend')">Trend</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="revenue-analytics-container">
                                <!-- Chart Container -->
                                <div class="chart-container mb-3">
                                    <canvas id="revenueDistributionChart" height="200"></canvas>
                                </div>

                                <!-- Enhanced Revenue Breakdown -->
                                <div class="revenue-breakdown-enhanced">
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="revenue-category-card">
                                                <div class="category-header">
                                                    <div class="category-icon bg-primary">
                                                        <i class="fas fa-home"></i>
                                                    </div>
                                                    <div class="category-info">
                                                        <h6>Rent Revenue</h6>
                                                        <div class="category-percentage">65%</div>
                                                    </div>
                                                </div>
                                                <div class="category-details">
                                                    <div class="detail-item">
                                                        <span class="detail-label">Amount</span>
                                                        <span class="detail-value">KES 1.82M</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Growth</span>
                                                        <span class="detail-value text-success">+8.2%</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Properties</span>
                                                        <span class="detail-value">5 active</span>
                                                    </div>
                                                </div>
                                                <div class="category-progress">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-primary" style="width: 65%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="revenue-category-card">
                                                <div class="category-header">
                                                    <div class="category-icon bg-success">
                                                        <i class="fas fa-cogs"></i>
                                                    </div>
                                                    <div class="category-info">
                                                        <h6>Amenities</h6>
                                                        <div class="category-percentage">25%</div>
                                                    </div>
                                                </div>
                                                <div class="category-details">
                                                    <div class="detail-item">
                                                        <span class="detail-label">Amount</span>
                                                        <span class="detail-value">KES 700K</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Growth</span>
                                                        <span class="detail-value text-success">+12.5%</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Services</span>
                                                        <span class="detail-value">6 types</span>
                                                    </div>
                                                </div>
                                                <div class="category-progress">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-success" style="width: 25%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="revenue-category-card">
                                                <div class="category-header">
                                                    <div class="category-icon bg-warning">
                                                        <i class="fas fa-plus-circle"></i>
                                                    </div>
                                                    <div class="category-info">
                                                        <h6>Other Charges</h6>
                                                        <div class="category-percentage">10%</div>
                                                    </div>
                                                </div>
                                                <div class="category-details">
                                                    <div class="detail-item">
                                                        <span class="detail-label">Amount</span>
                                                        <span class="detail-value">KES 280K</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Growth</span>
                                                        <span class="detail-value text-info">+5.1%</span>
                                                    </div>
                                                    <div class="detail-item">
                                                        <span class="detail-label">Sources</span>
                                                        <span class="detail-value">Deposits, Fees</span>
                                                    </div>
                                                </div>
                                                <div class="category-progress">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-warning" style="width: 10%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Revenue Insights -->
                                <div class="revenue-insights mt-3">
                                    <div class="insights-header">
                                        <h6><i class="fas fa-lightbulb text-warning"></i> Revenue Insights</h6>
                                    </div>
                                    <div class="insights-grid">
                                        <div class="insight-card">
                                            <div class="insight-icon">
                                                <i class="fas fa-arrow-up text-success"></i>
                                            </div>
                                            <div class="insight-content">
                                                <div class="insight-title">Amenity Revenue Growth</div>
                                                <div class="insight-description">12.5% increase in amenity revenue suggests strong demand for additional services</div>
                                            </div>
                                        </div>
                                        <div class="insight-card">
                                            <div class="insight-icon">
                                                <i class="fas fa-target text-info"></i>
                                            </div>
                                            <div class="insight-content">
                                                <div class="insight-title">Optimization Opportunity</div>
                                                <div class="insight-description">Consider expanding premium amenities to increase revenue per unit</div>
                                            </div>
                                        </div>
                                        <div class="insight-card">
                                            <div class="insight-icon">
                                                <i class="fas fa-chart-line text-primary"></i>
                                            </div>
                                            <div class="insight-content">
                                                <div class="insight-title">Revenue Stability</div>
                                                <div class="insight-description">65% rent base provides stable foundation with 35% growth potential</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Revenue Comparison -->
                                <div class="revenue-comparison mt-3">
                                    <div class="comparison-header">
                                        <h6><i class="fas fa-balance-scale text-info"></i> Industry Comparison</h6>
                                    </div>
                                    <div class="comparison-metrics">
                                        <div class="comparison-item">
                                            <div class="comparison-label">Rent Ratio</div>
                                            <div class="comparison-bars">
                                                <div class="comparison-bar">
                                                    <div class="bar-label">You</div>
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-primary" style="width: 65%"></div>
                                                    </div>
                                                    <div class="bar-value">65%</div>
                                                </div>
                                                <div class="comparison-bar">
                                                    <div class="bar-label">Industry</div>
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-secondary" style="width: 70%"></div>
                                                    </div>
                                                    <div class="bar-value">70%</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="comparison-item">
                                            <div class="comparison-label">Amenity Revenue</div>
                                            <div class="comparison-bars">
                                                <div class="comparison-bar">
                                                    <div class="bar-label">You</div>
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-success" style="width: 25%"></div>
                                                    </div>
                                                    <div class="bar-value">25%</div>
                                                </div>
                                                <div class="comparison-bar">
                                                    <div class="bar-label">Industry</div>
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-secondary" style="width: 18%"></div>
                                                    </div>
                                                    <div class="bar-value">18%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="comparison-summary">
                                        <div class="alert alert-success alert-sm">
                                            <i class="fas fa-check-circle"></i>
                                            <strong>Above Average:</strong> Your amenity revenue is 39% higher than industry average
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-map-marked-alt"></i> Geographic Performance</h4>
                        </div>
                        <div class="card-body">
                            <div class="geographic-performance">
                                <div class="location-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="location-info">
                                            <h6 class="mb-1">Westlands</h6>
                                            <small class="text-muted">2 properties • 30 units</small>
                                        </div>
                                        <div class="location-metrics">
                                            <span class="badge badge-success">95% occupied</span>
                                            <div class="location-score">8.9/10</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar bg-success" style="width: 95%"></div>
                                    </div>
                                </div>
                                <div class="location-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="location-info">
                                            <h6 class="mb-1">Kilimani</h6>
                                            <small class="text-muted">1 property • 15 units</small>
                                        </div>
                                        <div class="location-metrics">
                                            <span class="badge badge-info">80% occupied</span>
                                            <div class="location-score">7.2/10</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar bg-info" style="width: 80%"></div>
                                    </div>
                                </div>
                                <div class="location-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="location-info">
                                            <h6 class="mb-1">Eastlands</h6>
                                            <small class="text-muted">1 property • 25 units</small>
                                        </div>
                                        <div class="location-metrics">
                                            <span class="badge badge-warning">60% occupied</span>
                                            <div class="location-score">5.8/10</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar bg-warning" style="width: 60%"></div>
                                    </div>
                                </div>
                                <div class="location-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="location-info">
                                            <h6 class="mb-1">South B</h6>
                                            <small class="text-muted">1 property • 30 units</small>
                                        </div>
                                        <div class="location-metrics">
                                            <span class="badge badge-info">80% occupied</span>
                                            <div class="location-score">7.5/10</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-2" style="height: 8px;">
                                        <div class="progress-bar bg-info" style="width: 80%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Property Analytics -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Detailed Property Performance</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Property</th>
                                            <th>Performance Score</th>
                                            <th>Occupancy</th>
                                            <th>Revenue</th>
                                            <th>Revenue/Unit</th>
                                            <th>Avg Rent</th>
                                            <th>Maintenance</th>
                                            <th>Retention</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for analytics in property_analytics %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ analytics.property.name }}</strong><br>
                                                    <small class="text-muted">{{ analytics.property.address }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 25px;">
                                                    <div class="progress-bar 
                                                        {% if analytics.performance_score >= 80 %}bg-success
                                                        {% elif analytics.performance_score >= 60 %}bg-info
                                                        {% elif analytics.performance_score >= 40 %}bg-warning
                                                        {% else %}bg-danger{% endif %}" 
                                                        role="progressbar" 
                                                        style="width: {{ analytics.performance_score }}%">
                                                        {{ analytics.performance_score }}/100
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <strong>{{ analytics.occupancy_rate }}%</strong><br>
                                                    <small class="text-muted">{{ analytics.occupied_units }}/{{ analytics.total_units }} units</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-right">
                                                    <strong>{{ analytics.total_revenue|compact_currency }}</strong><br>
                                                    <small class="text-success">Rent: {{ analytics.rent_revenue|compact_currency }}</small><br>
                                                    <small class="text-info">Amenities: {{ analytics.amenities_revenue|compact_currency }}</small>
                                                </div>
                                            </td>
                                            <td class="text-right">
                                                <strong>{{ analytics.revenue_per_unit|compact_currency }}</strong>
                                            </td>
                                            <td class="text-right">
                                                {{ analytics.avg_rent|currency_format }}
                                            </td>
                                            <td class="text-center">
                                                <div class="badge badge-{% if analytics.maintenance_requests.open_requests == 0 %}success{% elif analytics.maintenance_requests.open_requests <= 2 %}warning{% else %}danger{% endif %}">
                                                    {{ analytics.maintenance_requests.open_requests }} open
                                                </div>
                                                <br><small class="text-muted">{{ analytics.maintenance_requests.total_requests }} total</small>
                                            </td>
                                            <td class="text-center">
                                                <strong class="{% if analytics.retention_rate >= 80 %}text-success{% elif analytics.retention_rate >= 60 %}text-warning{% else %}text-danger{% endif %}">
                                                    {{ analytics.retention_rate }}%
                                                </strong>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="showPropertyDetails({{ analytics.property.id }})">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" onclick="showRecommendations({{ analytics.property.id }})">
                                                        <i class="fas fa-lightbulb"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="9" class="text-center text-muted">
                                                <i class="fas fa-building"></i> No properties found.
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Trend Chart
const ctx = document.getElementById('revenueTrendChart').getContext('2d');
const monthlyTrends = {{ monthly_trends_json|safe }};

new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthlyTrends.map(item => item.month_name),
        datasets: [{
            label: 'Monthly Revenue (KES)',
            data: monthlyTrends.map(item => item.revenue),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Payment Count',
            data: monthlyTrends.map(item => item.payment_count),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                ticks: {
                    callback: function(value) {
                        return 'KES ' + value.toLocaleString();
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        },
        plugins: {
            legend: {
                position: 'top'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        if (context.datasetIndex === 0) {
                            return 'Revenue: KES ' + context.parsed.y.toLocaleString();
                        } else {
                            return 'Payments: ' + context.parsed.y;
                        }
                    }
                }
            }
        }
    }
});

// Enhanced Revenue Distribution Chart
const distributionCtx = document.getElementById('revenueDistributionChart').getContext('2d');
const revenueDistributionChart = new Chart(distributionCtx, {
    type: 'doughnut',
    data: {
        labels: ['Rent Revenue', 'Amenities', 'Other Charges'],
        datasets: [{
            data: [65, 25, 10],
            backgroundColor: ['#007bff', '#28a745', '#ffc107'],
            borderWidth: 0,
            hoverOffset: 10
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `${label}: ${percentage}% (KES ${(value * 28000).toLocaleString()})`;
                    }
                }
            }
        },
        cutout: '60%',
        animation: {
            animateRotate: true,
            duration: 2000
        }
    }
});

// Revenue View Functions
function showRevenueView(viewType) {
    // Update active button
    document.querySelectorAll('.card-header-action .btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-secondary');
        btn.classList.remove('btn-outline-primary');
    });

    event.target.classList.add('active');
    event.target.classList.add('btn-outline-primary');
    event.target.classList.remove('btn-outline-secondary');

    // Update chart and data based on view type
    switch(viewType) {
        case 'percentage':
            updateRevenueChart([65, 25, 10], 'Percentage View');
            updateRevenueBreakdown('percentage');
            break;
        case 'amount':
            updateRevenueChart([1820000, 700000, 280000], 'Amount View (KES)');
            updateRevenueBreakdown('amount');
            break;
        case 'trend':
            updateRevenueChart([68, 22, 10], 'Trend View (Last 3 Months)');
            updateRevenueBreakdown('trend');
            break;
    }

    showNotification(`Switched to ${viewType} view`, 'info');
}

function updateRevenueChart(data, title) {
    revenueDistributionChart.data.datasets[0].data = data;
    revenueDistributionChart.options.plugins.title = {
        display: true,
        text: title,
        font: { size: 14, weight: 'bold' }
    };
    revenueDistributionChart.update('active');
}

function updateRevenueBreakdown(viewType) {
    const breakdownData = {
        percentage: {
            rent: { amount: 'KES 1.82M', growth: '+8.2%', detail: '5 active' },
            amenities: { amount: 'KES 700K', growth: '+12.5%', detail: '6 types' },
            other: { amount: 'KES 280K', growth: '+5.1%', detail: 'Deposits, Fees' }
        },
        amount: {
            rent: { amount: 'KES 1.82M', growth: '+230K', detail: 'vs last month' },
            amenities: { amount: 'KES 700K', growth: '+78K', detail: 'vs last month' },
            other: { amount: 'KES 280K', growth: '+13K', detail: 'vs last month' }
        },
        trend: {
            rent: { amount: '68%', growth: '+3%', detail: '3-month avg' },
            amenities: { amount: '22%', growth: '-3%', detail: '3-month avg' },
            other: { amount: '10%', growth: '0%', detail: '3-month avg' }
        }
    };

    const data = breakdownData[viewType];

    // Update the breakdown cards with new data
    const cards = document.querySelectorAll('.revenue-category-card');
    const categories = ['rent', 'amenities', 'other'];

    cards.forEach((card, index) => {
        const category = categories[index];
        const categoryData = data[category];

        const amountElement = card.querySelector('.detail-value');
        const growthElement = card.querySelectorAll('.detail-value')[1];
        const detailElement = card.querySelectorAll('.detail-value')[2];

        if (amountElement) amountElement.textContent = categoryData.amount;
        if (growthElement) growthElement.textContent = categoryData.growth;
        if (detailElement) detailElement.textContent = categoryData.detail;
    });
}

// Interactive Functions
function refreshKPIs() {
    // Simulate refresh with loading animation
    const kpiCards = document.querySelectorAll('.kpi-value');
    kpiCards.forEach(card => {
        card.style.opacity = '0.5';
        setTimeout(() => {
            card.style.opacity = '1';
        }, 1000);
    });

    // Show success message
    showNotification('KPIs refreshed successfully!', 'success');
}

function exportKPIs() {
    // Simulate export functionality
    showNotification('Exporting KPI data...', 'info');
    setTimeout(() => {
        showNotification('KPI data exported to Excel!', 'success');
    }, 2000);
}

function showChart(chartType) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-secondary');
        btn.classList.remove('btn-outline-primary');
    });

    event.target.classList.add('active');
    event.target.classList.add('btn-outline-primary');
    event.target.classList.remove('btn-outline-secondary');

    showNotification(`Switched to ${chartType} view`, 'info');
}

function showActionPlan(propertyId) {
    const actionPlans = {
        1: 'Immediate Actions:\n• Reduce rent by 5-10%\n• Improve marketing\n• Address maintenance issues\n• Enhance amenities',
        2: 'Improvement Plan:\n• Tenant retention program\n• Preventive maintenance\n• Competitive pricing review\n• Community engagement',
        3: 'Optimization Strategy:\n• Premium pricing opportunity\n• Expand amenities\n• Referral programs\n• Property upgrades'
    };

    const plan = actionPlans[propertyId] || 'Custom action plan needed for this property.';
    showModal('Action Plan', plan);
}

function showPropertyDetails(propertyId) {
    const details = `
        <div class="property-details">
            <h6>Property Analytics Overview</h6>
            <div class="row">
                <div class="col-6">
                    <strong>Occupancy Trend:</strong><br>
                    <small class="text-success">↗ Improving (+5% this quarter)</small>
                </div>
                <div class="col-6">
                    <strong>Revenue Growth:</strong><br>
                    <small class="text-success">↗ +12% YoY</small>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-6">
                    <strong>Maintenance Score:</strong><br>
                    <small class="text-warning">⚠ Needs attention</small>
                </div>
                <div class="col-6">
                    <strong>Tenant Satisfaction:</strong><br>
                    <small class="text-success">★★★★☆ 4.2/5</small>
                </div>
            </div>
        </div>
    `;
    showModal('Property Details', details);
}

function showRecommendations(propertyId) {
    const recommendations = `
        <div class="recommendations">
            <h6>AI-Powered Recommendations</h6>
            <div class="recommendation-item mb-2">
                <span class="badge badge-success">High Impact</span>
                <strong>Implement preventive maintenance program</strong>
                <small class="d-block text-muted">Expected to reduce maintenance costs by 25%</small>
            </div>
            <div class="recommendation-item mb-2">
                <span class="badge badge-info">Medium Impact</span>
                <strong>Enhance tenant communication</strong>
                <small class="d-block text-muted">Could improve retention by 15%</small>
            </div>
            <div class="recommendation-item mb-2">
                <span class="badge badge-warning">Low Impact</span>
                <strong>Update property amenities</strong>
                <small class="d-block text-muted">May increase rent potential by 8%</small>
            </div>
        </div>
    `;
    showModal('Smart Recommendations', recommendations);
}

// Utility Functions
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show notification-toast" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;

    // Add to top of page
    const container = document.querySelector('.main-content .section');
    container.insertAdjacentHTML('afterbegin', notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        const toast = document.querySelector('.notification-toast');
        if (toast) toast.remove();
    }, 3000);
}

function showModal(title, content) {
    const modal = `
        <div class="modal fade" id="dynamicModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">Take Action</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('dynamicModal');
    if (existingModal) existingModal.remove();

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modal);
    $('#dynamicModal').modal('show');

    // Clean up when modal is hidden
    $('#dynamicModal').on('hidden.bs.modal', function () {
        this.remove();
    });
}

// Initialize tooltips and other interactive elements
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add hover effects to cards
    $('.card-statistic-1').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );

    // Animate counters on page load
    animateCounters();
});

function animateCounters() {
    $('.kpi-value, .card-statistic-1 .card-body').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text().replace(/[^\d]/g, '')) || 0;

        if (countTo > 0) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    const formatted = Math.floor(this.countNum).toLocaleString();
                    $this.text($this.text().replace(/[\d,]+/, formatted));
                },
                complete: function() {
                    const formatted = countTo.toLocaleString();
                    $this.text($this.text().replace(/[\d,]+/, formatted));
                }
            });
        }
    });
}
</script>

<style>
/* Enhanced Card Statistics */
.card-statistic-1 {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-statistic-1:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-statistic-1 .card-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 24px;
    color: white;
    position: absolute;
    right: 15px;
    top: 15px;
}

.card-statistic-1 .card-wrap {
    padding: 20px;
    padding-right: 85px;
}

.card-statistic-1 .card-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-statistic-1 .card-body {
    font-size: 24px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.card-statistic-1 .card-footer {
    font-size: 11px;
    color: #8e9aaf;
    margin-top: 5px;
}

/* KPI Cards */
.kpi-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin-right: 20px;
}

.bg-gradient-primary { background: linear-gradient(45deg, #007bff, #0056b3); }
.bg-gradient-success { background: linear-gradient(45deg, #28a745, #1e7e34); }
.bg-gradient-warning { background: linear-gradient(45deg, #ffc107, #e0a800); }
.bg-gradient-info { background: linear-gradient(45deg, #17a2b8, #138496); }

.kpi-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.kpi-value {
    font-size: 28px;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Performer Cards */
.performer-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.performer-card:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.performance-badge {
    font-size: 16px;
    font-weight: 700;
    padding: 8px 12px;
    border-radius: 20px;
    background: #28a745;
    color: white;
    margin-bottom: 5px;
    display: inline-block;
}

.badge-warning.performance-badge {
    background: #ffc107;
    color: #212529;
}

.performance-stars {
    font-size: 12px;
}

.performer-metrics small {
    margin-bottom: 2px;
}

/* Market Insights */
.market-insight h6 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e9ecef;
}

.insight-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f1f3f4;
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-label {
    font-size: 13px;
    color: #6c757d;
}

.insight-value {
    font-size: 12px;
    font-weight: 600;
}

/* Financial Health */
.financial-metric {
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
}

.financial-metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.metric-value {
    font-size: 16px;
    font-weight: 700;
}

.financial-score-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 16px;
}

/* Revenue Breakdown */
.breakdown-item {
    text-align: center;
}

.breakdown-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.breakdown-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
}

/* Geographic Performance */
.location-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.location-info h6 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
}

.location-score {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
}

/* Purple color for additional cards */
.bg-purple {
    background: linear-gradient(45deg, #6f42c1, #5a32a3) !important;
}

/* Badge enhancements */
.badge-lg {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
}

/* Enhanced Revenue Distribution Analytics */
.revenue-analytics-container {
    padding: 10px;
}

.chart-container {
    position: relative;
    height: 200px;
}

.revenue-breakdown-enhanced {
    margin-top: 20px;
}

.revenue-category-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
    height: 100%;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.revenue-category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.revenue-category-card:nth-child(1) { border-left-color: #007bff; }
.revenue-category-card:nth-child(2) { border-left-color: #28a745; }
.revenue-category-card:nth-child(3) { border-left-color: #ffc107; }

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.category-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    margin-right: 12px;
}

.category-info h6 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
    color: #495057;
}

.category-percentage {
    font-size: 20px;
    font-weight: 700;
    color: #495057;
}

.category-details {
    margin-bottom: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.detail-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    font-size: 12px;
    font-weight: 600;
}

.category-progress {
    margin-top: 10px;
}

/* Revenue Insights */
.revenue-insights {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
}

.insights-header h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
}

.insights-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.insight-card {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #e9ecef;
    transition: all 0.3s ease;
}

.insight-card:hover {
    border-left-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.insight-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
}

.insight-content {
    flex: 1;
}

.insight-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.insight-description {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.4;
}

/* Revenue Comparison */
.revenue-comparison {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
}

.comparison-header h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
}

.comparison-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.comparison-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
}

.comparison-label {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.comparison-bars {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.comparison-bar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar-label {
    font-size: 11px;
    color: #6c757d;
    width: 50px;
    flex-shrink: 0;
}

.bar-value {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    width: 35px;
    text-align: right;
    flex-shrink: 0;
}

.comparison-bar .progress {
    flex: 1;
    margin: 0 10px;
}

.comparison-summary {
    margin-top: 12px;
}

.alert-sm {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
}

/* Responsive adjustments for revenue analytics */
@media (max-width: 768px) {
    .revenue-category-card {
        margin-bottom: 15px;
    }

    .insights-grid {
        gap: 8px;
    }

    .insight-card {
        padding: 10px;
    }

    .comparison-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }

    .bar-label, .bar-value {
        width: auto;
        text-align: left;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kpi-card {
        flex-direction: column;
        text-align: center;
    }

    .kpi-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .card-statistic-1 .card-wrap {
        padding-right: 20px;
        text-align: center;
    }

    .card-statistic-1 .card-icon {
        position: relative;
        right: auto;
        top: auto;
        margin: 0 auto 15px;
    }
}

@media print {
    .btn, .card-header-action, .main-sidebar, .navbar, .alert {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}
</style>

{% include 'footer.html' %}
{% endblock %}
