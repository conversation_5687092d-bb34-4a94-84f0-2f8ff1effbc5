from django import template

register = template.Library()


@register.filter(name='attr')
def get_form_field(form, field_name_prefix):
    """
    Get a form field by concatenating prefix with next value.
    Usage: {{ form|attr:"amenity_"|add:amenity.id }}
    """
    return field_name_prefix


@register.filter(name='get_field')
def get_field(form, field_name):
    """
    Get a form field by name.
    Usage: {{ form|get_field:"amenity_1" }}
    """
    try:
        return form[field_name]
    except KeyError:
        return None

