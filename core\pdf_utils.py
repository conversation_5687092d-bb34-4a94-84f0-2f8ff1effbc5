"""PDF generation utilities for invoices and reports"""
from io import BytesIO
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from datetime import datetime


def generate_invoice_pdf(invoice):
    """
    Generate a professional PDF for an invoice
    
    Args:
        invoice: Invoice model instance
        
    Returns:
        BytesIO buffer containing the PDF
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter,
                           rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=18)
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        textColor=colors.HexColor('#2c3e50'),
        spaceAfter=30,
        alignment=TA_CENTER
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        textColor=colors.HexColor('#34495e'),
        spaceAfter=12,
    )
    
    normal_style = styles['Normal']
    
    # Header - Organization Info
    org_name = Paragraph(f"<b>{invoice.organization.name}</b>", title_style)
    elements.append(org_name)
    elements.append(Spacer(1, 12))
    
    # Invoice Title
    invoice_title = Paragraph(f"<b>INVOICE</b>", heading_style)
    elements.append(invoice_title)
    elements.append(Spacer(1, 12))
    
    # Invoice Details Table (2 columns)
    invoice_details_data = [
        ['Invoice Number:', invoice.number],
        ['Invoice Date:', invoice.issue_date.strftime('%B %d, %Y')],
        ['Due Date:', invoice.due_date.strftime('%B %d, %Y')],
        ['Invoice Type:', invoice.get_invoice_type_display()],
    ]
    
    tenant_details_data = [
        ['Bill To:', ''],
        ['Tenant:', f"{invoice.lease.tenant.first_name} {invoice.lease.tenant.last_name}"],
        ['Unit:', str(invoice.lease.unit)],
        ['Property:', str(invoice.lease.unit.property)],
    ]
    
    # Create side-by-side tables
    details_table = Table([invoice_details_data[i] + tenant_details_data[i] 
                           for i in range(max(len(invoice_details_data), len(tenant_details_data)))],
                          colWidths=[1.5*inch, 2*inch, 1.5*inch, 2*inch])
    
    details_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#7f8c8d')),
        ('TEXTCOLOR', (2, 0), (2, -1), colors.HexColor('#7f8c8d')),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))
    
    elements.append(details_table)
    elements.append(Spacer(1, 30))
    
    # Line Items Table
    line_items_heading = Paragraph("<b>Invoice Details</b>", heading_style)
    elements.append(line_items_heading)
    elements.append(Spacer(1, 12))
    
    # Get line items
    line_items = invoice.line_items.all()
    
    if line_items.exists():
        # Table header
        line_items_data = [['Description', 'Quantity', 'Unit Price', 'Amount']]
        
        # Add line items
        for item in line_items:
            line_items_data.append([
                item.description,
                str(item.quantity),
                f'KES {item.unit_price:,.2f}',
                f'KES {item.amount:,.2f}'
            ])
    else:
        # Fallback to invoice breakdown if no line items
        line_items_data = [['Description', 'Quantity', 'Unit Price', 'Amount']]
        
        if invoice.rent_amount > 0:
            line_items_data.append([
                f'Monthly Rent - {invoice.lease.unit}',
                '1',
                f'KES {invoice.rent_amount:,.2f}',
                f'KES {invoice.rent_amount:,.2f}'
            ])
        
        if invoice.amenities_amount > 0:
            line_items_data.append([
                'Amenities & Services',
                '1',
                f'KES {invoice.amenities_amount:,.2f}',
                f'KES {invoice.amenities_amount:,.2f}'
            ])
        
        if invoice.tax_amount > 0:
            line_items_data.append([
                'Taxes',
                '1',
                f'KES {invoice.tax_amount:,.2f}',
                f'KES {invoice.tax_amount:,.2f}'
            ])
        
        if invoice.other_charges > 0:
            line_items_data.append([
                'Other Charges',
                '1',
                f'KES {invoice.other_charges:,.2f}',
                f'KES {invoice.other_charges:,.2f}'
            ])
    
    # Create line items table
    line_items_table = Table(line_items_data, colWidths=[3.5*inch, 1*inch, 1.25*inch, 1.25*inch])
    
    line_items_table.setStyle(TableStyle([
        # Header row
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 11),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        
        # Data rows
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
        ('ALIGN', (0, 1), (0, -1), 'LEFT'),
        
        # Grid
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        
        # Alternating row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#ecf0f1')]),
    ]))
    
    elements.append(line_items_table)
    elements.append(Spacer(1, 20))
    
    # Summary Table (aligned to the right)
    summary_data = [
        ['Subtotal:', f'KES {(invoice.rent_amount + invoice.amenities_amount):,.2f}'],
        ['Taxes:', f'KES {invoice.tax_amount:,.2f}'],
        ['Other Charges:', f'KES {invoice.other_charges:,.2f}'],
        ['', ''],  # Spacer row
        ['Total Amount:', f'KES {invoice.amount_due:,.2f}'],
        ['Amount Paid:', f'KES {invoice.amount_paid:,.2f}'],
        ['Balance Due:', f'KES {invoice.balance:,.2f}'],
    ]
    
    summary_table = Table(summary_data, colWidths=[2*inch, 1.5*inch])
    summary_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, 2), 'Helvetica'),
        ('FONTNAME', (0, 4), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('TEXTCOLOR', (0, 4), (-1, -1), colors.HexColor('#2c3e50')),
        ('LINEABOVE', (0, 4), (-1, 4), 1, colors.HexColor('#3498db')),
        ('BACKGROUND', (0, 6), (-1, 6), colors.HexColor('#e74c3c') if invoice.balance > 0 else colors.HexColor('#27ae60')),
        ('TEXTCOLOR', (0, 6), (-1, 6), colors.whitesmoke),
    ]))
    
    # Create a table to align summary to the right
    wrapper_table = Table([[' ', summary_table]], colWidths=[3.5*inch, 3.5*inch])
    wrapper_table.setStyle(TableStyle([
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))
    
    elements.append(wrapper_table)
    elements.append(Spacer(1, 30))
    
    # Payment Status
    if invoice.is_paid:
        status_text = '<b><font color="green">✓ PAID IN FULL</font></b>'
    elif invoice.amount_paid > 0:
        status_text = f'<b><font color="orange">⚠ PARTIALLY PAID - Balance: KES {invoice.balance:,.2f}</font></b>'
    else:
        status_text = f'<b><font color="red">⚠ UNPAID - Due: {invoice.due_date.strftime("%B %d, %Y")}</font></b>'
    
    status_para = Paragraph(status_text, ParagraphStyle('Status', parent=normal_style, fontSize=12, alignment=TA_CENTER))
    elements.append(status_para)
    elements.append(Spacer(1, 20))
    
    # Notes
    if invoice.notes:
        notes_heading = Paragraph("<b>Notes:</b>", heading_style)
        elements.append(notes_heading)
        notes_para = Paragraph(invoice.notes, normal_style)
        elements.append(notes_para)
        elements.append(Spacer(1, 20))
    
    # Footer
    footer_text = f'<i>Please note that this is a system-generated invoice. Generated on {datetime.now().strftime("%B %d, %Y at %I:%M %p")}. For queries or concerns, please contact your property manager.</i>'
    # Footer Branding Powered by RentalX copyright Insquare Technologies
    footer_text += f'<br/><br/>Powered by RentalX &copy; {datetime.now().year} Insquare Technologies'

    footer_para = Paragraph(footer_text, ParagraphStyle('Footer', parent=normal_style, fontSize=8, textColor=colors.grey, alignment=TA_CENTER))
    elements.append(Spacer(1, 30))
    elements.append(footer_para)
    
    # Build PDF
    doc.build(elements)
    
    # Get the value of the BytesIO buffer and return it
    pdf = buffer.getvalue()
    buffer.close()
    return pdf

