# Daraja Sandbox Setup Guide - Complete Solution

## 🚨 Problem: "Invalid BusinessShortCode" Error

This error occurs because Daraja sandbox requires specific test credentials that you must obtain from the Safaricom Developer Portal.

---

## ✅ Complete Solution (10 Minutes)

### Step 1: Get Daraja Sandbox Credentials

#### A. Create Daraja Account

1. Go to: **https://developer.safaricom.co.ke/**
2. Click **"Sign Up"** (or Login if you have an account)
3. Fill in your details:
   - Email
   - Password
   - Phone number
   - Accept terms
4. Verify your email

#### B. Create a Sandbox App

1. After login, click **"My Apps"** in the top menu
2. Click **"Add a New App"**
3. Fill in app details:
   - **App Name:** RentalX Test (or any name)
   - **Description:** Testing M-Pesa integration
4. Click **"Create App"**

#### C. Get Your Credentials

After creating the app, you'll see:

```
Consumer Key: xxxxxxxxxxxxxxxxxxxxxxxxxxx
Consumer Secret: yyyyyyyyyyyyyyyyyyyyyyyy
```

**⚠️ IMPORTANT:** Copy these and save them securely!

---

### Step 2: Get Test Credentials

Daraja provides standard test credentials for sandbox:

#### **Business Short Code (Paybill):**
```
174379
```

#### **Passkey:**
```
bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
```

#### **Test Phone Numbers:**
```
************
************
************
```

These are **standard for all sandbox apps** - you don't need to request them.

---

### Step 3: Set Up ngrok (For Callback URL)

#### A. Download and Install ngrok

1. Go to: **https://ngrok.com/download**
2. Download for your OS (Windows/Mac/Linux)
3. Extract the file
4. (Optional) Sign up for free account to get permanent URL

#### B. Start ngrok

Open a **new terminal/command prompt** and run:

```bash
ngrok http 8000
```

You'll see:
```
Session Status                online
Account                       Your Name (Plan: Free)
Version                       3.x.x
Region                        United States (us)
Latency                       -
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:8000
```

**Copy the HTTPS URL:** `https://abc123.ngrok.io`

⚠️ **Keep this terminal open!** Closing it stops the tunnel.

---

### Step 4: Configure M-Pesa in RentalX

#### **Option A: Using Setup Script (Recommended)**

Run the interactive setup script:

```bash
python setup_mpesa_sandbox.py
```

Follow the prompts:
1. Enter your **Consumer Key** (from Daraja)
2. Enter your **Consumer Secret** (from Daraja)
3. Enter your **Callback URL** (from ngrok)
   - Example: `https://abc123.ngrok.io/payments/callback/`

The script will:
- ✅ Validate your credentials
- ✅ Set correct Business Short Code (174379)
- ✅ Set correct Passkey
- ✅ Test the configuration
- ✅ Save everything

---

#### **Option B: Manual Setup via Django Admin**

1. Go to: **http://127.0.0.1:8000/admin/**
2. Login with admin credentials
3. Click **"Mpesa configurations"**
4. Click **"Add Mpesa Configuration"** or edit existing
5. Fill in:

```
Organization: Test Organization
Consumer key: [Your Consumer Key from Daraja]
Consumer secret: [Your Consumer Secret from Daraja]
Business short code: 174379
Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
Callback url: https://abc123.ngrok.io/payments/callback/
Is sandbox: ✓ (checked)
Is active: ✓ (checked)
```

6. Click **"Save"**

---

#### **Option C: Using Django Shell**

```bash
python manage.py shell
```

Then paste (replace with YOUR values):

```python
from payments.models import MpesaConfiguration
from core.models import Organization

org = Organization.objects.get(slug='test')

config, created = MpesaConfiguration.objects.update_or_create(
    organization=org,
    defaults={
        'consumer_key': 'YOUR_CONSUMER_KEY_HERE',  # From Daraja
        'consumer_secret': 'YOUR_CONSUMER_SECRET_HERE',  # From Daraja
        'business_short_code': '174379',  # Standard sandbox
        'passkey': 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',  # Standard sandbox
        'callback_url': 'https://abc123.ngrok.io/payments/callback/',  # Your ngrok URL
        'is_sandbox': True,
        'is_active': True,
    }
)

print("✅ Configuration saved!")
print(f"Consumer Key: {config.consumer_key[:10]}...")
print(f"Callback URL: {config.callback_url}")
```

Press `Ctrl+D` to exit.

---

### Step 5: Verify Configuration

Run the diagnostic script:

```bash
python check_mpesa_config.py
```

You should see:
```
✅ Organization found: Test Organization
✅ M-Pesa configuration found
✅ All configuration fields are valid!
✅ Using HTTPS
✅ Not using localhost
✅ Access token generated successfully!
✅ All checks passed!
```

---

### Step 6: Test Payment

1. **Make sure both are running:**
   - Terminal 1: `python manage.py runserver`
   - Terminal 2: `ngrok http 8000`

2. **Go to an unpaid invoice:**
   - http://127.0.0.1:8000/test/billing/invoices/

3. **Click "Pay" or "Quick Pay"**

4. **Select M-Pesa**

5. **Enter test phone number:**
   ```
   ************
   ```

6. **Click "Pay Now"**

7. **You should see:**
   ```
   ✅ Payment request sent to ************. 
      Please enter your M-Pesa PIN to complete the payment.
   ```

---

## 🔍 Troubleshooting

### Error: "Invalid BusinessShortCode"

**Cause:** Using wrong short code

**Solution:** Use `174379` for sandbox (not your production paybill)

---

### Error: "Invalid Credentials"

**Cause:** Wrong Consumer Key or Secret

**Solution:** 
1. Go to Daraja portal
2. Check your app credentials
3. Copy them exactly (no extra spaces)
4. Update configuration

---

### Error: "Invalid Callback URL"

**Cause:** Callback URL is not HTTPS or not accessible

**Solution:**
1. Make sure ngrok is running
2. Use the HTTPS URL from ngrok (not HTTP)
3. Make sure URL ends with `/payments/callback/`

---

### Error: "Unauthorized"

**Cause:** Consumer Key/Secret are incorrect

**Solution:**
1. Double-check credentials from Daraja portal
2. Make sure you copied them correctly
3. No extra spaces or characters

---

### Errors Not Showing on Frontend

**Cause:** Messages not being displayed in template

**Solution:** Make sure your template has:

```html
{% if messages %}
    {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
    {% endfor %}
{% endif %}
```

This is already in your templates, so errors should show.

---

## 📋 Complete Checklist

Before testing payment:

- [ ] Created Daraja account
- [ ] Created sandbox app
- [ ] Got Consumer Key and Secret
- [ ] ngrok is running (`ngrok http 8000`)
- [ ] Django server is running (`python manage.py runserver`)
- [ ] M-Pesa configuration saved with:
  - [ ] Your Consumer Key
  - [ ] Your Consumer Secret
  - [ ] Business Short Code: `174379`
  - [ ] Correct Passkey
  - [ ] ngrok HTTPS URL as callback
  - [ ] `is_sandbox = True`
  - [ ] `is_active = True`
- [ ] Ran diagnostic script (`python check_mpesa_config.py`)
- [ ] All checks passed

---

## 🎯 Quick Reference

### Sandbox Standard Values:

```python
BUSINESS_SHORT_CODE = "174379"
PASSKEY = "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"
```

### Test Phone Numbers:

```
************
************
************
```

### Callback URL Format:

```
https://[your-ngrok-id].ngrok.io/payments/callback/
```

### Daraja Portal:

```
https://developer.safaricom.co.ke/
```

---

## 🚀 After Setup

Once configured correctly:

1. **Test with small amount:** KES 10
2. **Use test phone number:** ************
3. **Check Django logs** for detailed info
4. **Check ngrok dashboard:** http://127.0.0.1:4040

---

## 📱 Sandbox Behavior

**Important Notes:**

- ✅ Sandbox doesn't send actual STK push to phone
- ✅ Sandbox auto-completes after ~30 seconds
- ✅ You won't receive actual M-Pesa SMS
- ✅ It's for testing the integration only
- ✅ Use production credentials for real payments

---

## 🔄 Every Time You Restart

**Remember:**

1. **Restart ngrok** → Get new URL → Update callback URL
2. **Or:** Get ngrok free account for permanent URL
3. **Or:** Use other tunnel service (Cloudflare Tunnel, localtunnel)

---

## ✨ Summary

**The Issue:** Daraja doesn't provide credentials automatically - you must create an app.

**The Solution:**
1. Create Daraja account
2. Create sandbox app
3. Get Consumer Key/Secret
4. Use standard sandbox values (174379, passkey)
5. Set up ngrok for callback
6. Configure in RentalX
7. Test!

**That's it!** 🎉

---

**Last Updated:** October 3, 2025  
**Status:** Active  
**Version:** 1.0

