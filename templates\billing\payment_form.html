{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h1 class="h4 mb-0">{{ title }}</h1>
                                        <a href="{% org_url 'payment_list' %}" class="btn btn-secondary">Back to Payments</a>
                                            </div>
                                            </div>
                                            <div class="card">
                                                <div class="card-body">
                                                    <form method="post" id="payment-form">
                                                        {% csrf_token %}

                                                        <div class="mb-3">
                                                            <label for="{{ form.invoice.id_for_label }}" class="form-label">Invoice</label>
                                                            {{ form.invoice }}
                                                            {% if form.invoice.errors %}
                                                                <div class="text-danger">{{ form.invoice.errors }}</div>
                                                            {% endif %}
                                                        </div>

                                                        <!-- Invoice Details Section -->
                                                        <div id="invoice-details" class="card mb-3" style="display: none;">
                                                            <div class="card-header bg-light">
                                                                <h6 class="mb-0">Invoice Details</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row mb-2">
                                                                    <div class="col-md-6">
                                                                        <strong>Invoice Number:</strong> <span id="invoice-number">-</span>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <strong>Tenant:</strong> <span id="invoice-tenant">-</span>
                                                                    </div>
                                                                </div>
                                                                <div class="row mb-2">
                                                                    <div class="col-md-6">
                                                                        <strong>Unit:</strong> <span id="invoice-unit">-</span>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <strong>Due Date:</strong> <span id="invoice-due-date">-</span>
                                                                    </div>
                                                                </div>

                                                                <!-- Line Items Table -->
                                                                <div class="mt-3">
                                                                    <h6>Invoice Items</h6>
                                                                    <div class="table-responsive">
                                                                        <table class="table table-sm table-bordered">
                                                                            <thead class="table-light">
                                                                                <tr>
                                                                                    <th>Type</th>
                                                                                    <th>Description</th>
                                                                                    <th class="text-end">Qty</th>
                                                                                    <th class="text-end">Unit Price</th>
                                                                                    <th class="text-end">Amount</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody id="line-items-body">
                                                                                <!-- Line items will be populated here -->
                                                                            </tbody>
                                                                        </table>
                                                                    </div>
                                                                </div>

                                                                <!-- Payment Summary -->
                                                                <div class="row mt-3">
                                                                    <div class="col-md-6 offset-md-6">
                                                                        <table class="table table-sm">
                                                                            <tr>
                                                                                <td><strong>Total Amount:</strong></td>
                                                                                <td class="text-end">KES <span id="invoice-amount-due">0.00</span></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td><strong>Amount Paid:</strong></td>
                                                                                <td class="text-end">KES <span id="invoice-amount-paid">0.00</span></td>
                                                                            </tr>
                                                                            <tr class="table-active">
                                                                                <td><strong>Balance Due:</strong></td>
                                                                                <td class="text-end"><strong>KES <span id="invoice-balance">0.00</span></strong></td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="mb-3">
                                                                    <label for="{{ form.date.id_for_label }}" class="form-label">Payment Date</label>
                                                                    {{ form.date }}
                                                                    {% if form.date.errors %}
                                                                        <div class="text-danger">{{ form.date.errors }}</div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="mb-3">
                                                                    <label for="{{ form.amount.id_for_label }}" class="form-label">Amount</label>
                                                                    {{ form.amount }}
                                                                    {% if form.amount.errors %}
                                                                        <div class="text-danger">{{ form.amount.errors }}</div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div class="mb-3">
                                                                    <label for="{{ form.method.id_for_label }}" class="form-label">Payment Method</label>
                                                                    {{ form.method }}
                                                                    {% if form.method.errors %}
                                                                        <div class="text-danger">{{ form.method.errors }}</div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="mb-3">
                                                                    <label for="{{ form.reference.id_for_label }}" class="form-label">Reference</label>
                                                                    {{ form.reference }}
                                                                    {% if form.reference.errors %}
                                                                        <div class="text-danger">{{ form.reference.errors }}</div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="d-flex gap-2">
                                                            <button type="submit" class="btn btn-primary">Record Payment</button>
                                                            <a href="{% org_url 'payment_list' %}" class="btn btn-secondary">Cancel</a>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% include 'footer.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const invoiceSelect = document.getElementById('{{ form.invoice.id_for_label }}');
    const amountInput = document.getElementById('{{ form.amount.id_for_label }}');
    const invoiceDetailsDiv = document.getElementById('invoice-details');
    const orgSlug = '{{ org.slug }}';

    // Function to format currency
    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2);
    }

    // Function to fetch and display invoice details
    function loadInvoiceDetails(invoiceId) {
        if (!invoiceId) {
            invoiceDetailsDiv.style.display = 'none';
            return;
        }

        // Show loading state
        invoiceDetailsDiv.style.display = 'block';
        document.getElementById('invoice-number').textContent = 'Loading...';

        // Fetch invoice details
        fetch(`/${orgSlug}/api/invoices/${invoiceId}/details/`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to fetch invoice details');
                }
                return response.json();
            })
            .then(data => {
                // Update invoice details
                document.getElementById('invoice-number').textContent = data.number;
                document.getElementById('invoice-tenant').textContent = data.tenant;
                document.getElementById('invoice-unit').textContent = data.unit;
                document.getElementById('invoice-due-date').textContent = data.due_date;
                document.getElementById('invoice-amount-due').textContent = formatCurrency(data.amount_due);
                document.getElementById('invoice-amount-paid').textContent = formatCurrency(data.amount_paid);
                document.getElementById('invoice-balance').textContent = formatCurrency(data.balance);

                // Auto-populate amount with balance
                amountInput.value = formatCurrency(data.balance);

                // Populate line items
                const lineItemsBody = document.getElementById('line-items-body');
                lineItemsBody.innerHTML = '';

                if (data.line_items && data.line_items.length > 0) {
                    data.line_items.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.item_type}</td>
                            <td>${item.description}</td>
                            <td class="text-end">${formatCurrency(item.quantity)}</td>
                            <td class="text-end">KES ${formatCurrency(item.unit_price)}</td>
                            <td class="text-end">KES ${formatCurrency(item.amount)}</td>
                        `;
                        lineItemsBody.appendChild(row);
                    });
                } else {
                    lineItemsBody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No line items available</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error loading invoice details:', error);
                invoiceDetailsDiv.style.display = 'none';
                alert('Failed to load invoice details. Please try again.');
            });
    }

    // Listen for invoice selection changes
    invoiceSelect.addEventListener('change', function() {
        const selectedInvoiceId = this.value;
        loadInvoiceDetails(selectedInvoiceId);
    });

    // Load details if an invoice is already selected (e.g., on form validation error)
    if (invoiceSelect.value) {
        loadInvoiceDetails(invoiceSelect.value);
    }
});
</script>

{% endblock %}
