<!-- M-Pesa Payment Button Component -->
<!-- Include this in your invoice list or detail templates -->

{% load org_urls %}

<div class="payment-actions">
    {% if invoice.balance > 0 %}
        <!-- Payment Options Button -->
        <div class="btn-group" role="group">
            <a href="{% org_url 'invoice_payment_options' invoice_id=invoice.id %}" 
               class="btn btn-primary">
                <i class="fas fa-credit-card"></i> Pay Invoice
            </a>
            
            <!-- Quick M-Pesa Payment Button -->
            <button type="button" 
                    class="btn btn-success dropdown-toggle dropdown-toggle-split" 
                    data-toggle="dropdown" 
                    aria-haspopup="true" 
                    aria-expanded="false">
                <span class="sr-only">Toggle Dropdown</span>
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" 
                   href="{% org_url 'initiate_payment' invoice_id=invoice.id %}">
                    <i class="fas fa-mobile-alt text-success"></i> M-Pesa Payment
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#" onclick="showQuickPayModal({{ invoice.id }}, '{{ invoice.number }}', {{ invoice.balance }})">
                    <i class="fas fa-bolt text-warning"></i> Quick Pay
                </a>
            </div>
        </div>
        
        <!-- Payment Status Badge -->
        {% if invoice.amount_paid > 0 %}
            <span class="badge badge-warning ml-2">
                Partially Paid (KES {{ invoice.amount_paid|floatformat:2 }})
            </span>
        {% endif %}
    {% else %}
        <span class="badge badge-success">
            <i class="fas fa-check"></i> Fully Paid
        </span>
    {% endif %}
</div>

<!-- Quick Pay Modal (include once per page) -->
<div class="modal fade" id="quickPayModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-mobile-alt text-success"></i> Quick M-Pesa Payment
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="quickPayForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Invoice:</strong> <span id="modal-invoice-number"></span><br>
                        <strong>Amount:</strong> KES <span id="modal-amount"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="modal-phone">M-Pesa Phone Number</label>
                        <input type="tel" 
                               class="form-control" 
                               id="modal-phone" 
                               name="phone_number"
                               placeholder="0712345678"
                               pattern="^(\+?254|0)[17]\d{8}$"
                               required>
                        <small class="form-text text-muted">Enter your M-Pesa registered phone number</small>
                    </div>
                    
                    <input type="hidden" id="modal-invoice-id" name="invoice_id">
                    <input type="hidden" id="modal-amount-value" name="amount">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-credit-card"></i> Pay Now
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showQuickPayModal(invoiceId, invoiceNumber, amount) {
    document.getElementById('modal-invoice-id').value = invoiceId;
    document.getElementById('modal-invoice-number').textContent = invoiceNumber;
    document.getElementById('modal-amount').textContent = parseFloat(amount).toFixed(2);
    document.getElementById('modal-amount-value').value = amount;
    
    // Set form action
    const form = document.getElementById('quickPayForm');
    form.action = `/${orgSlug}/payments/invoice/${invoiceId}/pay/`;
    
    $('#quickPayModal').modal('show');
}

// Auto-format phone number input
document.getElementById('modal-phone')?.addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    
    if (value.startsWith('254')) {
        // Already in international format
        e.target.value = value;
    } else if (value.startsWith('0')) {
        // Local format, keep as is for now
        e.target.value = '0' + value.slice(1);
    } else if (value.length > 0) {
        // Add leading 0 for local format
        e.target.value = '0' + value;
    }
});
</script>

<style>
.payment-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-group .btn {
    border-radius: 0.25rem;
}

.btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .dropdown-toggle-split {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

.badge {
    font-size: 0.8rem;
}
</style>
