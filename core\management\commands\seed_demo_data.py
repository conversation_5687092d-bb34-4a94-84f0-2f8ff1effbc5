from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from core.models import Organization, Membership
from rentals.models import Property, Unit, Tenant, Lease, Invoice, Payment


class Command(BaseCommand):
    help = 'Create demo data for testing RentalX'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org-slug',
            type=str,
            default='demo-org',
            help='Organization slug to create (default: demo-org)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data for the organization first',
        )

    def handle(self, *args, **options):
        org_slug = options['org_slug']
        
        # Create or get organization
        org, created = Organization.objects.get_or_create(
            slug=org_slug,
            defaults={
                'name': 'Demo Property Management',
            }
        )
        
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            # Clear in reverse dependency order
            Payment.objects.filter(organization=org).delete()
            Invoice.objects.filter(organization=org).delete()
            Lease.objects.filter(organization=org).delete()
            Tenant.objects.filter(organization=org).delete()
            Unit.objects.filter(organization=org).delete()
            Property.objects.filter(organization=org).delete()
        
        if created:
            self.stdout.write(f'Created organization: {org.name}')
        else:
            self.stdout.write(f'Using existing organization: {org.name}')
        
        # Create demo properties
        properties_data = [
            {'name': 'Sunset Apartments', 'address': '123 Sunset Blvd, Los Angeles, CA 90028'},
            {'name': 'Downtown Lofts', 'address': '456 Main St, Los Angeles, CA 90013'},
            {'name': 'Garden View Complex', 'address': '789 Garden Ave, Beverly Hills, CA 90210'},
        ]
        
        properties = []
        for prop_data in properties_data:
            prop, created = Property.objects.get_or_create(
                organization=org,
                name=prop_data['name'],
                defaults={'address': prop_data['address']}
            )
            properties.append(prop)
            if created:
                self.stdout.write(f'Created property: {prop.name}')
        
        # Create demo units
        units_data = [
            # Sunset Apartments
            {'property': properties[0], 'code': 'A101', 'bedrooms': 1, 'bathrooms': 1},
            {'property': properties[0], 'code': 'A102', 'bedrooms': 1, 'bathrooms': 1},
            {'property': properties[0], 'code': 'B201', 'bedrooms': 2, 'bathrooms': 2},
            {'property': properties[0], 'code': 'B202', 'bedrooms': 2, 'bathrooms': 2},
            # Downtown Lofts
            {'property': properties[1], 'code': 'L301', 'bedrooms': 2, 'bathrooms': 1},
            {'property': properties[1], 'code': 'L302', 'bedrooms': 3, 'bathrooms': 2},
            # Garden View Complex
            {'property': properties[2], 'code': 'GV01', 'bedrooms': 3, 'bathrooms': 3},
            {'property': properties[2], 'code': 'GV02', 'bedrooms': 4, 'bathrooms': 3},
        ]
        
        units = []
        for unit_data in units_data:
            unit, created = Unit.objects.get_or_create(
                organization=org,
                property=unit_data['property'],
                code=unit_data['code'],
                defaults={
                    'bedrooms': unit_data['bedrooms'],
                    'bathrooms': unit_data['bathrooms'],
                    'is_active': True
                }
            )
            units.append(unit)
            if created:
                self.stdout.write(f'Created unit: {unit.property.name} - {unit.code}')
        
        # Create demo tenants
        tenants_data = [
            {'first_name': 'John', 'last_name': 'Smith', 'email': '<EMAIL>', 'phone': '555-0101'},
            {'first_name': 'Sarah', 'last_name': 'Johnson', 'email': '<EMAIL>', 'phone': '555-0102'},
            {'first_name': 'Michael', 'last_name': 'Brown', 'email': '<EMAIL>', 'phone': '555-0103'},
            {'first_name': 'Emily', 'last_name': 'Davis', 'email': '<EMAIL>', 'phone': '555-0104'},
            {'first_name': 'David', 'last_name': 'Wilson', 'email': '<EMAIL>', 'phone': '555-0105'},
        ]
        
        tenants = []
        for tenant_data in tenants_data:
            tenant, created = Tenant.objects.get_or_create(
                organization=org,
                email=tenant_data['email'],
                defaults=tenant_data
            )
            tenants.append(tenant)
            if created:
                self.stdout.write(f'Created tenant: {tenant.first_name} {tenant.last_name}')
        
        # Create demo leases
        today = timezone.now().date()
        leases_data = [
            {'unit': units[0], 'tenant': tenants[0], 'rent': Decimal('1200.00'), 'deposit': Decimal('1200.00')},
            {'unit': units[2], 'tenant': tenants[1], 'rent': Decimal('1800.00'), 'deposit': Decimal('1800.00')},
            {'unit': units[4], 'tenant': tenants[2], 'rent': Decimal('2200.00'), 'deposit': Decimal('2200.00')},
            {'unit': units[6], 'tenant': tenants[3], 'rent': Decimal('3500.00'), 'deposit': Decimal('3500.00')},
        ]
        
        leases = []
        for i, lease_data in enumerate(leases_data):
            start_date = today - timedelta(days=30 * (i + 1))  # Stagger start dates
            lease, created = Lease.objects.get_or_create(
                organization=org,
                unit=lease_data['unit'],
                tenant=lease_data['tenant'],
                start_date=start_date,
                defaults={
                    'rent_amount': lease_data['rent'],
                    'deposit_amount': lease_data['deposit'],
                    'billing_day': 1,
                    'status': 'ACTIVE'
                }
            )
            leases.append(lease)
            if created:
                self.stdout.write(f'Created lease: {lease.tenant} - {lease.unit}')
        
        # Create some demo invoices
        for i, lease in enumerate(leases[:3]):  # Create invoices for first 3 leases
            for month_offset in range(3):  # 3 months of invoices
                invoice_date = today - timedelta(days=30 * (2 - month_offset))
                invoice_number = f"INV-{invoice_date.strftime('%Y%m')}-{lease.id:04d}"
                
                invoice, created = Invoice.objects.get_or_create(
                    organization=org,
                    number=invoice_number,
                    defaults={
                        'lease': lease,
                        'issue_date': invoice_date,
                        'due_date': invoice_date + timedelta(days=30),
                        'amount_due': lease.rent_amount,
                        'amount_paid': lease.rent_amount if month_offset > 0 else Decimal('0.00'),
                        'is_paid': month_offset > 0
                    }
                )
                
                if created:
                    self.stdout.write(f'Created invoice: {invoice.number}')
                    
                    # Create payment for paid invoices
                    if invoice.is_paid:
                        payment, payment_created = Payment.objects.get_or_create(
                            organization=org,
                            invoice=invoice,
                            reference=f'PAY-{invoice.number}',
                            defaults={
                                'date': invoice.due_date - timedelta(days=5),
                                'method': 'Bank Transfer',
                                'amount': invoice.amount_due
                            }
                        )
                        if payment_created:
                            self.stdout.write(f'Created payment: {payment.reference}')
        
        # Create membership for superusers
        superusers = User.objects.filter(is_superuser=True)
        for superuser in superusers:
            membership, created = Membership.objects.get_or_create(
                user=superuser,
                organization=org,
                defaults={'role': 'ADMIN'}
            )
            if created:
                self.stdout.write(f'Added {superuser.username} as ADMIN to {org.name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'\nDemo data created successfully for organization: {org.name}\n'
                f'Access at: http://127.0.0.1:8000/{org.slug}/\n'
                f'Superusers have been automatically added as admins.'
            )
        )
