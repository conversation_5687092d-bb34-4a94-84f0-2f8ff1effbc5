# RentalX Billing System - Complete Feature Documentation

## Overview
This document describes the comprehensive billing system implemented for RentalX, including auto-captured amenities, bulk invoice generation, PDF export, recurring automation, and payment reminders.

---

## 1. Auto-Captured Billing Amenities ✅

### Description
When creating invoices, all mandatory amenities are automatically pre-selected and included in the invoice. The system supports both mandatory and optional amenities with custom pricing per unit.

### Features
- **Auto-selection of mandatory amenities** - Marked with "Mandatory" badge
- **Optional amenities** - Can be manually selected/deselected
- **Custom pricing support** - Unit-specific pricing overrides default amounts
- **Real-time total calculation** - Updates as items are selected
- **Detailed line items** - Each charge is a separate line item

### Usage
1. Navigate to: `/{org_slug}/invoices/add/`
2. Select a lease (shows tenant, unit, rent)
3. Mandatory amenities are auto-checked
4. Adjust amounts if needed
5. Select optional amenities
6. Add taxes if applicable
7. See real-time total
8. Click "Create Invoice"

### Technical Implementation
- **Form**: `core/forms.py` - `InvoiceForm` with dynamic amenity fields
- **View**: `core/crud_views.py` - `invoice_create()` with line item generation
- **Template**: `templates/billing/invoice_form.html` with JavaScript calculator
- **Models**: `AmenityType`, `UnitAmenity`, `InvoiceLineItem`

---

## 2. Bulk Invoice Generation ✅

### Description
Generate invoices for all active leases at once with a single click. Perfect for monthly billing cycles.

### Features
- **Batch processing** - Generate invoices for all active leases
- **Configurable settings** - Set invoice type, dates, and billing items
- **Preview before generation** - See all leases that will be invoiced
- **Auto-amenity inclusion** - Mandatory amenities automatically included
- **Progress tracking** - Shows created, skipped, and error counts

### Usage
1. Navigate to: `/{org_slug}/invoices/bulk-generate/`
2. Configure invoice settings (type, dates)
3. Select billing items to include
4. Review lease list
5. Click "Generate Invoices"

### Technical Implementation
- **View**: `core/crud_views.py` - `bulk_invoice_generate()`
- **Template**: `templates/billing/bulk_invoice_form.html`
- **URL**: `/invoices/bulk-generate/`

### Example
```
Scenario: Monthly billing for 10 active leases
- Click "Bulk Generate" button
- Set issue date: Oct 1, 2025
- Set due date: Oct 7, 2025
- Mandatory amenities auto-selected
- Click "Generate Invoices"
- Result: 10 invoices created in seconds!
```

---

## 3. Invoice PDF Export ✅

### Description
Generate professional PDF invoices with detailed line items, payment status, and organization branding.

### Features
- **Professional formatting** - Clean, organized layout
- **Detailed line items** - Shows all charges with quantities and prices
- **Payment status** - Visual indicators for paid/unpaid/partial
- **Summary section** - Subtotal, taxes, total, paid, balance
- **Auto-generated** - One-click download
- **Printable** - Ready for printing or emailing

### Usage
1. From invoice list: Click PDF icon next to invoice
2. From invoice detail: Click "Download PDF" button
3. PDF downloads automatically

### Technical Implementation
- **Utility**: `core/pdf_utils.py` - `generate_invoice_pdf()`
- **View**: `core/crud_views.py` - `invoice_pdf_download()`
- **Library**: ReportLab 4.4.4
- **URL**: `/invoices/<pk>/pdf/`

### PDF Contents
- Organization name and branding
- Invoice number, dates, type
- Tenant and unit information
- Detailed line items table
- Payment summary with totals
- Payment status indicator
- Notes (if any)
- Generation timestamp

---

## 4. Recurring Invoice Automation ✅

### Description
Automated monthly invoice generation using Django management commands. Can be scheduled with cron jobs or Windows Task Scheduler.

### Features
- **Automated generation** - Run on schedule (daily/weekly/monthly)
- **Organization filtering** - Process specific or all organizations
- **Dry-run mode** - Test without creating invoices
- **Duplicate prevention** - Skips if invoice already exists for the month
- **Mandatory amenities** - Automatically included
- **Detailed logging** - Shows created, skipped, and errors
- **Configurable due dates** - Set days until due

### Usage

#### Manual Execution
```bash
# Generate for all organizations
python manage.py generate_recurring_invoices

# Generate for specific organization
python manage.py generate_recurring_invoices --organization=test

# Dry run (test mode)
python manage.py generate_recurring_invoices --dry-run

# Custom due date (14 days)
python manage.py generate_recurring_invoices --due-days=14
```

#### Scheduled Execution

**Windows Task Scheduler:**
1. Open Task Scheduler
2. Create Basic Task
3. Name: "RentalX Monthly Invoices"
4. Trigger: Monthly, 1st day, 12:00 AM
5. Action: Start a program
6. Program: `D:\Web Dev\RentalX\venv\Scripts\python.exe`
7. Arguments: `manage.py generate_recurring_invoices`
8. Start in: `D:\Web Dev\RentalX`

**Linux Cron:**
```bash
# Edit crontab
crontab -e

# Add line (runs 1st of month at midnight)
0 0 1 * * cd /path/to/RentalX && /path/to/venv/bin/python manage.py generate_recurring_invoices
```

### Technical Implementation
- **Command**: `core/management/commands/generate_recurring_invoices.py`
- **Model**: `core/models.py` - `RecurringInvoiceSchedule`
- **Admin**: `core/admin.py` - Schedule management

### Output Example
```
======================================================================
RECURRING INVOICE GENERATION
======================================================================

Processing organization: Test (test)
----------------------------------------------------------------------
  Found 10 active lease(s)
  Found 3 mandatory amenity/amenities
  ✓ Created: INV-2025-10-0001 - John Doe (Unit A1) - KES 30,000.00 (4 items)
  ✓ Created: INV-2025-10-0002 - Jane Smith (Unit B2) - KES 28,500.00 (4 items)
  ⊘ Skipped: Bob Johnson (Unit C3) - Invoice already exists (INV-2025-10-0003)
  ...

======================================================================
SUMMARY
======================================================================
Total invoices created: 9
Total leases skipped: 1
Total errors: 0

✓ Invoice generation complete!
```

---

## 5. Payment Reminders System ✅

### Description
Automated email reminders for overdue invoices. Sends personalized emails to tenants with outstanding balances.

### Features
- **Automated reminders** - Run on schedule
- **Overdue detection** - Finds invoices past due date
- **Email notifications** - Sends to tenant email
- **Personalized messages** - Includes invoice details
- **Configurable thresholds** - Set minimum days overdue
- **Dry-run mode** - Test without sending emails
- **Error handling** - Skips tenants without email

### Usage

#### Manual Execution
```bash
# Send reminders for all overdue invoices
python manage.py send_payment_reminders

# Send for specific organization
python manage.py send_payment_reminders --organization=test

# Dry run (test mode)
python manage.py send_payment_reminders --dry-run

# Only invoices 3+ days overdue
python manage.py send_payment_reminders --days-overdue=3
```

#### Scheduled Execution

**Windows Task Scheduler:**
1. Create Daily Task
2. Name: "RentalX Payment Reminders"
3. Trigger: Daily, 9:00 AM
4. Action: Start a program
5. Program: `D:\Web Dev\RentalX\venv\Scripts\python.exe`
6. Arguments: `manage.py send_payment_reminders`
7. Start in: `D:\Web Dev\RentalX`

**Linux Cron:**
```bash
# Runs daily at 9 AM
0 9 * * * cd /path/to/RentalX && /path/to/venv/bin/python manage.py send_payment_reminders
```

### Email Template
```
Subject: Payment Reminder: Invoice INV-2025-10-0001 is Overdue

Dear John Doe,

This is a friendly reminder that your invoice is now overdue.

Invoice Details:
- Invoice Number: INV-2025-10-0001
- Unit: Unit A1
- Issue Date: October 01, 2025
- Due Date: October 07, 2025
- Days Overdue: 5 days

Amount Due: KES 30,000.00
Amount Paid: KES 0.00
Balance: KES 30,000.00

Please make payment at your earliest convenience to avoid late fees.

Payment Methods:
- M-Pesa: [Payment instructions]
- Bank Transfer: [Bank details]
- Cash: Visit our office

If you have already made payment, please disregard this message.

Thank you for your prompt attention to this matter.

Best regards,
Test Organization
```

### Technical Implementation
- **Command**: `core/management/commands/send_payment_reminders.py`
- **Email**: Django's `send_mail()` function
- **Configuration**: `settings.py` - Email backend settings

### Output Example
```
======================================================================
PAYMENT REMINDER SYSTEM
======================================================================

Processing organization: Test (test)
----------------------------------------------------------------------
  Found 5 overdue invoice(s)
  ✓ Sent: INV-2025-10-0001 - John Doe (<EMAIL>) - KES 30,000.00 (5 days overdue)
  ✓ Sent: INV-2025-10-0002 - Jane Smith (<EMAIL>) - KES 28,500.00 (3 days overdue)
  ⊘ Skipped: INV-2025-10-0003 - Bob Johnson (No email address)
  ...

======================================================================
SUMMARY
======================================================================
Total reminders sent: 4
Total invoices skipped: 1
Total errors: 0

✓ Payment reminders sent!
```

---

## Configuration

### Email Settings (settings.py)
```python
# For development (console backend)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# For production (SMTP)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
DEFAULT_FROM_EMAIL = 'RentalX <<EMAIL>>'
```

---

## Dependencies

```
Django==5.2.5
reportlab==4.4.4
pillow==11.3.0
```

---

## Summary

All four advanced billing features have been successfully implemented:

1. ✅ **Auto-Captured Billing Amenities** - Mandatory items automatically included
2. ✅ **Bulk Invoice Generation** - Generate invoices for all leases at once
3. ✅ **Invoice PDF Export** - Professional PDF invoices with one click
4. ✅ **Recurring Invoice Automation** - Scheduled automatic invoice generation
5. ✅ **Payment Reminders System** - Automated overdue payment notifications

The system is production-ready and can be scheduled for automated operation!

