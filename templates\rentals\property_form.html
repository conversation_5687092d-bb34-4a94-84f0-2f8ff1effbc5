{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'property_list' %}" class="btn btn-secondary">Back to Properties</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.name.id_for_label }}" class="form-label">Property Name</label>
                {{ form.name }}
                {% if form.name.errors %}
                    <div class="text-danger">{{ form.name.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                {{ form.address }}
                {% if form.address.errors %}
                    <div class="text-danger">{{ form.address.errors }}</div>
                {% endif %}
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Save Property</button>
                <a href="{% org_url 'property_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
</div>
</div>
</div>
</div>
</div>
{% include 'footer.html' %}

{% endblock %}
