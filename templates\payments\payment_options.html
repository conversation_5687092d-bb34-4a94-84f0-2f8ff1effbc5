{% extends 'base.html' %}
{% load org_urls %}

{% block title %}Payment Options - Invoice {{ invoice.number }}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card">
                        <div class="card-header">
                            <h4>Payment Options for Invoice {{ invoice.number }}</h4>
                        </div>
                        <div class="card-body">
                            <!-- Invoice Summary -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6>Invoice Details</h6>
                                                    <p><strong>Invoice:</strong> {{ invoice.number }}</p>
                                                    <p><strong>Property:</strong> {{ invoice.lease.unit.property.name }}</p>
                                                    <p><strong>Unit:</strong> {{ invoice.lease.unit.code }}</p>
                                                    <p><strong>Tenant:</strong> {{ invoice.lease.tenant.first_name }} {{ invoice.lease.tenant.last_name }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6>Payment Summary</h6>
                                                    <p><strong>Due Date:</strong> {{ invoice.due_date|date:"M d, Y" }}</p>
                                                    <p><strong>Total Amount:</strong> KES {{ invoice.amount_due|floatformat:2 }}</p>
                                                    <p><strong>Amount Paid:</strong> KES {{ invoice.amount_paid|floatformat:2 }}</p>
                                                    <p><strong>Outstanding Balance:</strong> 
                                                        <span class="text-danger font-weight-bold">KES {{ invoice.balance|floatformat:2 }}</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Payment Methods -->
                            <div class="row">
                                {% if mpesa_configured %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-success h-100">
                                        <div class="card-header bg-success text-white text-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-mobile-alt"></i> M-Pesa Payment
                                            </h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <i class="fas fa-mobile-alt fa-3x text-success"></i>
                                            </div>
                                            <h6>Pay with M-Pesa</h6>
                                            <p class="text-muted">
                                                Pay instantly using your M-Pesa mobile money account. 
                                                You'll receive an STK Push notification on your phone.
                                            </p>
                                            <ul class="list-unstyled text-left">
                                                <li><i class="fas fa-check text-success"></i> Instant payment</li>
                                                <li><i class="fas fa-check text-success"></i> Secure transaction</li>
                                                <li><i class="fas fa-check text-success"></i> SMS confirmation</li>
                                                <li><i class="fas fa-check text-success"></i> 24/7 availability</li>
                                            </ul>
                                            <a href="{% org_url 'initiate_payment' invoice_id=invoice.id %}" 
                                               class="btn btn-success btn-lg btn-block">
                                                <i class="fas fa-credit-card"></i> Pay with M-Pesa
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <div class="col-md-6 mb-3">
                                    <div class="card border-primary h-100">
                                        <div class="card-header bg-primary text-white text-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-university"></i> Bank Transfer
                                            </h5>
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <i class="fas fa-university fa-3x text-primary"></i>
                                            </div>
                                            <h6>Bank Transfer</h6>
                                            <p class="text-muted">
                                                Transfer funds directly from your bank account to our business account.
                                            </p>
                                            <div class="text-left">
                                                <p><strong>Bank:</strong> Example Bank</p>
                                                <p><strong>Account Name:</strong> {{ org.name }}</p>
                                                <p><strong>Account Number:</strong> **********</p>
                                                <p><strong>Reference:</strong> {{ invoice.number }}</p>
                                            </div>
                                            <button class="btn btn-primary btn-lg btn-block" 
                                                    onclick="copyBankDetails()">
                                                <i class="fas fa-copy"></i> Copy Bank Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Additional Payment Methods -->
                            <div class="row mt-3">
                                <div class="col-md-4 mb-3">
                                    <div class="card border-info h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-check fa-2x text-info mb-2"></i>
                                            <h6>Cheque Payment</h6>
                                            <p class="text-muted small">
                                                Write a cheque payable to "{{ org.name }}" with reference "{{ invoice.number }}"
                                            </p>
                                            <button class="btn btn-info btn-sm" onclick="showChequeDetails()">
                                                View Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="card border-warning h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-bill-wave fa-2x text-warning mb-2"></i>
                                            <h6>Cash Payment</h6>
                                            <p class="text-muted small">
                                                Visit our office to make a cash payment. Office hours: Mon-Fri 8AM-5PM
                                            </p>
                                            <button class="btn btn-warning btn-sm" onclick="showOfficeDetails()">
                                                Office Location
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <div class="card border-secondary h-100">
                                        <div class="card-body text-center">
                                            <i class="fas fa-credit-card fa-2x text-secondary mb-2"></i>
                                            <h6>Card Payment</h6>
                                            <p class="text-muted small">
                                                Pay using your debit or credit card (coming soon)
                                            </p>
                                            <button class="btn btn-secondary btn-sm" disabled>
                                                Coming Soon
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Back Button -->
                            <div class="text-center mt-4">
                                <a href="{% org_url 'invoice_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Invoices
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyBankDetails() {
    const bankDetails = `Bank: Example Bank
Account Name: {{ org.name }}
Account Number: **********
Reference: {{ invoice.number }}
Amount: KES {{ invoice.balance|floatformat:2 }}`;
    
    navigator.clipboard.writeText(bankDetails).then(function() {
        alert('Bank details copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = bankDetails;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Bank details copied to clipboard!');
    });
}

function showChequeDetails() {
    alert(`Cheque Payment Details:
    
Payable to: {{ org.name }}
Amount: KES {{ invoice.balance|floatformat:2 }}
Reference: {{ invoice.number }}

Please write the invoice number on the back of the cheque and deliver to our office.`);
}

function showOfficeDetails() {
    alert(`Office Location:
    
Address: [Your Office Address]
Phone: [Your Phone Number]
Email: [Your Email]
Office Hours: Monday - Friday, 8:00 AM - 5:00 PM

Please bring this invoice number: {{ invoice.number }}`);
}
</script>

<style>
.fa-2x {
    font-size: 2rem;
}

.fa-3x {
    font-size: 3rem;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.list-unstyled li {
    padding: 2px 0;
}

.font-weight-bold {
    font-weight: 700 !important;
}
</style>
{% endblock %}
