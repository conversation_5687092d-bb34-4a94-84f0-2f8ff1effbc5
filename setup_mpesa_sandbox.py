"""
M-Pesa Sandbox Setup Script
This script sets up the correct M-Pesa sandbox configuration with valid test credentials
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentalx.settings')
django.setup()

from payments.models import MpesaConfiguration
from core.models import Organization

def setup_mpesa_sandbox():
    """
    Set up M-Pesa sandbox configuration with correct test credentials.
    
    These are the official Safaricom Daraja sandbox test credentials.
    Source: https://developer.safaricom.co.ke/test_credentials
    """
    
    print("=" * 70)
    print("M-PESA SANDBOX CONFIGURATION SETUP")
    print("=" * 70)
    print()
    
    # Get organization
    try:
        org = Organization.objects.get(slug='test')
        print(f"✅ Found organization: {org.name}")
    except Organization.DoesNotExist:
        print("❌ Organization 'test' not found!")
        print("\nAvailable organizations:")
        for org in Organization.objects.all():
            print(f"  - {org.slug}: {org.name}")
        
        org_slug = input("\nEnter organization slug to use: ").strip()
        try:
            org = Organization.objects.get(slug=org_slug)
            print(f"✅ Using organization: {org.name}")
        except Organization.DoesNotExist:
            print(f"❌ Organization '{org_slug}' not found!")
            return
    
    print()
    print("=" * 70)
    print("IMPORTANT: DARAJA SANDBOX CREDENTIALS")
    print("=" * 70)
    print()
    print("Safaricom provides test credentials for sandbox testing.")
    print("You need to get these from: https://developer.safaricom.co.ke/")
    print()
    print("Steps to get credentials:")
    print("1. Go to https://developer.safaricom.co.ke/")
    print("2. Sign up / Login")
    print("3. Create a new app (Sandbox)")
    print("4. You'll get:")
    print("   - Consumer Key")
    print("   - Consumer Secret")
    print()
    print("For the sandbox, use these standard values:")
    print("   - Business Short Code: 174379")
    print("   - Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919")
    print()
    print("=" * 70)
    print()
    
    # Get user input
    print("Please enter your Daraja sandbox credentials:")
    print()
    
    consumer_key = input("Consumer Key: ").strip()
    if not consumer_key:
        print("❌ Consumer Key is required!")
        return
    
    consumer_secret = input("Consumer Secret: ").strip()
    if not consumer_secret:
        print("❌ Consumer Secret is required!")
        return
    
    print()
    print("For callback URL, you need a public HTTPS URL.")
    print("For local testing, use ngrok:")
    print("  1. Run: ngrok http 8000")
    print("  2. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)")
    print("  3. Paste it below")
    print()
    
    callback_url = input("Callback URL (e.g., https://abc123.ngrok.io/payments/callback/): ").strip()
    if not callback_url:
        print("⚠️  No callback URL provided. Using placeholder.")
        callback_url = "https://your-ngrok-url.ngrok.io/payments/callback/"
    
    # Ensure callback URL ends with /payments/callback/
    if not callback_url.endswith('/payments/callback/'):
        if callback_url.endswith('/'):
            callback_url += 'payments/callback/'
        else:
            callback_url += '/payments/callback/'
    
    # Standard sandbox values
    business_short_code = "174379"
    passkey = "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919"
    
    print()
    print("=" * 70)
    print("CONFIGURATION SUMMARY")
    print("=" * 70)
    print(f"Organization: {org.name}")
    print(f"Consumer Key: {consumer_key[:10]}...{consumer_key[-5:]}")
    print(f"Consumer Secret: {consumer_secret[:10]}...{consumer_secret[-5:]}")
    print(f"Business Short Code: {business_short_code}")
    print(f"Passkey: {passkey[:20]}...{passkey[-10:]}")
    print(f"Callback URL: {callback_url}")
    print(f"Is Sandbox: True")
    print(f"Is Active: True")
    print("=" * 70)
    print()
    
    confirm = input("Save this configuration? (yes/no): ").strip().lower()
    if confirm not in ['yes', 'y']:
        print("❌ Configuration not saved.")
        return
    
    # Create or update configuration
    config, created = MpesaConfiguration.objects.update_or_create(
        organization=org,
        defaults={
            'consumer_key': consumer_key,
            'consumer_secret': consumer_secret,
            'business_short_code': business_short_code,
            'passkey': passkey,
            'callback_url': callback_url,
            'is_sandbox': True,
            'is_active': True,
        }
    )
    
    print()
    print("=" * 70)
    if created:
        print("✅ M-Pesa configuration CREATED successfully!")
    else:
        print("✅ M-Pesa configuration UPDATED successfully!")
    print("=" * 70)
    print()
    
    # Test the configuration
    print("Testing configuration...")
    print()
    
    from payments.services import MpesaService
    
    try:
        service = MpesaService(org)
        token = service.get_access_token()
        print("✅ Access token generated successfully!")
        print(f"   Token: {token[:30]}...")
        print()
        print("=" * 70)
        print("🎉 SETUP COMPLETE!")
        print("=" * 70)
        print()
        print("Next steps:")
        print("1. Make sure ngrok is running: ngrok http 8000")
        print("2. Make sure Django server is running: python manage.py runserver")
        print("3. Test payment with phone number: 254708374149")
        print()
        print("For testing, use these sandbox phone numbers:")
        print("  - 254708374149")
        print("  - 254711111111")
        print("  - 254722000000")
        print()
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        print()
        print("Please check:")
        print("1. Consumer Key and Secret are correct")
        print("2. You have internet connection")
        print("3. Safaricom Daraja API is accessible")
        print()

if __name__ == "__main__":
    setup_mpesa_sandbox()

