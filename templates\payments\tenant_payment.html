{% extends 'base.html' %}
{% load org_urls %}

{% block title %}My Payments{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}

<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>My Outstanding Invoices</h4>
                            <div class="card-header-action">
                                <span class="badge badge-info">{{ invoices.count }} unpaid invoice{{ invoices.count|pluralize }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if invoices %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Invoice #</th>
                                                <th>Property</th>
                                                <th>Unit</th>
                                                <th>Due Date</th>
                                                <th>Amount Due</th>
                                                <th>Amount Paid</th>
                                                <th>Balance</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for invoice in invoices %}
                                            <tr>
                                                <td>{{ invoice.number }}</td>
                                                <td>{{ invoice.lease.unit.property.name }}</td>
                                                <td>{{ invoice.lease.unit.code }}</td>
                                                <td>
                                                    {{ invoice.due_date|date:"M d, Y" }}
                                                    {% if invoice.due_date < today %}
                                                        <span class="badge badge-danger">Overdue</span>
                                                    {% endif %}
                                                </td>
                                                <td>KES {{ invoice.amount_due|floatformat:2 }}</td>
                                                <td>KES {{ invoice.amount_paid|floatformat:2 }}</td>
                                                <td>
                                                    <strong class="text-danger">KES {{ invoice.balance|floatformat:2 }}</strong>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{% org_url 'invoice_payment_options' invoice_id=invoice.id %}" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fas fa-credit-card"></i> Pay
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-sm btn-success quick-pay-btn" 
                                                                data-invoice-id="{{ invoice.id }}"
                                                                data-amount="{{ invoice.balance }}"
                                                                data-invoice-number="{{ invoice.number }}">
                                                            <i class="fas fa-mobile-alt"></i> Quick Pay
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h5>All Caught Up!</h5>
                                    <p class="text-muted">You have no outstanding invoices at this time.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Pay Modal -->
<div class="modal fade" id="quickPayModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-mobile-alt text-success"></i> Quick M-Pesa Payment
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="quickPayForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Invoice:</strong> <span id="modal-invoice-number"></span><br>
                        <strong>Amount:</strong> KES <span id="modal-amount"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="modal-phone">M-Pesa Phone Number</label>
                        <input type="tel" 
                               class="form-control" 
                               id="modal-phone" 
                               placeholder="0712345678"
                               pattern="^(\+?254|0)[17]\d{8}$"
                               required>
                        <small class="form-text text-muted">Enter your M-Pesa registered phone number</small>
                    </div>
                    
                    <input type="hidden" id="modal-invoice-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-credit-card"></i> Pay Now
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quickPayButtons = document.querySelectorAll('.quick-pay-btn');
    const quickPayModal = document.getElementById('quickPayModal');
    const quickPayForm = document.getElementById('quickPayForm');
    
    // Handle quick pay button clicks
    quickPayButtons.forEach(button => {
        button.addEventListener('click', function() {
            const invoiceId = this.dataset.invoiceId;
            const amount = this.dataset.amount;
            const invoiceNumber = this.dataset.invoiceNumber;
            
            document.getElementById('modal-invoice-id').value = invoiceId;
            document.getElementById('modal-invoice-number').textContent = invoiceNumber;
            document.getElementById('modal-amount').textContent = parseFloat(amount).toFixed(2);
            
            $('#quickPayModal').modal('show');
        });
    });
    
    // Handle quick pay form submission
    quickPayForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const invoiceId = document.getElementById('modal-invoice-id').value;
        const phoneNumber = document.getElementById('modal-phone').value;
        
        if (!phoneNumber) {
            alert('Please enter your phone number');
            return;
        }
        
        // Redirect to payment initiation with pre-filled phone number
        const url = `/${orgSlug}/payments/invoice/${invoiceId}/pay/?phone=${encodeURIComponent(phoneNumber)}`;
        window.location.href = url;
    });
});
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
}

.btn-group .btn {
    margin-right: 2px;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-info {
    background-color: #17a2b8;
}

.fa-3x {
    font-size: 3rem;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}
</style>
{% endblock %}
