from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404
from core.decorators import require_any_member, require_admin_or_manager
from .models import Property, Unit, Tenant, Lease, Invoice, Payment, TenantCreditBalance


# Property views - Admin/Manager can manage, all members can view
@require_any_member
def property_list(request, org_slug):
    qs = Property.objects.filter(organization=request.org)
    return render(request, 'rentals/property_list.html', {'items': qs})


# Unit views - Admin/Manager can manage, all members can view
@require_any_member
def unit_list(request, org_slug):
    qs = Unit.objects.filter(organization=request.org).select_related('property')
    return render(request, 'rentals/unit_list.html', {'items': qs})


# Tenant views - Admin/Manager can manage, all members can view
@require_any_member
def tenant_list(request, org_slug):
    qs = Tenant.objects.filter(organization=request.org)
    return render(request, 'rentals/tenant_list.html', {'items': qs})


@login_required
def lease_list(request, org_slug):
    qs = Lease.objects.filter(organization=request.org).select_related('unit', 'tenant')
    return render(request, 'rentals/lease_list.html', {'items': qs})


@login_required
def lease_detail(request, org_slug, pk):
    """View lease details with credit balance and invoices"""
    lease = get_object_or_404(Lease, pk=pk, organization=request.org)

    # Get credit balance
    credit_balance = TenantCreditBalance.get_or_create_for_lease(lease)

    # Get invoices for this lease
    invoices = Invoice.objects.filter(
        organization=request.org,
        lease=lease
    ).order_by('-issue_date')

    # Get payments for this lease
    payments = Payment.objects.filter(
        organization=request.org,
        lease=lease
    ).order_by('-date')

    context = {
        'lease': lease,
        'credit_balance': credit_balance,
        'invoices': invoices,
        'payments': payments,
        'title': f'Lease Details - {lease.tenant}',
    }

    return render(request, 'rentals/lease_detail.html', context)


@login_required
def invoice_list(request, org_slug):
    qs = Invoice.objects.filter(organization=request.org)
    return render(request, 'billing/invoice_list.html', {'items': qs})


@login_required
def invoice_detail(request, org_slug, pk):
    obj = get_object_or_404(Invoice, pk=pk, organization=request.org)

    # Get credit balance for this tenant
    credit_balance = TenantCreditBalance.get_or_create_for_lease(obj.lease)

    context = {
        'invoice': obj,
        'credit_balance': credit_balance,
    }

    return render(request, 'billing/invoice_detail.html', context)


@login_required
def payment_list(request, org_slug):
    qs = Payment.objects.filter(organization=request.org).select_related('invoice')
    return render(request, 'billing/payment_list.html', {'items': qs})
