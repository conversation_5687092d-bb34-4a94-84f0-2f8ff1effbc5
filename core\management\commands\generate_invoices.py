from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import models
from datetime import datetime, timedelta
from rentals.models import Lease, Invoice
from core.models import Organization


class Command(BaseCommand):
    help = 'Generate monthly invoices for active leases'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org-slug',
            type=str,
            help='Generate invoices for specific organization only',
        )
        parser.add_argument(
            '--month',
            type=str,
            help='Generate invoices for specific month (YYYY-MM format)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be generated without creating invoices',
        )

    def handle(self, *args, **options):
        # Determine target month
        if options['month']:
            try:
                target_date = datetime.strptime(options['month'], '%Y-%m').date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('Invalid month format. Use YYYY-MM')
                )
                return
        else:
            # Default to next month
            today = timezone.now().date()
            if today.month == 12:
                target_date = today.replace(year=today.year + 1, month=1, day=1)
            else:
                target_date = today.replace(month=today.month + 1, day=1)

        # Get organizations to process
        if options['org_slug']:
            try:
                organizations = [Organization.objects.get(slug=options['org_slug'])]
            except Organization.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Organization "{options["org_slug"]}" not found')
                )
                return
        else:
            organizations = Organization.objects.all()

        total_generated = 0
        
        for org in organizations:
            self.stdout.write(f'\nProcessing organization: {org.name}')
            
            # Get active leases for this organization
            active_leases = Lease.objects.filter(
                organization=org,
                status='ACTIVE',
                start_date__lte=target_date
            ).filter(
                models.Q(end_date__isnull=True) | models.Q(end_date__gte=target_date)
            )
            
            org_generated = 0
            
            for lease in active_leases:
                # Check if invoice already exists for this month
                invoice_number = f"INV-{target_date.strftime('%Y%m')}-{lease.id:04d}"
                
                existing_invoice = Invoice.objects.filter(
                    organization=org,
                    number=invoice_number
                ).first()
                
                if existing_invoice:
                    self.stdout.write(f'  Invoice {invoice_number} already exists - skipping')
                    continue
                
                # Calculate due date (billing_day of the month)
                try:
                    due_date = target_date.replace(day=lease.billing_day)
                except ValueError:
                    # Handle cases where billing_day doesn't exist in target month (e.g., Feb 30)
                    due_date = target_date.replace(day=28)
                
                if options['dry_run']:
                    self.stdout.write(
                        f'  Would create: {invoice_number} for {lease.tenant} - '
                        f'${lease.rent_amount} due {due_date}'
                    )
                else:
                    # Create the invoice
                    invoice = Invoice.objects.create(
                        organization=org,
                        lease=lease,
                        number=invoice_number,
                        issue_date=timezone.now().date(),
                        due_date=due_date,
                        amount_due=lease.rent_amount
                    )
                    
                    self.stdout.write(
                        f'  Created: {invoice.number} for {lease.tenant} - '
                        f'${invoice.amount_due} due {invoice.due_date}'
                    )
                
                org_generated += 1
            
            self.stdout.write(f'Generated {org_generated} invoices for {org.name}')
            total_generated += org_generated
        
        action = 'Would generate' if options['dry_run'] else 'Generated'
        self.stdout.write(
            self.style.SUCCESS(f'\n{action} {total_generated} invoices total for {target_date.strftime("%B %Y")}')
        )
