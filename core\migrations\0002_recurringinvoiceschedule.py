# Generated by Django 5.2.5 on 2025-10-03 18:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecurringInvoiceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Monthly Rent Invoices', max_length=200)),
                ('frequency', models.CharField(choices=[('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly')], default='MONTHLY', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('day_of_month', models.IntegerField(default=1, help_text='Day of month to generate invoices (1-28)')),
                ('due_days', models.IntegerField(default=7, help_text='Number of days until invoice is due')),
                ('include_mandatory_amenities', models.<PERSON>oleanField(default=True)),
                ('last_run_date', models.DateField(blank=True, null=True)),
                ('last_run_count', models.IntegerField(default=0, help_text='Number of invoices created in last run')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
