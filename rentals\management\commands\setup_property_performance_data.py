from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from core.models import Organization
from rentals.models import (
    Property, Unit, Tenant, Lease, Invoice, Payment,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem
)
import random


class Command(BaseCommand):
    help = 'Create comprehensive property performance data for testing analytics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org-slug',
            type=str,
            help='Organization slug to create data for',
            required=True
        )

    def handle(self, *args, **options):
        org_slug = options['org_slug']
        
        try:
            organization = Organization.objects.get(slug=org_slug)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Organization with slug "{org_slug}" not found')
            )
            return

        self.stdout.write(f'Creating property performance data for: {organization.name}')

        # Create multiple properties with varied performance
        self.create_sample_properties_with_performance_data(organization)
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created property performance data!')
        )

    def create_sample_properties_with_performance_data(self, organization):
        """Create multiple properties with varied performance data"""
        properties_data = [
            {
                'name': 'Westlands Heights',
                'address': '123 Westlands Road, Nairobi',
                'units_count': 20,
                'performance_tier': 'high',  # High performer
            },
            {
                'name': 'Kilimani Apartments',
                'address': '456 Kilimani Street, Nairobi',
                'units_count': 15,
                'performance_tier': 'medium',  # Average performer
            },
            {
                'name': 'Eastlands Complex',
                'address': '789 Eastlands Avenue, Nairobi',
                'units_count': 25,
                'performance_tier': 'low',  # Needs attention
            },
            {
                'name': 'Karen Villas',
                'address': '321 Karen Road, Nairobi',
                'units_count': 10,
                'performance_tier': 'high',  # High performer
            },
            {
                'name': 'South B Towers',
                'address': '654 South B, Nairobi',
                'units_count': 30,
                'performance_tier': 'medium',  # Average performer
            },
        ]
        
        for prop_data in properties_data:
            # Create property
            property_obj, created = Property.objects.get_or_create(
                organization=organization,
                name=prop_data['name'],
                defaults={
                    'address': prop_data['address']
                }
            )
            
            if created:
                self.stdout.write(f'Created property: {property_obj.name}')
                
                # Create units for this property
                self.create_units_for_property(property_obj, prop_data['units_count'], prop_data['performance_tier'])
                
                # Create tenants and leases
                self.create_tenants_and_leases_for_property(property_obj, prop_data['performance_tier'])

    def create_units_for_property(self, property_obj, units_count, performance_tier):
        """Create units for a property based on performance tier"""
        base_rent = {
            'high': 35000,
            'medium': 25000,
            'low': 15000
        }
        
        for i in range(1, units_count + 1):
            unit_code = f"{property_obj.name[0]}{i:03d}"  # e.g., W001, K001
            
            # Vary rent slightly
            rent_variation = random.randint(-2000, 3000)
            rent_amount = base_rent[performance_tier] + rent_variation
            
            unit = Unit.objects.create(
                organization=property_obj.organization,
                property=property_obj,
                code=unit_code,
                bedrooms=random.choice([1, 2, 3]),
                bathrooms=random.choice([1, 2])
            )

            # Store rent amount for later use in lease creation
            unit._temp_rent_amount = Decimal(str(rent_amount))
            
            # Add amenities to units
            self.add_amenities_to_unit(unit)

    def add_amenities_to_unit(self, unit):
        """Add random amenities to a unit"""
        amenity_types = AmenityType.objects.filter(organization=unit.organization)
        
        for amenity_type in amenity_types:
            # 70% chance of having each amenity
            if random.random() < 0.7:
                UnitAmenity.objects.create(
                    organization=unit.organization,
                    unit=unit,
                    amenity_type=amenity_type,
                    amount=amenity_type.default_amount + Decimal(random.randint(-500, 1000)),
                    start_date=timezone.now().date() - timedelta(days=random.randint(30, 365))
                )

    def create_tenants_and_leases_for_property(self, property_obj, performance_tier):
        """Create tenants and leases based on property performance"""
        units = property_obj.unit_set.all()
        
        # Occupancy rates by performance tier
        occupancy_rates = {
            'high': 0.95,    # 95% occupancy
            'medium': 0.80,  # 80% occupancy
            'low': 0.60      # 60% occupancy
        }
        
        target_occupancy = occupancy_rates[performance_tier]
        units_to_occupy = int(len(units) * target_occupancy)
        
        occupied_units = random.sample(list(units), units_to_occupy)
        
        for i, unit in enumerate(occupied_units):
            # Create tenant
            tenant = Tenant.objects.create(
                organization=property_obj.organization,
                first_name=f"Tenant{i+1}",
                last_name=f"Property{property_obj.name.replace(' ', '')}",
                email=f"tenant{i+1}@{property_obj.name.lower().replace(' ', '')}.com",
                phone=f"07{random.randint(10000000, 99999999)}"
            )
            
            # Create lease
            start_date = timezone.now().date() - timedelta(days=random.randint(30, 365))
            end_date = start_date + timedelta(days=365)
            
            # Get the rent amount we stored temporarily
            rent_amount = getattr(unit, '_temp_rent_amount', Decimal('25000.00'))

            lease = Lease.objects.create(
                organization=property_obj.organization,
                unit=unit,
                tenant=tenant,
                start_date=start_date,
                end_date=end_date,
                rent_amount=rent_amount,
                deposit_amount=rent_amount * 2,
                status='ACTIVE'
            )
            
            # Create invoices and payments for this lease
            self.create_invoices_and_payments_for_lease(lease, performance_tier)

    def create_invoices_and_payments_for_lease(self, lease, performance_tier):
        """Create invoices and payments based on performance tier"""
        current_date = timezone.now().date()
        lease_months = (current_date - lease.start_date).days // 30
        
        # Payment reliability by performance tier
        payment_rates = {
            'high': 0.98,    # 98% payment rate
            'medium': 0.85,  # 85% payment rate
            'low': 0.70      # 70% payment rate
        }
        
        payment_rate = payment_rates[performance_tier]
        
        for month in range(max(1, lease_months)):
            invoice_date = lease.start_date + timedelta(days=30 * month)
            
            if invoice_date > current_date:
                break
                
            # Calculate amounts
            rent_amount = lease.rent_amount
            amenities_amount = Decimal('0.00')
            
            # Add amenity costs
            unit_amenities = UnitAmenity.objects.filter(unit=lease.unit, is_active=True)
            for amenity in unit_amenities:
                amenities_amount += amenity.amount
            
            # Calculate taxes
            tax_amount = (rent_amount + amenities_amount) * Decimal('0.10')  # 10% tax
            total_amount = rent_amount + amenities_amount + tax_amount
            
            # Generate unique invoice number
            invoice_number = f"INV{lease.unit.property.name[:3].upper()}{lease.unit.code}{month:02d}"

            # Create invoice
            invoice = Invoice.objects.create(
                organization=lease.organization,
                lease=lease,
                number=invoice_number,
                issue_date=invoice_date,
                due_date=invoice_date + timedelta(days=30),
                rent_amount=rent_amount,
                amenities_amount=amenities_amount,
                tax_amount=tax_amount,
                amount_due=total_amount,
                invoice_type='MIXED'
            )
            
            # Create payment based on payment rate
            if random.random() < payment_rate:
                payment_date = invoice_date + timedelta(days=random.randint(1, 25))
                
                # Sometimes partial payments
                if random.random() < 0.1:  # 10% chance of partial payment
                    payment_amount = total_amount * Decimal(str(random.uniform(0.5, 0.9)))
                else:
                    payment_amount = total_amount
                
                payment = Payment.objects.create(
                    organization=lease.organization,
                    invoice=invoice,
                    date=payment_date,
                    amount=payment_amount,
                    method=random.choice(['MPESA', 'CASH', 'BANK_TRANSFER']),
                    status='COMPLETED',
                    reference=f'PAY{random.randint(100000, 999999)}'
                )
                
                # Update invoice
                invoice.amount_paid = payment_amount
                if payment_amount >= total_amount:
                    invoice.is_paid = True
                invoice.save()
