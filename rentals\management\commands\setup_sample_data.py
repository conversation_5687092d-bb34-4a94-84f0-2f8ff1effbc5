from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from core.models import Organization
from rentals.models import (
    Property, Unit, Tenant, Lease, Invoice, Payment,
    AmenityType, UnitAmenity, GovernmentTax, InvoiceLineItem
)
import random


class Command(BaseCommand):
    help = 'Create sample data for testing reports'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org-slug',
            type=str,
            help='Organization slug to create data for',
            required=True
        )

    def handle(self, *args, **options):
        org_slug = options['org_slug']
        
        try:
            organization = Organization.objects.get(slug=org_slug)
        except Organization.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Organization with slug "{org_slug}" not found')
            )
            return

        self.stdout.write(f'Creating sample data for organization: {organization.name}')

        # Create Government Taxes
        self.create_government_taxes(organization)
        
        # Create Amenity Types
        self.create_amenity_types(organization)
        
        # Create sample payments for the last 12 months
        self.create_sample_payments(organization)
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )

    def create_government_taxes(self, organization):
        """Create sample government taxes"""
        taxes = [
            {
                'name': 'Rental Income Tax',
                'tax_type': 'RENTAL_INCOME_TAX',
                'calculation_method': 'PERCENTAGE',
                'rate': Decimal('10.00'),
                'description': 'Kenya rental income tax at 10%',
            },
            {
                'name': 'Property Tax',
                'tax_type': 'PROPERTY_TAX',
                'calculation_method': 'PER_UNIT',
                'rate': Decimal('500.00'),
                'description': 'Annual property tax per unit',
            },
            {
                'name': 'VAT on Services',
                'tax_type': 'VAT',
                'calculation_method': 'PERCENTAGE',
                'rate': Decimal('16.00'),
                'description': 'VAT on amenity services',
            },
        ]
        
        for tax_data in taxes:
            tax, created = GovernmentTax.objects.get_or_create(
                organization=organization,
                name=tax_data['name'],
                defaults={
                    **tax_data,
                    'effective_date': timezone.now().date() - timedelta(days=365),
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(f'Created tax: {tax.name}')

    def create_amenity_types(self, organization):
        """Create sample amenity types"""
        amenities = [
            {
                'name': 'Water Bill',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('1500.00'),
                'is_mandatory': True,
            },
            {
                'name': 'Electricity',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('2000.00'),
                'is_mandatory': True,
            },
            {
                'name': 'Garbage Collection',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('500.00'),
                'is_mandatory': True,
            },
            {
                'name': 'Internet',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('3000.00'),
                'is_mandatory': False,
            },
            {
                'name': 'Security',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('1000.00'),
                'is_mandatory': True,
            },
            {
                'name': 'Parking',
                'billing_cycle': 'MONTHLY',
                'default_amount': Decimal('2000.00'),
                'is_mandatory': False,
            },
        ]
        
        for amenity_data in amenities:
            amenity, created = AmenityType.objects.get_or_create(
                organization=organization,
                name=amenity_data['name'],
                defaults={
                    **amenity_data,
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(f'Created amenity: {amenity.name}')

    def create_sample_payments(self, organization):
        """Create sample payments for the last 12 months"""
        current_date = timezone.now().date()
        
        # Create sample payments for each month
        for month_offset in range(12):
            payment_date = current_date - timedelta(days=30 * month_offset)
            
            # Create 5-15 random payments per month
            num_payments = random.randint(5, 15)
            
            for _ in range(num_payments):
                # Random payment amount between 10,000 and 50,000
                amount = Decimal(random.randint(10000, 50000))
                
                # Create a dummy invoice if none exists
                invoice = self.get_or_create_dummy_invoice(organization, payment_date)
                
                # Create payment
                payment = Payment.objects.create(
                    organization=organization,
                    invoice=invoice,
                    date=payment_date,
                    amount=amount,
                    method=random.choice(['MPESA', 'CASH', 'BANK_TRANSFER']),
                    status='COMPLETED',
                    reference=f'PAY{random.randint(100000, 999999)}',
                )
                
                # Update invoice payment status
                invoice.amount_paid += amount
                if invoice.amount_paid >= invoice.amount_due:
                    invoice.is_paid = True
                invoice.save()
        
        self.stdout.write(f'Created sample payments for the last 12 months')

    def get_or_create_dummy_invoice(self, organization, date):
        """Get or create a dummy invoice for the payment"""
        # Try to find an existing unpaid invoice
        invoice = Invoice.objects.filter(
            organization=organization,
            is_paid=False
        ).first()
        
        if invoice:
            return invoice
        
        # Create a dummy invoice
        invoice_number = f'INV{random.randint(1000, 9999)}'
        
        invoice = Invoice.objects.create(
            organization=organization,
            lease=self.get_or_create_dummy_lease(organization),
            number=invoice_number,
            issue_date=date,
            due_date=date + timedelta(days=30),
            rent_amount=Decimal(random.randint(15000, 35000)),
            amenities_amount=Decimal(random.randint(5000, 15000)),
            tax_amount=Decimal(random.randint(1000, 5000)),
            amount_due=Decimal(random.randint(25000, 55000)),
            invoice_type='MIXED',
        )
        
        return invoice

    def get_or_create_dummy_lease(self, organization):
        """Get or create a dummy lease"""
        lease = Lease.objects.filter(organization=organization).first()
        
        if lease:
            return lease
        
        # Create dummy property, unit, tenant, and lease
        property_obj = Property.objects.create(
            organization=organization,
            name='Sample Property',
            address='123 Sample Street, Nairobi'
        )
        
        unit = Unit.objects.create(
            organization=organization,
            property=property_obj,
            code='A101',
            bedrooms=2,
            bathrooms=1
        )
        
        tenant = Tenant.objects.create(
            organization=organization,
            first_name='John',
            last_name='Doe',
            email='<EMAIL>',
            phone='0712345678'
        )
        
        lease = Lease.objects.create(
            organization=organization,
            unit=unit,
            tenant=tenant,
            start_date=timezone.now().date() - timedelta(days=365),
            end_date=timezone.now().date() + timedelta(days=365),
            rent_amount=Decimal('25000.00'),
            deposit_amount=Decimal('50000.00'),
            status='ACTIVE'
        )
        
        return lease
