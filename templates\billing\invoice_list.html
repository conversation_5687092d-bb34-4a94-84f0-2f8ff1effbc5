{% extends 'base.html' %}
{% load org_urls %}
{% block title %}{{ title }}{% endblock %}
{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
    <div class="main-wrapper main-wrapper-1">
        <div class="main-content">
            <div class="section">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h1 class="h4 mb-0">Invoices</h1>
                                    <div class="btn-group">
                                        <a href="{% org_url 'invoice_create' %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus"></i> Add Invoice
                                        </a>
                                        <a href="{% org_url 'bulk_invoice_generate' %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-file-invoice-dollar"></i> Bulk Generate
                                        </a>
                                        <a href="{% org_url 'payment_create' %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-credit-card"></i> Record Payment
                                        </a>
                                    </div>
                                </div>
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Invoice</th>
                                            <th>Tenant</th>
                                            <th>Unit</th>
                                            <th>Issue</th>
                                            <th>Due</th>
                                            <th>Amount</th>
                                            <th>Paid</th>
                                            <th>Balance</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inv in items %}
                                        <tr>
                                            <td>{{ forloop.counter }}</td>
                                            <td>
                                                <a href="{% org_url 'invoice_detail' pk=inv.pk %}">{{ inv.number }}</a>
                                            </td>
                                            <td>{{ inv.lease.tenant }}</td>
                                            <td>{{ inv.lease.unit.code }}</td>
                                            <td>{{ inv.issue_date }}</td>
                                            <td>{{ inv.due_date }}</td>
                                            <td>{{ inv.amount_due }}</td>
                                            <td>{{ inv.amount_paid }}</td>
                                            <td>{{ inv.balance }}</td>
                                            <td>
                                                {% if inv.is_paid %}
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check"></i> Paid
                                                    </span>
                                                {% else %}
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-clock"></i> Unpaid
                                                    </span>
                                                    {% if inv.balance > 0 and inv.amount_paid > 0 %}
                                                        <span class="badge bg-info ml-1">Partial</span>
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% org_url 'invoice_detail' pk=inv.pk %}" class="btn btn-outline-secondary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <a href="{% org_url 'invoice_pdf_download' pk=inv.pk %}" class="btn btn-outline-danger" target="_blank" title="Download PDF">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                    {% if not inv.is_paid %}
                                                        <a href="{% org_url 'quick_pay_invoice' invoice_id=inv.pk %}" class="btn btn-success" title="Quick Pay">
                                                            <i class="fas fa-bolt"></i> Pay
                                                        </a>
                                                        <a href="{% org_url 'invoice_edit' pk=inv.pk %}" class="btn btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="11" class="text-center">No invoices yet.</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Quick Pay Modal -->
<div class="modal fade" id="quickPayModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-mobile-alt text-success"></i> Quick M-Pesa Payment
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="quickPayForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Invoice:</strong> <span id="modal-invoice-number"></span><br>
                        <strong>Amount:</strong> KES <span id="modal-amount"></span>
                    </div>

                    <div class="form-group">
                        <label for="modal-phone">M-Pesa Phone Number</label>
                        <input type="tel"
                               class="form-control"
                               id="modal-phone"
                               name="phone_number"
                               placeholder="0712345678"
                               pattern="^(\+?254|0)[17]\d{8}$"
                               required>
                        <small class="form-text text-muted">Enter your M-Pesa registered phone number</small>
                    </div>

                    <input type="hidden" id="modal-invoice-id" name="invoice_id">
                    <input type="hidden" id="modal-amount-value" name="amount">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-credit-card"></i> Pay Now
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quickPayButtons = document.querySelectorAll('.quick-pay-btn');
    const orgSlug = '{{ org.slug }}';

    quickPayButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const invoiceId = this.dataset.invoiceId;
            const invoiceNumber = this.dataset.invoiceNumber;
            const amount = this.dataset.amount;

            document.getElementById('modal-invoice-id').value = invoiceId;
            document.getElementById('modal-invoice-number').textContent = invoiceNumber;
            document.getElementById('modal-amount').textContent = parseFloat(amount).toFixed(2);
            document.getElementById('modal-amount-value').value = amount;

            // Set form action
            const form = document.getElementById('quickPayForm');
            form.action = `/${orgSlug}/payments/invoice/${invoiceId}/pay/`;

            $('#quickPayModal').modal('show');
        });
    });

    // Auto-format phone number input
    document.getElementById('modal-phone')?.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Remove non-digits

        if (value.startsWith('254')) {
            e.target.value = value;
        } else if (value.startsWith('0')) {
            e.target.value = '0' + value.slice(1);
        } else if (value.length > 0) {
            e.target.value = '0' + value;
        }
    });
});
</script>

<style>
.dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

.badge {
    font-size: 0.8rem;
}

.btn-group .dropdown-toggle::after {
    margin-left: 0.3rem;
}
</style>

{% include 'footer.html' %}
{% endblock %}