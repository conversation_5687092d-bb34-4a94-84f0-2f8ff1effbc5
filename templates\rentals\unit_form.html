{% extends 'base.html' %}
{% load org_urls %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
{% include 'navbar.html' %}
{% include 'sidebar.html' %}
<div class="main-wrapper main-wrapper-1">
    <div class="main-content">
        <div class="section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="h4 mb-0">{{ title }}</h1>
    <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Back to Units</a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.property.id_for_label }}" class="form-label">Property</label>
                {{ form.property }}
                {% if form.property.errors %}
                    <div class="text-danger">{{ form.property.errors }}</div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.code.id_for_label }}" class="form-label">
                    Unit Code
                    <span class="badge bg-info text-white ms-2">
                        <i class="fas fa-magic"></i> Auto-generated
                    </span>
                </label>
                {{ form.code }}
                <small class="form-text text-muted">
                    <i class="fas fa-info-circle"></i> {{ form.code.help_text }}
                </small>
                {% if form.code.errors %}
                    <div class="text-danger">{{ form.code.errors }}</div>
                {% endif %}
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.bedrooms.id_for_label }}" class="form-label">Bedrooms</label>
                        {{ form.bedrooms }}
                        {% if form.bedrooms.errors %}
                            <div class="text-danger">{{ form.bedrooms.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.bathrooms.id_for_label }}" class="form-label">Bathrooms</label>
                        {{ form.bathrooms }}
                        {% if form.bathrooms.errors %}
                            <div class="text-danger">{{ form.bathrooms.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3 form-check">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="form-check-label">Active</label>
                {% if form.is_active.errors %}
                    <div class="text-danger">{{ form.is_active.errors }}</div>
                {% endif %}
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Save Unit</button>
                <a href="{% org_url 'unit_list' %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
{% include 'footer.html' %}
{% endblock %}
