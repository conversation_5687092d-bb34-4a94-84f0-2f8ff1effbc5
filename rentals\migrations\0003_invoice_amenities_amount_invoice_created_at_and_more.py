# Generated by Django 5.2.5 on 2025-10-03 11:21

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        ('rentals', '0002_payment_checkout_request_id_payment_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='amenities_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='invoice',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='invoice',
            name='invoice_type',
            field=models.CharField(choices=[('RENT', 'Rent'), ('AMENITIES', 'Amenities'), ('MIXED', 'Rent + Amenities'), ('DEPOSIT', 'Deposit'), ('OTHER', 'Other')], default='RENT', max_length=20),
        ),
        migrations.AddField(
            model_name='invoice',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='invoice',
            name='other_charges',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='invoice',
            name='rent_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='invoice',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='AmenityType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('billing_cycle', models.CharField(choices=[('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('ANNUALLY', 'Annually'), ('ONE_TIME', 'One Time'), ('ON_DEMAND', 'On Demand')], default='MONTHLY', max_length=20)),
                ('default_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_mandatory', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('organization', 'name')},
            },
        ),
        migrations.CreateModel(
            name='GovernmentTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('tax_type', models.CharField(choices=[('RENTAL_INCOME_TAX', 'Rental Income Tax'), ('PROPERTY_TAX', 'Property Tax'), ('SERVICE_CHARGE_TAX', 'Service Charge Tax'), ('VAT', 'Value Added Tax'), ('WITHHOLDING_TAX', 'Withholding Tax'), ('LAND_RATES', 'Land Rates'), ('OTHER', 'Other')], max_length=30)),
                ('calculation_method', models.CharField(choices=[('PERCENTAGE', 'Percentage of Income'), ('FIXED_AMOUNT', 'Fixed Amount'), ('PER_UNIT', 'Per Unit'), ('PER_SQFT', 'Per Square Foot')], max_length=20)),
                ('rate', models.DecimalField(decimal_places=2, help_text='Percentage rate or fixed amount', max_digits=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('effective_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('organization', 'name')},
            },
        ),
        migrations.CreateModel(
            name='InvoiceLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_type', models.CharField(choices=[('RENT', 'Rent'), ('WATER', 'Water'), ('ELECTRICITY', 'Electricity'), ('GARBAGE', 'Garbage Collection'), ('INTERNET', 'Internet'), ('SECURITY', 'Security'), ('PARKING', 'Parking'), ('MAINTENANCE', 'Maintenance'), ('TAX', 'Tax'), ('OTHER', 'Other')], max_length=20)),
                ('description', models.CharField(max_length=200)),
                ('quantity', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('amenity_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='rentals.amenitytype')),
                ('government_tax', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='rentals.governmenttax')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='rentals.invoice')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
            ],
            options={
                'ordering': ['item_type', 'description'],
            },
        ),
        migrations.CreateModel(
            name='UnitAmenity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('amenity_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rentals.amenitytype')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
                ('unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='amenities', to='rentals.unit')),
            ],
            options={
                'unique_together': {('organization', 'unit', 'amenity_type')},
            },
        ),
    ]
