from django import template
from django.urls import reverse


register = template.Library()


@register.simple_tag(takes_context=True)
def org_url(context, name, *args, **kwargs):
    request = context.get('request')

    # Try to get org from context first
    org = context.get('org')

    # If not in context, try to get from request
    if not org and request:
        org = getattr(request, 'org', None)

    # If still no org, try to get org_slug from the current URL
    if not org and request and hasattr(request, 'resolver_match') and request.resolver_match:
        org_slug = request.resolver_match.kwargs.get('org_slug')
        if org_slug:
            kwargs = {**kwargs, 'org_slug': org_slug}
            return reverse(name, args=args, kwargs=kwargs)

    # If we have an org object, use its slug
    if org:
        kwargs = {**kwargs, 'org_slug': org.slug}
        return reverse(name, args=args, kwargs=kwargs)

    # Fallback - try without org_slug (will likely fail)
    return reverse(name, args=args, kwargs=kwargs)