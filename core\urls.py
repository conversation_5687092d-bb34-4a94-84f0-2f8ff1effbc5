from django.urls import path, include
from . import views, crud_views, payment_views, advance_payment_views


urlpatterns = [
    path('<str:org_slug>/', include([
        path('', views.dashboard, name='dashboard'),
        path('api/dashboard/revenue-data/', views.dashboard_revenue_data, name='dashboard_revenue_data'),
        path('api/dashboard/refresh/', views.dashboard_refresh_data, name='dashboard_refresh_data'),
        path('auth/login/', views.login_view, name='login'),
        path('auth/logout/', views.logout_view, name='logout'),

        path('rentals/', include('rentals.urls')),

        # Property CRUD
        path('properties/add/', views.property_create, name='property_create'),
        path('properties/<int:pk>/edit/', views.property_edit, name='property_edit'),
        path('properties/<int:pk>/delete/', views.property_delete, name='property_delete'),

        # Unit CRUD
        path('units/add/', crud_views.unit_create, name='unit_create'),
        path('units/<int:pk>/edit/', crud_views.unit_edit, name='unit_edit'),
        path('units/<int:pk>/delete/', crud_views.unit_delete, name='unit_delete'),

        # Tenant CRUD
        path('tenants/add/', crud_views.tenant_create, name='tenant_create'),
        path('tenants/<int:pk>/edit/', crud_views.tenant_edit, name='tenant_edit'),
        path('tenants/<int:pk>/delete/', crud_views.tenant_delete, name='tenant_delete'),

        # Lease CRUD
        path('leases/add/', crud_views.lease_create, name='lease_create'),
        path('leases/<int:pk>/edit/', crud_views.lease_edit, name='lease_edit'),
        path('leases/<int:pk>/delete/', crud_views.lease_delete, name='lease_delete'),

        # Invoice CRUD
        path('invoices/add/', crud_views.invoice_create, name='invoice_create'),
        path('invoices/<int:pk>/edit/', crud_views.invoice_edit, name='invoice_edit'),
        path('invoices/bulk-generate/', crud_views.bulk_invoice_generate, name='bulk_invoice_generate'),
        path('invoices/<int:pk>/pdf/', crud_views.invoice_pdf_download, name='invoice_pdf_download'),

        # Payment CRUD
        path('payments/add/', crud_views.payment_create, name='payment_create'),
        path('api/invoices/<int:invoice_id>/details/', crud_views.get_invoice_details, name='get_invoice_details'),

        # Simplified Quick Pay
        path('invoices/<int:invoice_id>/quick-pay/', payment_views.quick_pay_invoice, name='quick_pay_invoice'),
        path('api/invoices/<int:invoice_id>/payment-info/', payment_views.get_invoice_payment_info, name='get_invoice_payment_info'),

        # Advance Payment URLs
        path('leases/<int:lease_id>/advance-payment/', advance_payment_views.record_advance_payment, name='record_advance_payment'),
        path('leases/<int:lease_id>/credit-balance/', advance_payment_views.tenant_credit_balance_view, name='tenant_credit_balance'),
        path('invoices/<int:invoice_id>/apply-credit/', advance_payment_views.apply_credit_to_invoice, name='apply_credit_to_invoice'),
        path('api/leases/<int:lease_id>/calculate-advance/', advance_payment_views.calculate_advance_payment, name='calculate_advance_payment'),

        # M-Pesa Payment URLs
        path('payments/', include('payments.urls')),
    ])),
]