"""
M-Pesa Configuration Diagnostic Script
Run this to check your M-Pesa configuration and identify issues
"""
import os
import django
import sys

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'rentalx.settings')
django.setup()

from payments.models import MpesaConfiguration
from core.models import Organization
from payments.services import MpesaService
from colorama import init, Fore, Style

# Initialize colorama for colored output
try:
    init(autoreset=True)
except:
    pass

def print_success(message):
    print(f"{Fore.GREEN}✅ {message}{Style.RESET_ALL}")

def print_error(message):
    print(f"{Fore.RED}❌ {message}{Style.RESET_ALL}")

def print_warning(message):
    print(f"{Fore.YELLOW}⚠️  {message}{Style.RESET_ALL}")

def print_info(message):
    print(f"{Fore.CYAN}ℹ️  {message}{Style.RESET_ALL}")

def print_header(message):
    print(f"\n{Fore.BLUE}{'='*60}")
    print(f"{message}")
    print(f"{'='*60}{Style.RESET_ALL}\n")

def check_organization():
    """Check if organization exists"""
    print_header("1. Checking Organization")
    
    try:
        org = Organization.objects.get(slug='test')
        print_success(f"Organization found: {org.name}")
        return org
    except Organization.DoesNotExist:
        print_error("Organization 'test' not found!")
        print_info("Available organizations:")
        for org in Organization.objects.all():
            print(f"  - {org.slug}: {org.name}")
        return None

def check_mpesa_config(org):
    """Check M-Pesa configuration"""
    print_header("2. Checking M-Pesa Configuration")
    
    try:
        config = MpesaConfiguration.objects.get(organization=org)
        print_success("M-Pesa configuration found")
        
        # Check each field
        print("\n📋 Configuration Details:")
        print(f"  Consumer Key: {config.consumer_key[:10]}..." if config.consumer_key else "  Consumer Key: NOT SET")
        print(f"  Consumer Secret: {config.consumer_secret[:10]}..." if config.consumer_secret else "  Consumer Secret: NOT SET")
        print(f"  Business Short Code: {config.business_short_code}")
        print(f"  Passkey: {config.passkey[:20]}..." if config.passkey else "  Passkey: NOT SET")
        print(f"  Callback URL: {config.callback_url}")
        print(f"  Is Sandbox: {config.is_sandbox}")
        print(f"  Is Active: {config.is_active}")
        
        # Validate fields
        issues = []
        
        if not config.consumer_key:
            issues.append("Consumer Key is not set")
        
        if not config.consumer_secret:
            issues.append("Consumer Secret is not set")
        
        if not config.business_short_code:
            issues.append("Business Short Code is not set")
        elif config.is_sandbox and config.business_short_code != "174379":
            issues.append(f"Business Short Code should be '174379' for sandbox (currently: {config.business_short_code})")
        
        if not config.passkey:
            issues.append("Passkey is not set")
        elif config.is_sandbox and config.passkey != "bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919":
            issues.append("Passkey doesn't match sandbox passkey")
        
        if not config.callback_url:
            issues.append("Callback URL is not set")
        elif not config.callback_url.startswith('https://'):
            issues.append(f"Callback URL must use HTTPS (currently: {config.callback_url})")
        elif 'localhost' in config.callback_url or '127.0.0.1' in config.callback_url:
            issues.append("Callback URL cannot be localhost - use ngrok or similar tunnel")
        
        if not config.is_active:
            issues.append("Configuration is not active")
        
        if issues:
            print("\n⚠️  Issues Found:")
            for issue in issues:
                print_error(issue)
            return config, False
        else:
            print_success("\nAll configuration fields are valid!")
            return config, True
            
    except MpesaConfiguration.DoesNotExist:
        print_error("M-Pesa configuration not found!")
        print_info("Create one in Django admin or run the fix script")
        return None, False

def check_callback_url(config):
    """Check if callback URL is accessible"""
    print_header("3. Checking Callback URL Accessibility")
    
    if not config:
        print_error("No configuration to check")
        return False
    
    callback_url = config.callback_url
    print_info(f"Callback URL: {callback_url}")
    
    # Check HTTPS
    if not callback_url.startswith('https://'):
        print_error("Callback URL must use HTTPS")
        return False
    else:
        print_success("Using HTTPS")
    
    # Check for localhost
    if 'localhost' in callback_url or '127.0.0.1' in callback_url:
        print_error("Callback URL is localhost - M-Pesa cannot reach it!")
        print_info("Use ngrok: ngrok http 8000")
        print_info("Then update callback URL to: https://your-id.ngrok.io/payments/callback/")
        return False
    else:
        print_success("Not using localhost")
    
    # Try to ping the URL
    try:
        import requests
        print_info("Testing callback URL accessibility...")
        response = requests.get(callback_url.replace('/payments/callback/', '/'), timeout=5, verify=False)
        print_success(f"Server is reachable (Status: {response.status_code})")
        return True
    except Exception as e:
        print_warning(f"Could not reach server: {e}")
        print_info("This might be okay if your server is running but not responding to GET requests")
        return True

def test_access_token(org):
    """Test getting access token"""
    print_header("4. Testing Access Token Generation")
    
    try:
        service = MpesaService(org)
        token = service.get_access_token()
        print_success(f"Access token generated successfully!")
        print_info(f"Token: {token[:30]}...")
        return True
    except Exception as e:
        print_error(f"Failed to get access token: {e}")
        print_info("Check your consumer key and consumer secret")
        return False

def print_recommendations():
    """Print recommendations based on findings"""
    print_header("📝 Recommendations")
    
    print("For Sandbox Testing:")
    print("  1. Use ngrok to expose your local server:")
    print("     ngrok http 8000")
    print()
    print("  2. Update M-Pesa configuration with ngrok URL:")
    print("     Callback URL: https://your-id.ngrok.io/payments/callback/")
    print()
    print("  3. Use sandbox credentials:")
    print("     Business Short Code: 174379")
    print("     Passkey: bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919")
    print()
    print("  4. Test with sandbox phone number:")
    print("     254708374149 or 254711111111")
    print()
    print("For detailed troubleshooting, see: MPESA_TROUBLESHOOTING.md")

def main():
    """Main diagnostic function"""
    print_header("🔍 M-Pesa Configuration Diagnostic Tool")
    
    # Check organization
    org = check_organization()
    if not org:
        print_error("\nCannot proceed without organization")
        sys.exit(1)
    
    # Check M-Pesa config
    config, config_valid = check_mpesa_config(org)
    
    # Check callback URL
    if config:
        callback_valid = check_callback_url(config)
    else:
        callback_valid = False
    
    # Test access token
    if config and config_valid:
        token_valid = test_access_token(org)
    else:
        token_valid = False
    
    # Summary
    print_header("📊 Summary")
    
    if config_valid and callback_valid and token_valid:
        print_success("All checks passed! Your M-Pesa configuration looks good.")
        print_info("You should be able to process payments now.")
    else:
        print_error("Some issues were found. Please fix them and run this script again.")
        print_recommendations()
    
    print()

if __name__ == "__main__":
    main()

