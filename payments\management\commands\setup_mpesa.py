from django.core.management.base import BaseCommand
from django.core.management import Command<PERSON>rror
from core.models import Organization
from payments.models import MpesaConfiguration


class Command(BaseCommand):
    help = 'Set up M-Pesa configuration for an organization'

    def add_arguments(self, parser):
        parser.add_argument('org_slug', type=str, help='Organization slug')
        parser.add_argument('--consumer-key', type=str, required=True, help='M-Pesa Consumer Key')
        parser.add_argument('--consumer-secret', type=str, required=True, help='M-Pesa Consumer Secret')
        parser.add_argument('--business-shortcode', type=str, required=True, help='Business Short Code')
        parser.add_argument('--passkey', type=str, required=True, help='M-Pesa Passkey')
        parser.add_argument('--callback-url', type=str, required=True, help='Callback URL')
        parser.add_argument('--sandbox', action='store_true', help='Use sandbox environment')

    def handle(self, *args, **options):
        try:
            organization = Organization.objects.get(slug=options['org_slug'])
        except Organization.DoesNotExist:
            raise CommandError(f'Organization with slug "{options["org_slug"]}" does not exist.')

        # Create or update M-Pesa configuration
        config, created = MpesaConfiguration.objects.get_or_create(
            organization=organization,
            defaults={
                'consumer_key': options['consumer_key'],
                'consumer_secret': options['consumer_secret'],
                'business_short_code': options['business_shortcode'],
                'passkey': options['passkey'],
                'callback_url': options['callback_url'],
                'is_sandbox': options['sandbox'],
                'is_active': True,
            }
        )

        if not created:
            # Update existing configuration
            config.consumer_key = options['consumer_key']
            config.consumer_secret = options['consumer_secret']
            config.business_short_code = options['business_shortcode']
            config.passkey = options['passkey']
            config.callback_url = options['callback_url']
            config.is_sandbox = options['sandbox']
            config.is_active = True
            config.save()

        action = 'Created' if created else 'Updated'
        environment = 'sandbox' if options['sandbox'] else 'production'
        
        self.stdout.write(
            self.style.SUCCESS(
                f'{action} M-Pesa configuration for {organization.name} '
                f'(Short Code: {options["business_shortcode"]}, Environment: {environment})'
            )
        )

        # Display configuration summary
        self.stdout.write('\nConfiguration Summary:')
        self.stdout.write(f'Organization: {organization.name}')
        self.stdout.write(f'Business Short Code: {config.business_short_code}')
        self.stdout.write(f'Callback URL: {config.callback_url}')
        self.stdout.write(f'Environment: {"Sandbox" if config.is_sandbox else "Production"}')
        self.stdout.write(f'Status: {"Active" if config.is_active else "Inactive"}')

        if options['sandbox']:
            self.stdout.write(
                self.style.WARNING(
                    '\nNote: This configuration is set for SANDBOX environment. '
                    'Make sure to update for production use.'
                )
            )

        self.stdout.write(
            self.style.SUCCESS(
                '\nM-Pesa configuration completed successfully!'
            )
        )
