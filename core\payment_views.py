"""
Simplified unified payment views for all payment methods
"""
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.db import transaction
from datetime import date
from decimal import Decimal

from rentals.models import Invoice, Payment
from payments.services import MpesaService, MpesaAPIException


@login_required
@transaction.atomic
def quick_pay_invoice(request, org_slug, invoice_id):
    """
    Simplified unified payment view for all payment methods.
    Auto-fills tenant phone and invoice balance.
    """
    invoice = get_object_or_404(Invoice, pk=invoice_id, organization=request.org)
    
    # Get tenant phone number
    tenant_phone = invoice.lease.tenant.phone or ''
    
    if request.method == 'POST':
        payment_method = request.POST.get('payment_method')
        amount = Decimal(request.POST.get('amount', invoice.balance))
        
        # Validate amount
        if amount <= 0:
            messages.error(request, 'Amount must be greater than zero.')
            return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice_id)
        
        if amount > invoice.balance:
            messages.error(request, f'Amount cannot exceed balance of KES {invoice.balance:,.2f}')
            return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice_id)
        
        # Handle different payment methods
        if payment_method == 'MPESA':
            return handle_mpesa_payment(request, org_slug, invoice, amount)
        
        elif payment_method == 'CASH':
            return handle_cash_payment(request, org_slug, invoice, amount)
        
        elif payment_method in ['BANK_TRANSFER', 'CHEQUE', 'CARD']:
            return handle_other_payment(request, org_slug, invoice, amount, payment_method)
        
        else:
            messages.error(request, 'Invalid payment method selected.')
            return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice_id)
    
    # GET request - show payment form
    context = {
        'invoice': invoice,
        'tenant_phone': tenant_phone,
        'balance': invoice.balance,
        'title': f'Pay Invoice {invoice.number}',
    }
    
    return render(request, 'billing/quick_pay.html', context)


def handle_mpesa_payment(request, org_slug, invoice, amount):
    """Handle M-Pesa STK Push payment"""
    phone_number = request.POST.get('phone_number', invoice.lease.tenant.phone)
    
    if not phone_number:
        messages.error(request, 'Phone number is required for M-Pesa payment.')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)
    
    try:
        # Initiate STK Push
        mpesa_service = MpesaService(request.org)
        transaction_obj, response_data = mpesa_service.initiate_stk_push(
            phone_number=phone_number,
            amount=amount,
            invoice=invoice
        )
        
        messages.success(
            request,
            f'Payment request sent to {phone_number}. Please enter your M-Pesa PIN to complete the payment.'
        )
        return redirect('payment_status', org_slug=org_slug, transaction_id=transaction_obj.id)
        
    except MpesaAPIException as e:
        messages.error(request, f'M-Pesa payment failed: {str(e)}')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)
    except Exception as e:
        messages.error(request, f'An error occurred: {str(e)}')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)


def handle_cash_payment(request, org_slug, invoice, amount):
    """Handle cash payment - simple and direct"""
    try:
        # Create payment record
        payment = Payment.objects.create(
            organization=request.org,
            invoice=invoice,
            amount=amount,
            method='CASH',
            date=date.today(),
            reference=f'CASH-{invoice.number}-{date.today().strftime("%Y%m%d")}',
            status='COMPLETED'
        )
        
        # Update invoice
        invoice.amount_paid += amount
        if invoice.amount_paid >= invoice.amount_due:
            invoice.is_paid = True
        invoice.save()
        
        messages.success(
            request,
            f'Cash payment of KES {amount:,.2f} recorded successfully! '
            f'Remaining balance: KES {invoice.balance:,.2f}'
        )
        return redirect('invoice_detail', org_slug=org_slug, pk=invoice.pk)
        
    except Exception as e:
        messages.error(request, f'Error recording cash payment: {str(e)}')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)


def handle_other_payment(request, org_slug, invoice, amount, method):
    """Handle bank transfer, cheque, or card payments"""
    reference = request.POST.get('reference', '')
    
    if not reference:
        messages.error(request, f'{method.replace("_", " ").title()} reference number is required.')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)
    
    try:
        # Create payment record
        payment = Payment.objects.create(
            organization=request.org,
            invoice=invoice,
            amount=amount,
            method=method,
            date=date.today(),
            reference=reference,
            status='COMPLETED'
        )
        
        # Update invoice
        invoice.amount_paid += amount
        if invoice.amount_paid >= invoice.amount_due:
            invoice.is_paid = True
        invoice.save()
        
        method_name = method.replace('_', ' ').title()
        messages.success(
            request,
            f'{method_name} payment of KES {amount:,.2f} recorded successfully! '
            f'Reference: {reference}. Remaining balance: KES {invoice.balance:,.2f}'
        )
        return redirect('invoice_detail', org_slug=org_slug, pk=invoice.pk)
        
    except Exception as e:
        messages.error(request, f'Error recording payment: {str(e)}')
        return redirect('quick_pay_invoice', org_slug=org_slug, invoice_id=invoice.pk)


@login_required
def get_invoice_payment_info(request, org_slug, invoice_id):
    """AJAX endpoint to get invoice payment information"""
    invoice = get_object_or_404(Invoice, pk=invoice_id, organization=request.org)
    
    data = {
        'invoice_number': invoice.number,
        'tenant_name': f"{invoice.lease.tenant.first_name} {invoice.lease.tenant.last_name}",
        'tenant_phone': invoice.lease.tenant.phone or '',
        'unit': str(invoice.lease.unit),
        'amount_due': str(invoice.amount_due),
        'amount_paid': str(invoice.amount_paid),
        'balance': str(invoice.balance),
        'due_date': invoice.due_date.strftime('%Y-%m-%d'),
    }
    
    return JsonResponse(data)

