from django import template
from decimal import Decimal

register = template.Library()


@register.filter
def subtract(value, arg):
    """Subtract arg from value"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def percentage_change(current, previous):
    """Calculate percentage change between two values"""
    try:
        current = float(current)
        previous = float(previous)
        if previous == 0:
            return 0
        return round(((current - previous) / previous) * 100, 1)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0


@register.filter
def sum_list(value_list, attribute=None):
    """Sum a list of values or a specific attribute of objects in a list"""
    try:
        if attribute:
            return sum(getattr(item, attribute, 0) for item in value_list)
        else:
            return sum(float(item) for item in value_list)
    except (ValueError, TypeError, AttributeError):
        return 0


@register.filter
def average_list(value_list, attribute=None):
    """Calculate average of a list of values or a specific attribute"""
    try:
        if attribute:
            values = [getattr(item, attribute, 0) for item in value_list]
        else:
            values = [float(item) for item in value_list]
        
        if not values:
            return 0
        return sum(values) / len(values)
    except (ValueError, TypeError, AttributeError, ZeroDivisionError):
        return 0


@register.filter
def currency_format(value):
    """Format a number as currency"""
    try:
        return f"KES {float(value):,.2f}"
    except (ValueError, TypeError):
        return "KES 0.00"


@register.filter
def compact_currency(value):
    """Format large numbers in compact form (K, M, B)"""
    try:
        value = float(value)
        if value >= 1_000_000_000:
            return f"KES {value/1_000_000_000:.1f}B"
        elif value >= 1_000_000:
            return f"KES {value/1_000_000:.1f}M"
        elif value >= 1_000:
            return f"KES {value/1_000:.1f}K"
        else:
            return f"KES {value:.0f}"
    except (ValueError, TypeError):
        return "KES 0"


@register.filter
def growth_indicator(current, previous):
    """Return growth indicator with icon and percentage"""
    try:
        current = float(current)
        previous = float(previous)
        
        if previous == 0:
            return '<span class="badge badge-secondary">N/A</span>'
        
        change = ((current - previous) / previous) * 100
        
        if change > 0:
            return f'<span class="badge badge-success"><i class="fas fa-arrow-up"></i> {change:.1f}%</span>'
        elif change < 0:
            return f'<span class="badge badge-danger"><i class="fas fa-arrow-down"></i> {abs(change):.1f}%</span>'
        else:
            return '<span class="badge badge-secondary">0%</span>'
    except (ValueError, TypeError, ZeroDivisionError):
        return '<span class="badge badge-secondary">N/A</span>'


@register.simple_tag
def cash_flow_total(cash_flow_data):
    """Calculate total cash flow from data"""
    try:
        total = sum(item.get('inflow', 0) for item in cash_flow_data)
        return compact_currency(total)
    except (AttributeError, TypeError):
        return "KES 0"


@register.simple_tag
def cash_flow_average(cash_flow_data):
    """Calculate average cash flow from data"""
    try:
        if not cash_flow_data:
            return "KES 0"
        total = sum(item.get('inflow', 0) for item in cash_flow_data)
        average = total / len(cash_flow_data)
        return compact_currency(average)
    except (AttributeError, TypeError, ZeroDivisionError):
        return "KES 0"


@register.simple_tag
def growth_trend(cash_flow_data):
    """Calculate overall growth trend"""
    try:
        if len(cash_flow_data) < 2:
            return "N/A"
        
        first = cash_flow_data[0].get('inflow', 0)
        last = cash_flow_data[-1].get('inflow', 0)
        
        if first == 0:
            return "N/A"
        
        change = ((last - first) / first) * 100
        
        if change > 0:
            return f"+{change:.1f}%"
        elif change < 0:
            return f"{change:.1f}%"
        else:
            return "0%"
    except (AttributeError, TypeError, ZeroDivisionError, IndexError):
        return "N/A"
